from django.urls import reverse_lazy
from django.views.generic import C<PERSON>View, UpdateView, ListView, DeleteView, FormView
from django.contrib.auth.forms import PasswordChangeForm
from .forms import *
from django.contrib.messages.views import SuccessMessageMixin
from django.contrib.auth.views import PasswordChangeView, LoginView
from django.shortcuts import render, redirect
from django.contrib.auth.mixins import LoginRequiredMixin

# Create your views here.
class SignUpView(LoginRequiredMixin, CreateView):
    form_class = CustomUserCreationForm
    success_url = reverse_lazy('login')
    template_name = 'registration/signup.html'

class UpdateView(LoginRequiredMixin, UpdateView):
    form_class = CustomUserChangeForm
    success_url = reverse_lazy('password_change_done')
    template_name = 'registration/password_change_form.html'

class UsersView(LoginRequiredMixin, ListView):
    model = CustomUser
    template_name = 'registration/users.html'
    paginate_by = 20

class UsersUpdate(UpdateView):
    model = CustomUser
    template_name = 'registration/updateuser.html'
    form_class = CustomUserChangeForm
    success_url = reverse_lazy('users')

class UsersUpdateDone():
    template_name = 'registration/user_update_complete.html'

class UsersDelete(LoginRequiredMixin,DeleteView):
    model = CustomUser
    template_name = 'registration/user_delete.html'
    success_url = reverse_lazy('users')


class PasswordChange(SuccessMessageMixin, PasswordChangeView):
    form_class = PasswordChangeForm
    template_name = 'registration/password_change_form.html'
    success_url = 'changepassdone'
#    success_message = 'Ваш пароль успешно изменен!!!'
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'Изменение пароля на сайте'
        return context
        #
        # def get_success_url(self):
        #  return reverse_lazy('profile_detail', kwargs={'slug': self.request.user.profile.slug})

def ChangePasswordDone(request):
    return render(request,'registration/password_change_done.html')

from django.contrib.auth.views import LoginView as BaseLoginView

class CustomLoginView(BaseLoginView):
    template_name = 'registration/login.html' # Укажите путь к вашему шаблону входа
    print('I Am Login')
    def get_success_url(self):
        user = self.request.user
        print(user)
        # Проверяем, что пользователь аутентифицирован и имеет атрибут user_previlege
        if user.is_authenticated and hasattr(user, 'privilege_id'):
            privilege = getattr(user, 'privilege_id', None) # Безопасно получаем значение
            print(privilege)
            if privilege is not None:
                if 1 <= privilege <= 4:
                    # Укажите имя URL-шаблона для привилегий 1-4
                    return reverse_lazy('bsview')
                elif privilege == 5:
                    # Укажите имя URL-шаблона для привилегии 5
                    return reverse_lazy('mapview2')

        # URL по умолчанию, если условие не выполнено или нет user_previlege
        # Можно использовать LOGIN_REDIRECT_URL из settings.py
        # Или указать конкретный URL
        return reverse_lazy('login') # Или settings.LOGIN_REDIRECT_URL
