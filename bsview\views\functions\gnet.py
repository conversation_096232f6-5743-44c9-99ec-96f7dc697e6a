import csv
import os
from pathlib import Path
from .myfunctions import getAzimuth
from django.conf import settings

def cellCreate(bss,files):
    path2g = settings.BASE_DIR / "media" / "2G"
    path3g = settings.BASE_DIR / "media" / "3G"
    path4g = settings.BASE_DIR / "media" / "4G"
    cell = "CELLNAME\tLAT\tLONG\tMCC\tMNC\tLAC\tNODE\tCELLID\tAZIMUTH\tTECH\tPSC\tARFCN\tLAYER\n"
    filename2G = os.path.join(path2g, files.file2g)
    filename3G = os.path.join(path3g, files.file3g)
    filename4G = os.path.join(path4g, files.file4g)
    # print(filename2G, filename3G, filename4G)
    with open(filename2G, mode='rb') as file2G:
        decoded_file = file2G.read().decode('latin-1').splitlines()
        reader = csv.reader(decoded_file, delimiter=';')
        rowsFile2g=[]
        for row in reader:
            rowsFile2g.append(row)

    with open(filename3G, mode='rb') as file3G:
        decoded_file = file3G.read().decode('latin-1').splitlines()
        reader = csv.reader(decoded_file, delimiter=';')
        rowsFile3g=[]
        for row in reader:
            rowsFile3g.append(row)

    with open(filename4G, mode='rb') as file4G:
        decoded_file = file4G.read().decode('latin-1').splitlines()
        reader = csv.reader(decoded_file, delimiter=';')
        rowsFile4g=[]
        for row in reader:
            rowsFile4g.append(row)

    for bs in bss:
        for row in rowsFile2g:
            Azimuth=""
            if bs.bsnum == row[2]:
                if row[10]=="GSM900":
                    Azimuth=bs.gsm900
                elif row[10]=="DCS1800":
                    Azimuth = bs.gsm1800
                #cell = "CELLNAME            LAT          LONG	       MCC	      MNC	    LAC	         NODE	     CELLID	   AZIMUTH	TECH	PSC	ARFCN	LAYER\n"
                cell = cell + f"{row[5]}\t{bs.lat}\t{bs.lon}\t434\t04\t{row[6]}\t\t{row[7]}\t{getAzimuth(row[5],Azimuth)}\t2G\t\t{row[16]}\t3\n"

        for row in rowsFile3g:
            Azimuth=""
            if bs.bsnum == row[2]:
                if row[11]=="3082":
                    Azimuth=bs.umts900
                else:
                    Azimuth=bs.umts2100
                #cell = "CELLNAME            LAT          LONG	       MCC	      MNC	    LAC	         NODE	     CELLID	   AZIMUTH	TECH	PSC	ARFCN	LAYER\n"
                cell = cell + f"{row[8]}\t{bs.lat}\t{bs.lon}\t434\t04\t{row[15]}\t{row[2]}\t{row[5]}\t{getAzimuth(row[8],Azimuth)}\t3G\t\t{row[11]}\t7\n"

        for row in rowsFile4g:
            Azimuth=""
            if bs.bsnum == row[2]:
                if row[8]=="18" or row[8]=="5" or row[8]=="8":
                    Azimuth=bs.lte800
                elif row[8]=="3":
                    Azimuth = bs.lte1800
                elif row[8]=="7" or row[8]=="38" or row[8]=="41":
                    Azimuth = bs.lte2600
                elif row[8]=="1":
                    Azimuth = bs.lte2100
                elif row[8]=="40":
                    Azimuth = bs.lte2300
                #cell = "CELLNAME            LAT          LONG	       MCC	      MNC	    LAC	         NODE	     CELLID	   AZIMUTH	TECH	PSC	ARFCN	LAYER\n"
                cell = cell + f"{row[6]}\t{bs.lat}\t{bs.lon}\t434\t04\t{row[7]}\t{row[2]}\t{row[5]}\t{getAzimuth(row[5],Azimuth)}\t4G\t\t{row[10]}\t9\n"


    return cell


def getAzimuth(cellname, Az):
    try:
        cellnumber = cellname[cellname.rfind("-") + 1:]
        if cellnumber.isdigit() and Az.split('-')[0].isdigit():
            last_digit = int(cellnumber) % 10  # Получаем последнюю цифру числа
            if last_digit in (1, 5, 8):
               return Az.split('-')[0]
            elif last_digit in (2, 6, 9):
               return Az.split('-')[1]
            elif last_digit in (3, 7, 0):
               return Az.split('-')[2]
            # elif last_digit == 4:
            #     return 4
        else:
            return "0"
    except:
        return "0"

