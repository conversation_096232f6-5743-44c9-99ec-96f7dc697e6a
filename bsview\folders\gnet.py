import csv
import os
from pathlib import Path
from .myfunctions import getAzimuth
from django.conf import settings

def cellCreate(bss,files):
    path2g = settings.BASE_DIR / "media" / "2G"
    cell = "CELLNAME   LAT LONG	MCC	MNC	LAC	NODE	CELLID	AZIMUTH	TECH	PSC	ARFCN	LAYER\n"
    filename2G = os.path.join(path2g, files.file2g)
    print(filename2G)
    with open(filename2G, mode='rb') as file2G:
        decoded_file = file2G.read().decode('latin-1').splitlines()
        reader = csv.reader(decoded_file, delimiter=';')
        rowsFile=[]
        for row in reader:
            rowsFile.append(row)
    errorcell =[]
    for bs in bss:
        for row in rowsFile:
            if bs.bsnum == row[2]:
                #cell = "CELLNAME   LAT LONG	MCC	MNC	LAC	NODE	CELLID	AZIMUTH	TECH	PSC	ARFCN	LAYER\n"
                cell = cell + f"{row[5]}    {bs.lat}    {bs.lon}    {row[12]}   {row[6]}    {row[2]}    {row[7]}\n"

    print(cell)


    # file_data = bts2Create(bss, gen, bsName, bsnomer, cellName, channel, bsic, cid, lac)
    # if type(file_data) != list:
    #     context = {'region': reg,
    #                'allError': zip(file_data['errbsname'], file_data['errbs_dp'], file_data['errbs_pk'])}
    #     return render(request, 'btscreate.html', context)
    # else:
    #     response = HttpResponse(file_data, content_type='application/text charset=utf-8')
    #     response[
    #         'Content-Disposition'] = f'attachment; filename= Bts_file-2G-{dt.datetime.now().strftime("%d-%m-%Y-%H-%M-%S")}.nbf'
    #     return response

