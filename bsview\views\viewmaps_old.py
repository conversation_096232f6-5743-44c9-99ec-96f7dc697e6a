import json  # Понадобится для передачи данных в шаблон безопасно
from bsview.views.imports_file import *
from django.shortcuts import render
from rest_framework import viewsets, status
from rest_framework.decorators import api_view
from rest_framework.response import Response
# from functions.serializers import RegionSerializer, AreaSerializer, BaseStationSerializer, AlarmSerializer
from django.templatetags.static import static
from django.views.generic import TemplateView
from django.core.serializers import serialize
from django.db.models import F, Value, CharField
from django.db.models.functions import Concat
# Керакли моделларни импорт қилинг (ўз моделларингизга қараб)
# Мисол учун:
from bsview.models import RegionUzb, AreaUzb, BsBeeline, Current_Alarms
from datetime import datetime


# Frontend uchun asosiy view
def mapindex(request):
    """Asosiy sahi<PERSON>i ko'rsatish"""
    return render(request, 'map3.html')


# API Views
@api_view(['GET'])
def get_all_base_stations(request):
    """Barcha baza stantsiyalarini olish"""
    # Limit parametrini olish
    limit = request.query_params.get('limit')
    offset = request.query_params.get('offset', 0)

    # Barcha baza stantsiyalarini olish
    queryset = BsBeeline.objects.all()

    # Agar limit berilgan bo'lsa, faqat shu miqdordagi ma'lumotlarni olish
    if limit:
        try:
            limit = int(limit)
            offset = int(offset)
            queryset = queryset[offset:offset + limit]
        except ValueError:
            pass

    serializer = BaseStationSerializer(queryset, many=True)
    return Response(serializer.data)


@api_view(['GET'])
def get_base_stations_by_region(request, region_id):
    """Viloyat bo'yicha baza stantsiyalarini olish"""
    # BsBeeline ma'lumotlarini olish
    base_stations = BsBeeline.objects.filter(region_id=region_id).values(
        'id', 'bsname', 'bsnum', 'lat', 'lon', 'area_id', 'region_id'
    )

    # Ma'lumotlarni formatlash
    formatted_stations = []
    for bs in base_stations:
        # Koordinatalarni to'g'rilash
        if bs.get('lat'):
            bs['lat'] = bs['lat'].replace(',', '.')
        if bs.get('lon'):
            bs['lon'] = bs['lon'].replace(',', '.')

        # Viloyat va tuman nomlarini olish
        region_name = None
        if bs.get('region_id'):
            region = RegionUzb.objects.filter(id=bs['region_id']).first()
            if region:
                region_name = region.name

        area_name = None
        if bs.get('area_id'):
            area = AreaUzb.objects.filter(id=bs['area_id']).first()
            if area:
                area_name = area.name

        # Avaria ma'lumotlarini tekshirish
        status = False  # Default: online
        calc_time = None

        # Avariyadagi BS larni tekshirish
        alarm = Current_Alarms.objects.filter(bsname__contains=bs['bsnum']).first()
        if alarm:
            status = True  # Avaria holati: offline
            # Davomiylikni olish
            calc_time = str(alarm.calctime) if hasattr(alarm, 'calctime') else None

        # To'liq ma'lumotlarni qo'shish
        formatted_stations.append({
            'id': bs['id'],
            'name': bs['bsname'],
            'bsName': bs['bsname'],
            'lat': bs['lat'],
            'lon': bs['lon'],
            'status': status,
            'calcTime': calc_time,  # Avaria davomiyligi
            'region_id': bs['region_id'],
            'region_name': region_name,
            'area_id': bs['area_id'],
            'area_name': area_name
        })

    return Response(formatted_stations)


@api_view(['GET'])
def get_base_stations_by_area(request, area_id):
    """Tuman bo'yicha baza stantsiyalarini olish"""
    # BsBeeline ma'lumotlarini olish
    base_stations = BsBeeline.objects.filter(area_id=area_id).values(
        'id', 'bsname', 'bsnum', 'lat', 'lon', 'area_id', 'region_id'
    )

    # Ma'lumotlarni formatlash
    formatted_stations = []
    for bs in base_stations:
        # Koordinatalarni to'g'rilash
        if bs.get('lat'):
            bs['lat'] = bs['lat'].replace(',', '.')
        if bs.get('lon'):
            bs['lon'] = bs['lon'].replace(',', '.')

        # Viloyat va tuman nomlarini olish
        region_name = None
        if bs.get('region_id'):
            region = RegionUzb.objects.filter(id=bs['region_id']).first()
            if region:
                region_name = region.name

        area_name = None
        if bs.get('area_id'):
            area = AreaUzb.objects.filter(id=bs['area_id']).first()
            if area:
                area_name = area.name

        # Avaria ma'lumotlarini tekshirish
        status = False  # Default: online
        calc_time = None

        # Avariyadagi BS larni tekshirish
        alarm = Current_Alarms.objects.filter(bsname__contains=bs['bsnum']).first()
        if alarm:
            status = True  # Avaria holati: offline
            # Davomiylikni olish
            calc_time = str(alarm.calctime) if hasattr(alarm, 'calctime') else None

        # To'liq ma'lumotlarni qo'shish
        formatted_stations.append({
            'id': bs['id'],
            'name': bs['bsname'],
            'bsName': bs['bsname'],
            'lat': bs['lat'],
            'lon': bs['lon'],
            'status': status,
            'calcTime': calc_time,  # Avaria davomiyligi
            'region_id': bs['region_id'],
            'region_name': region_name,
            'area_id': bs['area_id'],
            'area_name': area_name
        })

    return Response(formatted_stations)


@api_view(['GET'])
def get_base_stations_by_status(request, status_value):
    """Status bo'yicha baza stantsiyalarini olish"""
    if status_value not in ['online', 'offline']:
        return Response(
            {"error": "Invalid status. Must be 'online' or 'offline'"},
            status=status.HTTP_400_BAD_REQUEST
        )

    # Barcha stantsiyalarni olish
    base_stations = BsBeeline.objects.all()
    serializer = BaseStationSerializer(base_stations, many=True)

    # Status bo'yicha filtrlash
    filtered_stations = [station for station in serializer.data if station['status'] == status_value]
    return Response(filtered_stations)


@api_view(['GET'])
def get_all_regions(request):
    """Barcha viloyatlarni olish"""
    regions = RegionUzb.objects.all()
    serializer = RegionSerializer(regions, many=True)
    return Response(serializer.data)


@api_view(['GET'])
def get_all_areas(request):
    """Barcha tumanlarni olish"""
    areas = AreaUzb.objects.all()
    serializer = AreaSerializer(areas, many=True)
    return Response(serializer.data)


@api_view(['GET'])
def get_areas_by_region(request, region_id):
    """Viloyat bo'yicha tumanlarni olish"""
    areas = AreaUzb.objects.filter(region_id=region_id)
    serializer = AreaSerializer(areas, many=True)
    return Response(serializer.data)


@api_view(['GET'])
def get_all_alarms(request):
    """Barcha avariyalarni olish"""
    alarms = Current_Alarms.objects.all()
    serializer = AlarmSerializer(alarms, many=True)
    return Response(serializer.data)


@login_required()
def map_view(request):
    # MapView класси орқали маълумотларни олиш
    mapview = MapView()
    context_data = mapview.get_context_data()

    # БС координаталарини олиш
    bs_coordinates = json.loads(context_data['bs_coordinates_json'])

    # Авария статусини олиш
    accident_data = json.loads(context_data['accident_data_json'])

    # Регионлар маълумотларини олиш
    regions_data = json.loads(context_data['regions_data_json'])
    # Регионлар маълумотлари учун ID бўйича луғат яратиш
    region_dict = {}
    for region in regions_data:
        region_dict[region['id']] = region['name']

    # Туманлар маълумотларини олиш
    areas_data = json.loads(context_data['areas_data_json'])
    # Туманлар маълумотлари учун ID бўйича луғат яратиш
    area_dict = {}
    for area in areas_data:
        area_dict[area['id']] = area['name']

    # Авария статусини БС координаталарига қўшиш
    points_for_template = []

    # Авария бўлган БС лар рўйхатини тузиш
    accident_stations = {}
    for accident in accident_data:
        accident_stations[accident['base_station_id']] = accident

    # Барча БС лар учун маълумотларни тайёрлаш
    for bs in bs_coordinates:
        status = False  # Стандарт ҳолат - онлайн
        calcTime = None
        typeG = None

        # Агар БС аварияда бўлса, статусни ўзгартириш
        if bs['id'] in accident_stations:
            status = True  # Авария ҳолати - оффлайн
            calcTime = accident_stations[bs['id']].get('calctime')
            typeG = accident_stations[bs['id']].get('typeG')  # Технология типини олиш

        # Регион номини олиш
        region_name = None
        if bs.get('region_id') and bs['region_id'] in region_dict:
            region_name = region_dict[bs['region_id']]

        # Туман номини олиш
        area_name = None
        if bs.get('area_id') and bs['area_id'] in area_dict:
            area_name = area_dict[bs['area_id']]

        points_for_template.append({
            'name': bs['bsname'],
            'bsName': bs['bsname'],
            'lat': bs['lat'],
            'lon': bs['lon'],
            'status': status,
            'calcTime': calcTime,
            'typeG': typeG,  # Технология типини қўшиш
            'region_id': bs['region_id'],
            'region_name': region_name,
            'area_id': bs['area_id'],
            'area_name': area_name
        })

    context = {
        'points_data_json': points_for_template
    }
    return render(request, 'map.html', context)


class MapView(TemplateView):
    template_name = 'map.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Барча регионларни олиш
        regions_data = self.get_regions_data()

        # Барча ареаларни олиш
        areas_data = self.get_areas_data()

        # БС координаталарини олиш
        bs_coordinates = self.get_bs_coordinates()

        # Авария статуси ва давомийлигини олиш
        accident_data = self.get_accident_data()

        # Барча маълумотларни контекстга қўшиш
        context['regions_data_json'] = json.dumps(regions_data)
        context['areas_data_json'] = json.dumps(areas_data)
        context['bs_coordinates_json'] = json.dumps(bs_coordinates)
        context['accident_data_json'] = json.dumps(accident_data)

        return context

    def get_regions_data(self):
        # Регионлар маълумотини олиш
        regions = RegionUzb.objects.all().values('id', 'name')
        return list(regions)

    def get_areas_data(self):
        # Ареалар маълумотини олиш
        areas = AreaUzb.objects.all().values('id', 'name', 'region_id')
        return list(areas)

    def get_bs_coordinates(self):
        # БС координаталарини олиш
        base_stations = BsBeeline.objects.all().values(
            'id', 'bsname', 'lat', 'lon', 'area_id', 'region_id'
        )
        # Координаталарни формат қилиш (запятая ўрнига нуқта)
        for bs in base_stations:
            if bs.get('lat'):
                bs['lat'] = bs['lat'].replace(',', '.')
            if bs.get('lon'):
                bs['lon'] = bs['lon'].replace(',', '.')

        return list(base_stations)

    def get_accident_data(self):
        # Авария статуси ва давомийлигини олиш
        accidents = Current_Alarms.objects.all().values(
            'id',
            'bsname',
            'appeartime'
        )

        # Маълумотларни мос форматга ўзгартириш
        formatted_accidents = []
        # Бир БС да бир нечта технология авария ҳолатида бўлса, уларни бирлаштириш учун луғат
        bs_technologies = {}

        for accident in accidents:
            bs_number = getNumberBS(accident['bsname']) if 'bsname' in accident else None
            if bs_number:
                # БС рақами бўйича мос БС ни топиш
                matching_bs = BsBeeline.objects.filter(bsnum=bs_number).first()
                if matching_bs:
                    # Avaria davomiyligini olish
                    current_alarm = Current_Alarms.objects.get(id=accident['id'])
                    calc_time = str(current_alarm.calctime) if hasattr(current_alarm, 'calctime') else None

                    # Технология турини аниқлаш
                    if current_alarm.bscrnc.startswith("BSC"):
                        typeG = "2G"
                    elif current_alarm.bscrnc.startswith("LTE"):
                        typeG = "4G"
                    elif current_alarm.bscrnc.startswith("RNC"):
                        typeG = "3G"

                    # БС идентификатори
                    bs_id = matching_bs.id

                    # Агар бу БС аввал учраган бўлса, технологияларни бирлаштирамиз
                    if bs_id in bs_technologies:
                        # Агар бу технология аввал қўшилмаган бўлса, қўшамиз
                        if typeG not in bs_technologies[bs_id]['typeG']:
                            bs_technologies[bs_id]['typeG'] += "/" + typeG
                    else:
                        # Янги БС учун маълумотларни сақлаш
                        bs_technologies[bs_id] = {
                            'id': accident['id'],
                            'base_station_id': bs_id,
                            'status': 'offline',  # Авария ҳолатида статус offline
                            'calctime': calc_time,  # Avaria davomiyligini saqlash
                            'typeG': typeG,  # Texnologiyasi
                        }

        # Барча БС лар учун маълумотларни форматлаш
        formatted_accidents = list(bs_technologies.values())

        return formatted_accidents
