def bts2Create(bss, gen, bsName, bsnomer, cellName, channel, bsic, cid, lac):
    if gen == 2:
        bts = [f'SYSTEM;SITE;LAT;LON;CELL;CH;BSIC;CID;LAC;DIR\n']
    elif gen == 3:
        bts = [f'SYSTEM;SITE;LAT;LON;CELL;CH;BSIC;CID;LAC;DIR\n']
    elif gen == 4:
        bts = [f'SYSTEM;SITE;LAT;LON;CELL;CH;BSIC;CID;LAC;DIR\n']


    err = 0
    errbs_dp900 = ''
    errbs_dp1800 = ''
    errbsname=[]
    errbs_pk=[]
    errbs_dp=[]

    for bs in bss:
        num = 0
        errbs_dp900=''
        errbs_dp1800=''
        errbs_dpbi1800=''

        for cellid in cid:
            sec=''
            Erroriko = False

            # print(bs.bsname)
            if bs.bsnum == bsnomer[num]:
                #sec900 = bs.gsm900.split('-')
                #sec1800 = bs.gsm1800.split('-')
                if cellid[-1]=='1':
                    try:
                        sec=bs.gsm1800.split('-')[0]
                    except:
                        Erroriko=True
                        errbs_dp1800 = 'GSM-1800'
                elif cellid[-1]=='2':
                    try:
                        sec=bs.gsm1800.split('-')[1]
                    except:
                        # return f"Ошибка в данных БС {bs.bsname} на GSM-1800"
                        Erroriko=True
                        errbs_dp1800 = 'GSM-1800'
                elif cellid[-1]=='3':
                    try:
                        sec = bs.gsm1800.split('-')[2]
                    except:
                        Erroriko=True
                        errbs_dp1800 = 'GSM-1800'
                elif cellid[-1]=='4':
                    try:
                        sec=bs.gsm1800.split('-')[3]
                    except:
                        Erroriko=True
                        errbs_dp1800 = 'GSM-1800'
                elif cellid[-1]=='5':
                    try:
                        sec = bs.gsm900.split('-')[0]
                    except:
                        Erroriko=True
                        errbs_dp900 = 'GSM-900'
                elif cellid[-1]=='6':
                    try:
                        sec = bs.gsm900.split('-')[1]
                    except:
                        Erroriko=True
                        errbs_dp900 = 'GSM-900'
                elif cellid[-1]=='7':
                    try:
                        sec = bs.gsm900.split('-')[2]
                    except:
                        Erroriko=True
                        errbs_dp900 = 'GSM-900'
                elif cellid[-1]=='8':
                    try:
                        sec = bs.gsmbi1800.split('-')[0]
                    except:
                        Erroriko=True
                        errbs_dpbi1800 = 'GSM-1800-Bi'
                elif cellid[-1] == '9':
                    try:
                        sec = bs.gsmbi1800.split('-')[1]
                    except:
                        Erroriko = True
                        errbs_dpbi1800 = 'GSM-1800-Bi'
                elif cellid[-1] == '0':
                    try:
                        sec = bs.gsmbi1800.split('-')[2]
                    except:
                        Erroriko = True
                        errbs_dpbi1800 = 'GSM-1800-Bi'

                    #print(sec)
                if Erroriko==True:
                    err +=1
                    errbsname.append(cellid)
                    errbs_pk.append(bs.id)
                    errbs_dp.append(errbs_dp900 + ' | ' + errbs_dp1800)
                else:
                    bts.append(f'GSM;{bs.bsname};{bs.lat.replace(",",".")};{bs.lon.replace(",",".")};{cellName[num]};{channel[num]};{bsic[num]};{cid[num]};{lac[num]};{sec} \n')
            num = num + 1
                # for sec in range(len(bs.gsm1800.split('-'))):
                #     bts.append(f'GSM;{bs.bsname};{bs.lat.replace(",",".").replace(",",".")};{bs.lon};{cellName[num-1]};{channel[num-1]};{bsic[num-1]};{cid[num-1]};{lac[num-1]};{bs.gsm1800.split("-")[sec]} \n')
    # bts.append('</Folder></Document>\n')

    if err > 0:
        # print(f'{errbsname} {errbs_pk} {errbs_dp}')
        errors = {'errbsname': errbsname, 'errbs_pk': errbs_pk , 'errbs_dp': errbs_dp}
        return errors
    else:

        return bts
