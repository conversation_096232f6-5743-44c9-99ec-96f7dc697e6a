{% extends 'base.html' %}
{% load static %}

{% block content %}

<div class="jumbotron">
    <p>
</div>
<body>
<h1>На стадии разработки !!!</h1>
<form action="{% url 'getfromnri' %}" method="post" id="getnriform" enctype="multipart/form-data">
    {% csrf_token %}

    <div class="container card ">
        <div class="row ">
            <div class="col col-3">
                <p>
                    <b>Выберите область:</b>
                <p>
                <div class="card-header w-100 p-3">
                    <select class="form-select" multiple aria-label="multiple select example" name="regiono" id="reg" style="height: 200px;">
                        {% for reg in region %}
                        {% if reg.id == user.region_id %}
                        <option selected value="{{ reg.id }}">{{reg.name}}</option>
                        {% else %}
                        {% if user.privilege_id == 1 %}
                        <option value="{{ reg.id }}">{{reg.name}}</option>
                        {% else %}
                        <option value="{{ reg.id }}" disabled>{{reg.name}}</option>
                        {% endif %}

                        {% endif %}
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="col ">
                <div id="lteOptions">
                    <label for="lteSelect">Выберите Band:</label>
                    <select id="lteSelect" class="form-select" name="band">
                        <option value="1800">LTE 1800</option>
                        <option value="2600">LTE 2600</option>
                    </select>
                </div>
                <br><Br>
                    <button class="btn btn-primary" type="submit" id="knopka">Экспортировать данные из NRI</button>
                    <a href="{% url 'bsview' %}" class="btn btn-primary">Отмена</a>
                <p>
            </div>
        </div>
        <br>
    </div>
    <br>
</form>
<br>


</body>
{% endblock %}