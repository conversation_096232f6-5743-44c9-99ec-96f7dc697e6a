<!-- Modal -->
{% load crispy_forms_tags %}
<div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h1 class="modal-title fs-5" id="exampleModalLabel">Изменение данных</h1>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form action="" method="post" id="editForm" data-areas-url="{% url 'ajax_load_areas' %}" novalidate>
                    {% csrf_token %}
                    <table class="table table-stripped">
                        <TR class="bg-light">
                            <TD>
                                <div class="row">
                                    <div class="col">
                                        <label class="form-control bg-light" for="form.bsname.id_for_label">
                                        {{form.bsname | as_crispy_field }}
                                        </label>
                                    </div>
                                    <div class="col">
                                        <label class="form-control bg-light" for="form.bsnum.id_for_label">
                                            {{form.bsnum | as_crispy_field }}
                                        </label>
                                    </div>
                                    <div class="col">
                                        <label class="form-control bg-light" for="form.besecnum.id_for_label">
                                            {{form.besecnum | as_crispy_field }}
                                        </label>
                                    </div>
                                </div>
                            </TD>
                        </TR>
                        <TR class="bg-light">
                            <TD>
                                <div class="row">
                                    <div class="col">
                                        <label class="form-control bg-light" for="form.lat.id_for_label"
                                               aria-label="{{ form.lat.label }}">
                                            {{form.lat | as_crispy_field }}
                                        </label>
                                    </div>
                                    <div class="col">
                                        <label class="form-control bg-light" for="form.lon.id_for_label"
                                               aria-label="{{ form.lon.label }}">
                                            {{form.lon | as_crispy_field }}
                                        </label>
                                    </div>
                                    <div class="col">
                                        <label class="form-control  bg-light" for="form.modem.id_for_label"
                                               aria-label="{{ form.modem.label }}">
                                            {{form.modem | as_crispy_field }}
                                        </label>
                                    </div>
                                </div>
                            </TD>
                        </TR>
                        <TR class="bg-light">
                            <TD>
                                <div class="row">
                                    <div class="col">
                                        <label class="form-control bg-light" for="form.region.id_for_label"
                                               aria-label="{{ form.region.label }}">
                                            {{form.region | as_crispy_field }}
                                        </label>
                                    </div>
                                    <div class="col">
                                        <label class="form-control bg-light" for="form.area.id_for_label"
                                               aria-label="{{ form.area.label }}">
                                            {{form.area | as_crispy_field }}
                                        </label>
                                    </div>
                                </div>
                            </TD>
                        </TR>
                        <TR class="bg-light">
                            <TD>
                                <div class="row">
                                    <div class="col">
                                        <label class="form-control bg-light" for="form.address.id_for_label"
                                               aria-label="{{ form.address.label }}">
                                            {{form.address | as_crispy_field }}
                                        </label>
                                    </div>
                                </div>
                            </TD>
                        </TR>
                        <TR class="bg-light">
                            <TD>
                                <div class="row">
                                    <div class="col">
                                        <label class="form-control bg-light" for="form.gsm900.id_for_label"
                                               aria-label="{{ form.gsm900.label }}">
                                            {{form.gsm900 | as_crispy_field }}
                                        </label>
                                    </div>
                                    <div class="col">
                                        <label class="form-control bg-light" for="form.gsm1800.id_for_label"
                                               aria-label="{{ form.gsm1800.label }}">
                                            {{form.gsm1800 | as_crispy_field }}
                                        </label>
                                    </div>
                                    <div class="col">
                                        <label class="form-control bg-light" for="form.umts900.id_for_label"
                                               aria-label="{{ form.umts900.label }}">
                                            {{form.umts900 | as_crispy_field }}
                                        </label>
                                    </div>
                                    <div class="col">
                                        <label class="form-control bg-light" for="form.umts2100.id_for_label"
                                               aria-label="{{ form.umts2100.label }}">
                                            {{form.umts2100 | as_crispy_field }}
                                        </label>
                                    </div>
                                    <div class="col">
                                        <label class="form-control bg-light" for="form.umtsbesec.id_for_label"
                                               aria-label="{{ form.umtsbesec.label }}">
                                            {{form.umtsbesec | as_crispy_field }}
                                        </label>
                                    </div>
                                </div>
                            </TD>
                        </TR>
                        <TR class="bg-light">
                            <TD>
                                <div class="row">
                                    <div class="col">
                                        <label class="form-control bg-light" for="form.lte800.id_for_label"
                                               aria-label="{{ form.lte800.label }}">
                                            {{form.lte800 | as_crispy_field }}
                                        </label>
                                    </div>
                                    <div class="col">
                                        <label class="form-control bg-light" for="form.lte1800.id_for_label"
                                               aria-label="{{ form.lte1800.label }}">
                                            {{form.lte1800 | as_crispy_field }}
                                        </label>
                                    </div>
                                    <div class="col">
                                        <label class="form-control bg-light" for="form.lte2600.id_for_label"
                                               aria-label="{{ form.lte2600.label }}">
                                            {{form.lte2600 | as_crispy_field }}
                                        </label>
                                    </div>
                                    <div class="col">
                                        <label class="form-control bg-light" for="form.lte2300.id_for_label"
                                               aria-label="{{ form.lte2300.label }}">
                                            {{form.lte2300 | as_crispy_field }}
                                        </label>
                                    </div>
                                    <div class="col">
                                        <label class="form-control bg-light" for="form.lte2100.id_for_label"
                                               aria-label="{{ form.lte2100.label }}">
                                            {{form.lte2100 | as_crispy_field }}
                                        </label>
                                    </div>
                                </div>
                            </TD>
                        </TR>
                    </table>
                    <button class="btn btn-primary" type="submit">Сохранить</button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Закрыть</button>
<!--                    <input class="btn btn-primary" type="button" value="Назад" onclick="javascript:history.go(-1);">-->
                </form>


            </div>
            <div class="modal-footer">

            </div>
        </div>
    </div>
</div>
<!--End modal-->