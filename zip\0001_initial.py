# Generated by Django 4.1.5 on 2023-01-08 15:53

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Bsbeeline',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('bsname', models.Char<PERSON><PERSON>(max_length=30, verbose_name='Наименование БС')),
                ('bsnum', models.Char<PERSON>ield(max_length=6, verbose_name='Номер БС')),
                ('besecnum', models.Char<PERSON>ield(max_length=6, verbose_name='Номер Бисектор')),
                ('lat', models.Char<PERSON>ield(max_length=7, verbose_name='Долгота')),
                ('lon', models.Char<PERSON>ield(max_length=7, verbose_name='Широта')),
                ('modem', models.Cha<PERSON><PERSON><PERSON>(max_length=30, verbose_name='Номер модема(АСКУЭ)')),
                ('address', models.<PERSON><PERSON><PERSON><PERSON>(max_length=100, verbose_name='Адрес')),
                ('area', models.CharField(max_length=30, verbose_name='Район')),
                ('region', models.CharField(max_length=30, verbose_name='Область')),
                ('gsm900', models.CharField(max_length=15, verbose_name='GSM-900')),
                ('gsm1800', models.CharField(max_length=15, verbose_name='GSM-1800')),
                ('umts900', models.CharField(max_length=15, verbose_name='UMTS-900')),
                ('umts2100', models.CharField(max_length=15, verbose_name='UMTS-2100')),
                ('umtsbesec', models.CharField(max_length=15, verbose_name='UMTS-Бисектор')),
                ('lte800', models.CharField(max_length=15, verbose_name='LTE-800')),
                ('lte1800', models.CharField(max_length=15, verbose_name='LTE-1800')),
                ('lte2600', models.CharField(max_length=30, verbose_name='LTE-2600')),
                ('lte2300', models.CharField(max_length=15, verbose_name='LTE-2300')),
                ('changedate', models.DateTimeField(auto_now_add=True)),
            ],
        ),
    ]
