# Generated by Django 4.1.5 on 2023-01-15 14:24

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='RegionUzb',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(db_index=True, max_length=30)),
            ],
        ),
        migrations.CreateModel(
            name='BsBeeline',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('bsname', models.Char<PERSON>ield(max_length=30, verbose_name='Наименование БС')),
                ('bsnum', models.Char<PERSON>ield(max_length=6, verbose_name='Номер БС')),
                ('besecnum', models.Char<PERSON>ield(max_length=6, verbose_name='Номер Бисектор')),
                ('lat', models.CharField(max_length=9, verbose_name='Долгота')),
                ('lon', models.CharField(max_length=9, verbose_name='Широта')),
                ('modem', models.CharField(max_length=30, verbose_name='Номер модема(АСКУЭ)')),
                ('address', models.CharField(max_length=100, verbose_name='Адрес')),
                ('area', models.CharField(max_length=30, verbose_name='Район')),
                ('gsm900', models.CharField(max_length=15, verbose_name='GSM-900')),
                ('gsm1800', models.CharField(max_length=15, verbose_name='GSM-1800')),
                ('umts900', models.CharField(max_length=15, verbose_name='UMTS-900')),
                ('umts2100', models.CharField(max_length=15, verbose_name='UMTS-2100')),
                ('umtsbesec', models.CharField(max_length=15, verbose_name='UMTS-Бисектор')),
                ('lte800', models.CharField(max_length=15, verbose_name='LTE-800')),
                ('lte1800', models.CharField(max_length=15, verbose_name='LTE-1800')),
                ('lte2600', models.CharField(max_length=30, verbose_name='LTE-2600')),
                ('lte2300', models.CharField(max_length=15, verbose_name='LTE-2300')),
                ('lte2100', models.CharField(max_length=15, verbose_name='LTE-2100')),
                ('changedate', models.DateTimeField(auto_now_add=True)),
                ('region', models.ForeignKey(null=True, on_delete=django.db.models.deletion.PROTECT, to='bsview.regionuzb')),
            ],
        ),
    ]
