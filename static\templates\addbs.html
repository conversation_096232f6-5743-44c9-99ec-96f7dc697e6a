{% extends 'base.html' %}
{% load static %}

{% block content %}

<div class="jumbotron">
    <p>
</div>
<body>
<form action="{% url 'addbs' %}" method="post" id="addForm" data-areas-url="{% url 'ajax_load_areas' %}">
    {% csrf_token %}
    <!--    {{ form.as_p }}-->
    <table class="table table-stripped">
        <TR class="bg-light">
            <TD>
                <div class="row">
                    <div class="col">
                        <label class="form-control bg-light" for="form.bsname.id_for_label" ">
                        {{form.bsname.label}}: {{form.bsname}}
                        </label>
                    </div>
                    <div class="col">
                        <label class="form-control bg-light" for="form.bsnum.id_for_label">
                            {{form.bsnum.label}}: {{form.bsnum}}
                        </label>
                    </div>
                    <div class="col">
                        <label class="form-control bg-light" for="form.besecnum.id_for_label">
                            {{form.besecnum.label}}: {{form.besecnum}}
                        </label>
                    </div>
                </div>
            </TD>
        </TR>
        <TR class="bg-light">
            <TD>
                <div class="row">
                    <div class="col">
                        <label class="form-control bg-light" for="form.lat.id_for_label" aria-label="{{ form.lat.label }}">
                            {{form.lat.label}}: {{form.lat}}
                        </label>
                    </div>
                    <div class="col">
                        <label class="form-control bg-light" for="form.lon.id_for_label" aria-label="{{ form.lon.label }}">
                            {{form.lon.label}}: {{form.lon}}
                        </label>
                    </div>
                    <div class="col">
                        <label class="form-control  bg-light" for="form.modem.id_for_label" aria-label="{{ form.modem.label }}">
                            {{form.modem.label}}: {{form.modem}}
                        </label>
                    </div>
                </div>
            </TD>
        </TR>
        <TR class="bg-light">
            <TD>
                <div class="row">
                    <div class="col">
                        <label class="form-control bg-light" for="form.region.id_for_label" aria-label="{{ form.region.label }}">
                            {{form.region.label}}: {{form.region}}
                        </label>
                    </div>
                    <div class="col">
                        <label class="form-control bg-light" for="form.area.id_for_label" aria-label="{{ form.area.label }}">
                            {{form.area.label}}: {{form.area}}
                        </label>
                    </div>
                </div>
            </TD>
        </TR>
        <TR class="bg-light">
            <TD>
                <div class="row">
                    <div class="col">
                        <label class="form-control bg-light" for="form.address.id_for_label" aria-label="{{ form.address.label }}">
                            {{form.address.label}}: {{form.address}}
                        </label>
                    </div>
                </div>
            </TD>
        </TR>
        <TR class="bg-light">
            <TD>
                <div class="row">
                    <div class="col">
                        <label class="form-control bg-light" for="form.gsm900.id_for_label" aria-label="{{ form.gsm900.label }}">
                            {{form.gsm900.label}}: {{form.gsm900}}
                        </label>
                    </div>
                    <div class="col">
                        <label class="form-control bg-light" for="form.gsm1800.id_for_label" aria-label="{{ form.gsm1800.label }}">
                            {{form.gsm1800.label}}: {{form.gsm1800}}
                        </label>
                    </div>
                     <div class="col">
                        <label class="form-control bg-light" for="form.gsmbi1800.id_for_label" aria-label="{{ form.gsmbi1800.label }}">
                            {{form.gsmbi1800.label}}: {{form.gsmbi1800}}
                        </label>
                    </div>
                    <div class="col">
                        <label class="form-control bg-light" for="form.umts900.id_for_label" aria-label="{{ form.umts900.label }}">
                            {{form.umts900.label}}: {{form.umts900}}
                        </label>
                    </div>
                    <div class="col">
                        <label class="form-control bg-light" for="form.umts2100.id_for_label" aria-label="{{ form.umts2100.label }}">
                            {{form.umts2100.label}}: {{form.umts2100}}
                        </label>
                    </div>
                    <div class="col">
                        <label class="form-control bg-light" for="form.umtsbesec.id_for_label" aria-label="{{ form.umtsbesec.label }}">
                            {{form.umtsbesec.label}}: {{form.umtsbesec}}
                        </label>
                    </div>
                </div>
            </TD>
        </TR>
        <TR class="bg-light">
            <TD>
                <div class="row">
                    <div class="col">
                        <label class="form-control bg-light" for="form.lte800.id_for_label" aria-label="{{ form.lte800.label }}">
                            {{form.lte800.label}}: {{form.lte800}}
                        </label>
                    </div>
                    <div class="col">
                        <label class="form-control bg-light" for="form.lte1800.id_for_label" aria-label="{{ form.lte1800.label }}">
                            {{form.lte1800.label}}: {{form.lte1800}}
                        </label>
                    </div>
                    <div class="col">
                        <label class="form-control bg-light" for="form.ltebi1800.id_for_label" aria-label="{{ form.ltebi1800.label }}">
                            {{form.ltebi1800.label}}: {{form.ltebi1800}}
                        </label>
                    </div>

                    <div class="col">
                        <label class="form-control bg-light" for="form.lte2600.id_for_label" aria-label="{{ form.lte2600.label }}">
                            {{form.lte2600.label}}: {{form.lte2600}}
                        </label>
                    </div>
                    <div class="col">
                        <label class="form-control bg-light" for="form.lte2300.id_for_label" aria-label="{{ form.lte2300.label }}">
                            {{form.lte2300.label}}: {{form.lte2300}}
                        </label>
                    </div>
                    <div class="col">
                        <label class="form-control bg-light" for="form.lte2100.id_for_label" aria-label="{{ form.lte2100.label }}">
                            {{form.lte2100.label}}: {{form.lte2100}}
                        </label>
                    </div>
                </div>
            </TD>
        </TR>


    </table>

    <button class="btn btn-primary" type="submit">Добавить</button>
    <input class="btn btn-primary" type="button" value="Назад" onclick="javascript:history.go(-1);">
</form>

<!--<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>-->
<script src="{% static 'js/jquery-3.3.1.min.js' %}"></script>
  <script>
    $("#id_region").change(function () {
      var url = $("#addForm").attr("data-areas-url");
      var regionId = $(this).val();

      $.ajax({
        url: url,
        data: {
          'region': regionId
        },
        success: function (data) {
          $("#id_area").html(data);
        }
      });

    });
  </script>


</body>
{% endblock %}

