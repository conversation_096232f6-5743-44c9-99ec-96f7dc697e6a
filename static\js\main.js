// DOM elementlarini olish
const mapContainer = document.getElementById('map');
const searchInput = document.getElementById('search');
const regionSelect = document.getElementById('region');
const areaSelect = document.getElementById('area');
const statusSelect = document.getElementById('status');
const resetButton = document.getElementById('reset-filters');
const totalStationsElement = document.getElementById('total-stations');
const onlineStationsElement = document.getElementById('online-stations');
const offlineStationsElement = document.getElementById('offline-stations');

// Xarita sozlamalari
let mapSettings = {
    mapType: 'roadmap',
    zoom: 6, // Kichikroq zoom darajasi butun respublikani ko'rish uchun
    center: [41.3, 69.3] // Toshkent koordinatalari
};

// Filtrlar
let filters = {
    search: '',
    regionId: null,
    areaId: null,
    status: 'all'
};

// Ma'lumotlar
let baseStations = [];
let regions = [];
let areas = [];
let filteredStations = [];
let map = null;
let markers = [];
let labels = [];

// Viloyatlar uchun markaziy koordinatalar
const regionCenters = {
    1: [41.3, 69.3], // Toshkent shahri
    2: [40.8, 72.3], // Andijon
    3: [40.4, 71.8], // Farg'ona
    4: [40.5, 70.9], // Namangan
    5: [39.7, 64.4], // Buxoro
    6: [40.1, 67.8], // Jizzax
    7: [39.0, 66.2], // Qashqadaryo
    8: [40.1, 65.4], // Navoiy
    9: [41.0, 71.7], // Toshkent viloyati
    10: [40.8, 68.8], // Sirdaryo
    11: [37.2, 67.3], // Surxondaryo
    12: [41.6, 60.6], // Xorazm
    13: [43.0, 59.0], // Qoraqalpog'iston
    14: [38.8, 65.8]  // Samarqand
};

// Xaritani yaratish
function initMap() {
    // Xaritani yaratish
    map = L.map(mapContainer, {
        center: mapSettings.center,
        zoom: mapSettings.zoom,
        zoomControl: false
    });

    // Zoom kontrolini qo'shish
    L.control.zoom({ position: 'bottomright' }).addTo(map);

    // Xarita turini tanlash
    updateMapType(mapSettings.mapType);

    // Xarita turi kontrolini qo'shish
    const mapTypeControl = L.control({ position: 'topright' });

    mapTypeControl.onAdd = function () {
        const div = L.DomUtil.create('div', 'map-type-control');
        div.innerHTML = `
            <button class="${mapSettings.mapType === 'roadmap' ? 'active' : ''}" data-type="roadmap">Карта</button>
            <button class="${mapSettings.mapType === 'satellite' ? 'active' : ''}" data-type="satellite">Спутник</button>
        `;

        // Xarita turini o'zgartirish
        div.querySelectorAll('button').forEach(button => {
            button.addEventListener('click', function () {
                const type = this.getAttribute('data-type');
                updateMapType(type);

                // Aktiv klassni yangilash
                div.querySelectorAll('button').forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');
            });
        });

        return div;
    };

    mapTypeControl.addTo(map);

    // Xarita zoom o'zgarishini kuzatish
    map.on('zoomend', updateMarkerSize);

    // Xarita ko'rinishi o'zgarganida markerlarni yangilash
    map.on('moveend', updateMap);
    map.on('zoomend', updateMap);

    // Xarita yuklanganda markerlarni yangilash
    map.on('load', updateMap);
}

// Xarita turini yangilash
function updateMapType(type) {
    // Mavjud tile layerni o'chirish
    if (map && map.hasLayer(mapSettings.tileLayer)) {
        map.removeLayer(mapSettings.tileLayer);
    }

    // Yangi tile layerni qo'shish
    if (type === 'satellite') {
        mapSettings.tileLayer = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
            attribution: '&copy; Esri &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community'
        }).addTo(map);
    } else {
        mapSettings.tileLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; OpenStreetMap contributors'
        }).addTo(map);
    }

    mapSettings.mapType = type;
}

// Zoom darajasiga qarab o'lchamlarni keshlab olish
const sizeCache = {};

// Marker o'lchamini yangilash - optimizatsiya qilingan
function updateMarkerSize() {
    const currentZoom = map.getZoom();

    // Agar bu zoom darajasi uchun o'lchamlar hisoblangan bo'lmasa, hisoblash
    if (!sizeCache[currentZoom]) {
        // Zoom darajasi kichik bo'lganda ham markerlar ko'rinadigan bo'lishi uchun
        // minimal o'lchamni kattaroq qilamiz
        const size = Math.max(6, Math.min(14, 16 - currentZoom));
        const anchor = Math.max(3, Math.min(7, 8 - currentZoom / 2));

        // Zoom darajasi kichik bo'lganda ham yozuvlar o'qiladigan bo'lishi uchun
        // minimal shrift o'lchamini kattaroq qilamiz
        const fontSize = Math.max(8, 12 - currentZoom / 2);
        // Zoom darajasi kichik bo'lganda ham konteyner kattaroq bo'lishi kerak
        const width = Math.max(50, 80 - currentZoom * 3);
        const height = Math.max(12, 16 - currentZoom);
        const anchorX = Math.max(25, 40 - currentZoom * 2);

        // O'lchamlarni keshga saqlash
        sizeCache[currentZoom] = {
            marker: { size, anchor },
            label: { fontSize, width, height, anchorX }
        };
    }

    // Keshdan o'lchamlarni olish
    const sizes = sizeCache[currentZoom];

    // Barcha markerlarni yangilash - keshdan foydalanib
    // Har 10 ta markerni bir vaqtda yangilash (batching)
    const batchSize = 20;
    for (let i = 0; i < markers.length; i += batchSize) {
        setTimeout(() => {
            const batch = markers.slice(i, i + batchSize);
            batch.forEach(marker => {
                const icon = marker.getIcon();
                icon.options.iconSize = [sizes.marker.size, sizes.marker.size];
                icon.options.iconAnchor = [sizes.marker.anchor, sizes.marker.anchor];
                marker.setIcon(icon);
            });
        }, 0);
    }

    // Barcha labellarni yangilash - keshdan foydalanib
    // Har 10 ta labelni bir vaqtda yangilash (batching)
    for (let i = 0; i < labels.length; i += batchSize) {
        setTimeout(() => {
            const batch = labels.slice(i, i + batchSize);
            batch.forEach(label => {
                const icon = label.getIcon();
                const station = label.station;
                icon.options.html = `<div class="bs-name ${station.status}" style="font-size: ${sizes.label.fontSize}px">${station.bsName}</div>`;
                icon.options.iconSize = [sizes.label.width, sizes.label.height];
                icon.options.iconAnchor = [sizes.label.anchorX, 0];
                label.setIcon(icon);
            });
        }, 0);
    }
}

// Marker ikonlarini oldindan yaratib olish (keshlab olish)
const markerIcons = {
    online: L.divIcon({
        className: 'custom-marker online',
        html: '<div></div>',
        iconSize: [8, 8],
        iconAnchor: [4, 4]
    }),
    offline: L.divIcon({
        className: 'custom-marker offline',
        html: '<div></div>',
        iconSize: [8, 8],
        iconAnchor: [4, 4]
    })
};

// Markerni yaratish - optimizatsiya qilingan
function createMarker(station) {
    // Marker - oldindan yaratilgan ikonlardan foydalanish
    const marker = L.marker([station.lat, station.lon], {
        icon: markerIcons[station.status]
    });

    // Popup yaratish
    marker.station = station; // Ma'lumotlarni markerni o'zida saqlash

    // Popup kontentini oldindan yaratib qo'yish
    const popupContent = createPopupContent(station);
    marker.bindPopup(popupContent, {
        maxWidth: 300,
        minWidth: 200,
        className: 'station-popup-container'
    });

    // Label - yanada ixchamroq
    const label = L.marker([station.lat - 0.0003, station.lon], {
        icon: L.divIcon({
            className: 'station-label',
            html: `<div class="bs-name ${station.status}">${station.bsName}</div>`,
            iconSize: [60, 14],
            iconAnchor: [30, 0]
        }),
        interactive: false
    });

    // Stansiya ma'lumotlarini saqlash
    label.station = station;

    return { marker, label };
}

// Popup kontentini yaratish - optimizatsiya qilingan
function createPopupContent(station) {
    // Asosiy ma'lumotlar
    const statusText = station.status === 'online' ? 'Онлайн' : 'Оффлайн';

    let content = `
        <div class="station-popup">
            <h3>${station.bsName}</h3>
            <div class="popup-info">
                <div><strong>Регион:</strong> ${station.region}</div>
                <div><strong>Район:</strong> ${station.area}</div>
                <div><strong>Статус:</strong> <span class="status ${station.status}">${statusText}</span></div>
            </div>
    `;

    // Avariya ma'lumotlarini qo'shish
    if (station.status === 'offline' && station.alarms && station.alarms.length > 0) {
        content += `<div class="alarm-info"><h4>Информация об авариях:</h4>`;

        // Faqat birinchi 3 ta avariyani ko'rsatish (tezroq ishlashi uchun)
        const alarmsToShow = station.alarms.slice(0, 3);

        // Avariyalarni string yaratish orqali qo'shish (join metodi tezroq ishlaydi)
        const alarmItems = alarmsToShow.map(alarm => {
            const alarmType = alarm.type === 'OML_fault' ? 'OML Fault' : 'Nodeb Unavailable';
            const alarmTime = new Date(alarm.appeartime).toLocaleString('ru-RU');

            return `
                <div class="alarm-item">
                    <div><strong>Тип:</strong> ${alarmType}</div>
                    <div><strong>Время:</strong> ${alarmTime}</div>
                    <div><strong>Длительность:</strong> ${alarm.duration}</div>
                    <div><strong>BSC/RNC:</strong> ${alarm.bscrnc}</div>
                </div>
            `;
        }).join('');

        content += alarmItems;

        // Agar ko'proq avariyalar bo'lsa, ularning sonini ko'rsatish
        if (station.alarms.length > 3) {
            content += `<div class="more-alarms">И еще ${station.alarms.length - 3} аварий...</div>`;
        }

        content += `</div>`;
    }

    content += '</div>';

    return content;
}

// Xaritani yangilash uchun debounce funksiyasi
// Bu funksiya xaritani juda tez-tez yangilamaslikni ta'minlaydi
let updateMapTimeout = null;
function debouncedUpdateMap() {
    if (updateMapTimeout) {
        clearTimeout(updateMapTimeout);
    }
    updateMapTimeout = setTimeout(updateMapActual, 100);
}

// Xaritani markerlar bilan yangilash - optimizatsiya qilingan
function updateMap() {
    debouncedUpdateMap();
}

// Xaritani yangilashning asosiy funksiyasi
function updateMapActual() {
    // Barcha markerlarni o'chirish
    markers.forEach(marker => map.removeLayer(marker));
    labels.forEach(label => map.removeLayer(label));

    markers = [];
    labels = [];

    // Xaritaning joriy ko'rinish chegaralarini olish
    const bounds = map.getBounds();
    const currentZoom = map.getZoom();

    // Faqat ko'rinish doirasidagi stantsiyalarni ko'rsatish
    const visibleStations = filteredStations.filter(station => {
        return bounds.contains([station.lat, station.lon]);
    });

    let stationsToShow = [];

    // Zoom darajasi kichik bo'lganda (butun respublika ko'rinishi)
    if (currentZoom <= 6) {
        // Har bir viloyatdan ma'lum miqdorda stansiyalarni ko'rsatish
        // Bu barcha viloyatlar uchun markerlarni ko'rsatishni ta'minlaydi
        const stationsByRegion = {};

        // Stansiyalarni viloyatlar bo'yicha guruhlash - optimizatsiya qilingan
        for (let i = 0; i < visibleStations.length; i++) {
            const station = visibleStations[i];
            if (!stationsByRegion[station.regionId]) {
                stationsByRegion[station.regionId] = {
                    offline: [],
                    online: []
                };
            }
            // Bir vaqtning o'zida status bo'yicha ham ajratib olish
            stationsByRegion[station.regionId][station.status].push(station);
        }

        // Har bir viloyatdan ma'lum miqdorda stansiyalarni olish
        const stationsPerRegion = 100; // Har bir viloyatdan ko'rsatiladigan stansiyalar soni
        const offlinePerRegion = Math.floor(stationsPerRegion / 2); // Offline stansiyalar soni

        // Har bir viloyatdan stansiyalarni qo'shish
        Object.keys(stationsByRegion).forEach(regionId => {
            const regionData = stationsByRegion[regionId];

            // Avval offline stansiyalarni qo'shish
            stationsToShow = stationsToShow.concat(
                regionData.offline.slice(0, offlinePerRegion)
            );

            // Keyin online stansiyalarni qo'shish (limit qolgan bo'lsa)
            const remainingSlots = stationsPerRegion - Math.min(offlinePerRegion, regionData.offline.length);
            stationsToShow = stationsToShow.concat(
                regionData.online.slice(0, remainingSlots)
            );
        });
    } else {
        // Zoom darajasiga qarab maksimal marker sonini aniqlash
        let maxMarkersToShow;
        if (currentZoom <= 8) {
            maxMarkersToShow = 1000; // O'rta zoom darajasi uchun
        } else if (currentZoom <= 10) {
            maxMarkersToShow = 800; // Yaqin zoom darajasi uchun
        } else {
            maxMarkersToShow = 600; // Juda yaqin zoom darajasi uchun
        }

        // Offline stansiyalarga ustunlik berish
        const offlineStations = visibleStations.filter(s => s.status === 'offline');
        const onlineStations = visibleStations.filter(s => s.status === 'online');

        // Offline stansiyalarni olish (maksimal sonning 40%)
        const maxOffline = Math.floor(maxMarkersToShow * 0.4);
        stationsToShow = stationsToShow.concat(
            offlineStations.slice(0, Math.min(maxOffline, offlineStations.length))
        );

        // Online stansiyalarni olish (qolgan kvota)
        const remainingSlots = maxMarkersToShow - Math.min(maxOffline, offlineStations.length);
        stationsToShow = stationsToShow.concat(
            onlineStations.slice(0, Math.min(remainingSlots, onlineStations.length))
        );
    }

    // Yangi markerlarni qo'shish - batching orqali
    const batchSize = 100; // Bir vaqtda qo'shiladigan markerlar soni
    for (let i = 0; i < stationsToShow.length; i += batchSize) {
        const batch = stationsToShow.slice(i, i + batchSize);
        setTimeout(() => {
            batch.forEach(station => {
                const { marker, label } = createMarker(station);
                marker.addTo(map);
                label.addTo(map);

                markers.push(marker);
                labels.push(label);
            });

            // Faqat oxirgi batch uchun o'lchamlarni yangilash
            if (i + batchSize >= stationsToShow.length) {
                updateMarkerSize();
            }
        }, 0);
    }

    // Agar barcha stantsiyalar ko'rsatilmagan bo'lsa, ogohlantirish ko'rsatish
    if (visibleStations.length > stationsToShow.length) {
        console.log(`Showing ${stationsToShow.length} out of ${visibleStations.length} visible stations. Zoom in to see more details.`);
    }
}

// Filtrlash uchun debounce funksiyasi
let filterTimeout = null;
function debouncedFilterStations() {
    if (filterTimeout) {
        clearTimeout(filterTimeout);
    }
    filterTimeout = setTimeout(filterStationsActual, 100);
}

// Stansiyalarni filtrlash - optimizatsiya qilingan
function filterStations() {
    debouncedFilterStations();
}

// Xaritani markazlashtirish funksiyasi
function centerMapOnRegion(regionId) {
    if (regionId && regionCenters[regionId]) {
        const center = regionCenters[regionId];
        map.setView(center, 8); // Zoom darajasi 8 - viloyat uchun mos
    } else {
        // Agar regionId yo'q bo'lsa, butun O'zbekistonni ko'rsatish
        map.setView(mapSettings.center, 6);
    }
}

// Xaritani tuman markaziga markazlashtirish
function centerMapOnArea(areaId) {
    if (!areaId) return;

    // Tanlangan tumandagi birinchi BS ni topish
    const areaStations = baseStations.filter(station => station.areaId === areaId);

    if (areaStations.length > 0) {
        // Tumandagi birinchi BS koordinatalarini olish
        const firstStation = areaStations[0];
        map.setView([firstStation.lat, firstStation.lon], 10); // Zoom darajasi 10 - tuman uchun mos
    } else {
        // Agar tumanda BS yo'q bo'lsa, viloyat markaziga o'tish
        const area = areas.find(a => a.id === areaId);
        if (area) {
            centerMapOnRegion(area.region_id);
        }
    }
}

// BS ga markazlashtirish funksiyasi
function centerMapOnBS(bsName) {
    if (!bsName) return;

    // BS nomiga mos stantsiyani topish
    const foundStations = baseStations.filter(station =>
        station.bsName.toLowerCase().includes(bsName.toLowerCase()));

    if (foundStations.length > 0) {
        // Birinchi topilgan BS koordinatalarini olish
        const station = foundStations[0];
        map.setView([station.lat, station.lon], 12); // Zoom darajasi 12 - BS uchun mos

        // BS markerini ajratib ko'rsatish
        highlightBS(station);
    }
}

// BS markerini ajratib ko'rsatish
function highlightBS(station) {
    // Barcha markerlarni o'chirish
    markers.forEach(marker => {
        // Marker ikonini asliga qaytarish
        if (marker.station && marker.station.id === station.id) {
            // Markerni kattalashtirish
            const icon = marker.getIcon();
            const originalSize = icon.options.iconSize[0];
            icon.options.iconSize = [originalSize * 1.5, originalSize * 1.5];
            icon.options.iconAnchor = [originalSize * 0.75, originalSize * 0.75];
            marker.setIcon(icon);

            // Popup ni ochish
            marker.openPopup();
        }
    });
}

// Stansiyalarni filtrlashning asosiy funksiyasi
function filterStationsActual() {
    // Bir marta filter qilish uchun
    filteredStations = baseStations.filter(station => {
        // Qidiruv bo'yicha filtrlash
        if (filters.search) {
            const searchTerm = filters.search.toLowerCase();
            if (!station.bsName.toLowerCase().includes(searchTerm)) {
                return false;
            }
        }

        // Viloyat bo'yicha filtrlash
        if (filters.regionId && station.regionId !== filters.regionId) {
            return false;
        }

        // Tuman bo'yicha filtrlash
        if (filters.areaId && station.areaId !== filters.areaId) {
            return false;
        }

        // Status bo'yicha filtrlash
        if (filters.status !== 'all' && station.status !== filters.status) {
            return false;
        }

        return true;
    });

    // Qidiruv bo'yicha BS ga markazlashtirish
    if (filters.search && filteredStations.length > 0) {
        // Agar faqat bitta BS topilgan bo'lsa, unga markazlashtirish
        if (filteredStations.length === 1) {
            centerMapOnBS(filters.search);
        } else {
            // Agar bir nechta BS topilgan bo'lsa, birinchisiga markazlashtirish
            centerMapOnBS(filters.search);
        }
    }
    // Viloyat yoki tuman tanlanganda xaritani markazlashtirish
    else if (filters.areaId) {
        centerMapOnArea(filters.areaId);
    } else if (filters.regionId) {
        centerMapOnRegion(filters.regionId);
    }

    // Xaritani yangilash
    updateMap();

    // Statistikani yangilash
    updateStatistics();
}

// Statistikani yangilash - optimizatsiya qilingan
function updateStatistics() {
    const totalStations = baseStations.length;

    // Bir marta aylanib chiqib, online va offline stansiyalarni hisoblash
    let onlineStations = 0;
    let offlineStations = 0;

    for (let i = 0; i < baseStations.length; i++) {
        if (baseStations[i].status === 'online') {
            onlineStations++;
        } else {
            offlineStations++;
        }
    }

    // DOM elementlarini yangilash
    totalStationsElement.textContent = totalStations;
    onlineStationsElement.textContent = onlineStations;
    offlineStationsElement.textContent = offlineStations;
}

// Viloyatlar ro'yxatini yangilash
function updateRegionSelect() {
    // Mavjud variantlarni tozalash
    regionSelect.innerHTML = '<option value="">Все регионы</option>';

    // Yangi variantlarni qo'shish
    regions.forEach(region => {
        const option = document.createElement('option');
        option.value = region.id;
        option.textContent = region.name;
        regionSelect.appendChild(option);
    });
}

// Tumanlar ro'yxatini yangilash
function updateAreaSelect() {
    // Mavjud variantlarni tozalash
    areaSelect.innerHTML = '<option value="">Все районы</option>';

    // Agar viloyat tanlangan bo'lsa
    if (filters.regionId) {
        // Tanlangan viloyatga tegishli tumanlarni olish
        const filteredAreas = areas.filter(area => area.region_id === filters.regionId);

        // Yangi variantlarni qo'shish
        filteredAreas.forEach(area => {
            const option = document.createElement('option');
            option.value = area.id;
            option.textContent = area.name;
            areaSelect.appendChild(option);
        });

        // Tuman tanlovini yoqish
        areaSelect.disabled = false;
    } else {
        // Tuman tanlovini o'chirish
        areaSelect.disabled = true;
    }
}

// Ma'lumotlarni yuklash
async function loadData() {
    try {
        // Viloyatlarni yuklash
        const regionsResponse = await fetch('/api/regions/');
        regions = await regionsResponse.json();
        updateRegionSelect();

        // Tumanlarni yuklash
        const areasResponse = await fetch('/api/areas/');
        areas = await areasResponse.json();

        // Baza stantsiyalarini porsiyalar bilan yuklash
        await loadBaseStationsInBatches();

        // Statistikani yangilash
        updateStatistics();
    } catch (error) {
        console.error('Error loading data:', error);
    }
}

// Baza stantsiyalarini porsiyalar bilan yuklash
async function loadBaseStationsInBatches() {
    try {
        // Avval birinchi 100 ta stantsiyani yuklash
        const firstBatchResponse = await fetch('/api/base-stations/?limit=100&offset=0');
        const firstBatch = await firstBatchResponse.json();

        // Birinchi porsiyani ko'rsatish
        baseStations = firstBatch;
        filteredStations = [...baseStations];
        updateMap();

        // Qolgan stantsiyalarni porsiyalar bilan yuklash
        let offset = 100;
        const batchSize = 100;
        let hasMore = firstBatch.length === batchSize;

        while (hasMore) {
            const batchResponse = await fetch(`/api/base-stations/?limit=${batchSize}&offset=${offset}`);
            const batch = await batchResponse.json();

            // Yangi stantsiyalarni qo'shish
            baseStations = [...baseStations, ...batch];

            // Filtrlangan stantsiyalarni yangilash
            filterStations();

            // Xaritani yangilash
            updateMap();

            // Statistikani yangilash
            updateStatistics();

            // Keyingi porsiya uchun offset ni yangilash
            offset += batchSize;

            // Agar porsiya to'liq bo'lmasa, demak boshqa stantsiyalar yo'q
            hasMore = batch.length === batchSize;

            // Keyingi porsiyani yuklashdan oldin kichik pauza
            await new Promise(resolve => setTimeout(resolve, 100));
        }
    } catch (error) {
        console.error('Error loading base stations in batches:', error);
    }
}

// Event listenerlarni qo'shish
function setupEventListeners() {
    // Qidiruv
    searchInput.addEventListener('input', function () {
        filters.search = this.value;
        filterStations();
    });

    // Viloyat tanlovi
    regionSelect.addEventListener('change', function () {
        filters.regionId = this.value ? parseInt(this.value) : null;
        filters.areaId = null; // Tuman tanlovini tiklash
        updateAreaSelect();
        filterStations();
    });

    // Tuman tanlovi
    areaSelect.addEventListener('change', function () {
        filters.areaId = this.value ? parseInt(this.value) : null;
        filterStations();
    });

    // Status tanlovi
    statusSelect.addEventListener('change', function () {
        filters.status = this.value;
        filterStations();
    });

    // Filtrlarni tiklash
    resetButton.addEventListener('click', function () {
        // Filtrlarni tiklash
        filters = {
            search: '',
            regionId: null,
            areaId: null,
            status: 'all'
        };

        // UI ni tiklash
        searchInput.value = '';
        regionSelect.value = '';
        statusSelect.value = 'all';
        updateAreaSelect();

        // Xaritani boshlang'ich holatga qaytarish
        map.setView(mapSettings.center, mapSettings.zoom);

        // Stansiyalarni filtrlash
        filterStations();
    });
}

// Ma'lumotlarni yangilash intervali
function setupDataRefresh() {
    // Har 2 daqiqada ma'lumotlarni yangilash
    setInterval(async () => {
        try {
            // Baza stantsiyalarini yangilash
            const baseStationsResponse = await fetch('/api/base-stations/');
            baseStations = await baseStationsResponse.json();

            // Stansiyalarni filtrlash
            filterStations();
        } catch (error) {
            console.error('Error updating data:', error);
        }
    }, 120000); // 2 daqiqa
}

// Ilovani ishga tushirish
function initApp() {
    // Xaritani yaratish
    initMap();

    // Event listenerlarni qo'shish
    setupEventListeners();

    // Ma'lumotlarni yuklash
    loadData();

    // Ma'lumotlarni yangilash intervalini o'rnatish
    setupDataRefresh();
}

// Sahifa yuklanganda ilovani ishga tushirish
document.addEventListener('DOMContentLoaded', initApp);
