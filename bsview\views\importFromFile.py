from bsview.views.imports_file import *
@login_required
def importData(request):
    region = RegionUzb.objects.all()
    return render(request, 'import.html', {'region': region})


@login_required
def getfromexcel(request):
    region = request.POST.get('regiono')
    if request.method == "POST":
        fileExcel = request.FILES['FileExcel']
        workbook = load_workbook(fileExcel)
        sheet = workbook.active
        errorbs = []
        errorarea = []
        error = False
        for row in sheet.iter_rows(min_row=2):
            if CheckAreaId(row[20].value):
                errorbs.append(row[1].value)
                errorarea.append(row[20].value)
                error = True
        if error:
            return render(request, 'bs_import.html', {'error': error, 'allError': zip(errorbs, errorarea)})
        else:
            with connection.cursor() as cursor:
                cursor.execute("DELETE FROM bsview_bsbeeline WHERE region_id = '" + region + "'")
                cursor.fetchone()  # получаем одну строку
                for row in sheet.iter_rows(min_row=2):
                    cursor.execute(f"INSERT INTO bsview_bsbeeline("
                                   f"bsname, bsnum, besecnum, lat, lon, modem, address, gsm900, gsm1800, gsmbi1800, umts900, "
                                   f"umts2100, umtsbesec, lte800, lte1800, ltebi1800, lte2600, lte2300, lte2100, "
                                   f"area_id, region_id, author_id)"
                                   f" VALUES ('{row[1].value}', '{row[2].value}', '{row[3].value}', '{row[4].value}'"
                                   f", '{row[5].value}', '{row[18].value}', '{row[19].value}', '{row[6].value}', '{row[7].value}'"
                                   f", '{row[8].value}', '{row[9].value}', '{row[10].value}', '{row[11].value}', '{row[12].value}'"
                                   f", '{row[13].value}', '{row[14].value}', '{row[15].value}', '{row[16].value}', '{row[17].value}'"
                                   f", '{getAreaId(row[20].value).id}', '{region}', '{request.user.id}');")
                    cursor.fetchone()
            return render(request, 'bs_import.html', {'error': error, 'message': "Обновление данных выполнена успешно"})
