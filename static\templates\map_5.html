{% extends 'base.html' %}
{% load crispy_forms_tags %}
{% block title %} Map View BS Beeline {% endblock title %}

{% block content %}

    <title>Карта Точек</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
     integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
     crossorigin=""/>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
     integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
     crossorigin=""></script>

    <style>
        /* Задаем размер для контейнера карты */
        #mapid { height: 600px; width: 100%; }
    </style>
</head>
<body>

<h1>Карта с Точками</h1>

<div id="mapid"></div>

{{ points_data_json|json_script:"points-data" }}

<script>
    // 1. Инициализация карты
    var mymap = L.map('mapid').setView([50, 70], 3);

    // 2. Добавление слоя тайлов
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(mymap);

    // 3. Получение данных о точках
    const pointsDataElement = document.getElementById('points-data');
    const points = JSON.parse(pointsDataElement.textContent);

    // Функция для определения размера круга в зависимости от уровня зума
    function getCircleRadius(zoom) {
        if (zoom < 6) {
            return 2;
        } else if (zoom < 10) {
            return 6;
        } else {
            return 10;
        }
    }

    // Функция для обновления размера кругов на карте
    function updateCircleSizes() {
        const currentZoom = mymap.getZoom();
        const newRadius = getCircleRadius(currentZoom);

        mymap.eachLayer(function (layer) {
            if (layer instanceof L.CircleMarker) {
                layer.setRadius(newRadius);
            }
        });
    }

    // Создаем и добавляем маркеры на карту
    const pointLayers = []; // Массив для хранения слоев маркеров
    points.forEach(function(point) {
        let pointColor;
        let statusText;

        if (point.status === true) {
            pointColor = 'red';
            statusText = 'БС не работает';
        } else {
            pointColor = 'green';
            statusText = 'БС активен';
        }

        const circleMarker = L.circleMarker([point.lat, point.lon], {
            radius: getCircleRadius(mymap.getZoom()),
            fillColor: pointColor,
            color: "#000",
            weight: 1,
            opacity: 1,
            fillOpacity: 0.8
        })
        .bindTooltip("<b>" + point.name + "</b><br>" + statusText, {
            permanent: false, // Надпись исчезает при отведении курсора
            direction: 'top', // Направление отображения подсказки
            offset: [0, -10]   // Смещение подсказки относительно маркера
        })
        .addTo(mymap);

        pointLayers.push(circleMarker);
    });

    // Слушаем событие 'zoomend'
    mymap.on('zoomend', updateCircleSizes);

    // (Опционально) Центрирование карты
    if (points.length > 0) {
        var group = new L.featureGroup(points.map(p => L.marker([p.lat, p.lon])));
        mymap.fitBounds(group.getBounds().pad(0.5));
    }

</script>



</body>

{% endblock content %}