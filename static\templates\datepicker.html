<script type="text/javascript">

var today = new Date("{{ start_date }}");
var endDate = new Date("{{ end_date }}");

$(function() {
    $('input[name="daterange"]').daterangepicker({
    startDate: today,
    endDate: endDate,
    showWeekNumbers: true,
    "locale": {
        "format": "DD/MM/YYYY",
        "separator": " - ",
        "applyLabel": "Сохранить",
        "cancelLabel": "Назад",
        "daysOfWeek": [
            "Вс",
            "Пн",
            "Вт",
            "Ср",
            "Чт",
            "Пт",
            "Сб"
        ],
        "monthNames": [
            "Январь",
            "Февраль",
            "Март",
            "Апрель",
            "Май",
            "Июнь",
            "Июль",
            "Август",
            "Сентябрь",
            "Октя<PERSON>рь",
            "Но<PERSON><PERSON>р<PERSON>",
            "Декабрь"
        ],
        "firstDay": 1
    }}
    );
});
</script>


</script>