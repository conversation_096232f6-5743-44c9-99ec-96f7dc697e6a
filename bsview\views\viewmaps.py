import json, re
from datetime import datetime
from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status
from bsview.models import RegionUzb, AreaUzb, BsBeeline, Current_Alarms, Log_Alarms

# Ихчам ёрдамчи функциялар
def getNumberBS(bsname): return bsname.split('_')[-1] if bsname and '_' in bsname else re.search(r'\d+$', bsname or '').group(0) if bsname and re.search(r'\d+$', bsname) else None
def get_cabinet_type(bsname): return 'LTE' if bsname and 'lte' in bsname.lower() else 'BTS' if bsname and 'bts' in bsname.lower() else 'NodeB' if bsname and 'node' in bsname.lower() else None

# BS маълумотларини олиш ва форматлаш
def get_formatted_bs_data(base_stations=None, region_id=None, area_id=None, datetime_param=None, offset=0, limit=None, skip_alarms=False):
    # Аргументлар асосида фильтрлаш
    query = BsBeeline.objects.all()
    if region_id: query = query.filter(region_id=region_id)
    if area_id: query = query.filter(area_id=area_id)
    
    # Базадан BS маълумотларини олиш
    if base_stations is None:
        base_stations = query.values('id', 'bsname', 'bsnum', 'lat', 'lon', 'area_id', 'region_id')
        if limit: base_stations = base_stations[int(offset):int(offset)+int(limit)]
    
    # Регион ва туман луғатларини олиш
    regions = {r['id']: r['name'] for r in RegionUzb.objects.all().values('id', 'name')}
    
    # Агар регион берилган бўлса, фақат шу региондаги туманларни олиш
    if region_id:
        areas = {a['id']: a['name'] for a in AreaUzb.objects.filter(region_id=region_id).values('id', 'name')}
    elif area_id:
        area = AreaUzb.objects.filter(id=area_id).first()
        areas = {area_id: area.name if area else ''}
    else:
        areas = {a['id']: a['name'] for a in AreaUzb.objects.all().values('id', 'name')}
    
    # Таърих учун сана олиш
    dt = datetime.fromisoformat(datetime_param.replace('Z', '+00:00')) if datetime_param else None
    
    # BS маълумотларини форматлаш
    formatted_stations = []
    for bs in base_stations:
        # Координаталарни тўғирлаш
        if bs.get('lat'): bs['lat'] = bs['lat'].replace(',', '.')
        if bs.get('lon'): bs['lon'] = bs['lon'].replace(',', '.')
        
        # Базавий маълумотлар
        bs_data = {
            'id': bs['id'], 'name': bs['bsname'], 'bsName': bs['bsname'], 
            'lat': bs['lat'], 'lon': bs['lon'], 'status': False, 
            'calcTime': None, 'cabinetType': None, 'typeG': None,
            'region_id': bs['region_id'], 'region_name': regions.get(bs['region_id']),
            'area_id': bs['area_id'], 'area_name': areas.get(bs['area_id'])
        }
        
        # Авария маълумотларини аниқлаш (фақат алоҳида сўралмаганда)
        if not skip_alarms:
            if dt:
                # Таърихий авария текшириш
                historical_alarm = Log_Alarms.objects.filter(
                    bsname__contains=bs['bsnum'], appeartime__lte=dt, cleartime__gt=dt
                ).first()
                
                if historical_alarm:
                    bs_data['status'] = True
                    # Давомийликни ҳисоблаш
                    time_diff = dt - historical_alarm.appeartime
                    hours, remainder = divmod(time_diff.total_seconds(), 3600)
                    bs_data['calcTime'] = f"{int(hours)} hours {int(remainder // 60)} minutes"
                    bs_data['cabinetType'] = get_cabinet_type(historical_alarm.bsname)
            else:
                # Жорий авария текшириш
                alarm = Current_Alarms.objects.filter(bsname__contains=bs['bsnum']).first()
                if alarm:
                    bs_data['status'] = True
                    bs_data['calcTime'] = str(alarm.calctime) if hasattr(alarm, 'calctime') else None
                    bs_data['cabinetType'] = get_cabinet_type(bs['bsname'])

        formatted_stations.append(bs_data)
    
    return formatted_stations

# API эндпоинтлар
@api_view(['GET'])
def mapindex(request): return render(request, 'map3.html')

@api_view(['GET'])
def get_all_base_stations(request):
    """Barcha baza stantsiyalarini olish"""
    try:
        stations = get_formatted_bs_data(
            datetime_param=request.query_params.get('datetime'),
            offset=request.query_params.get('offset', 0),
            limit=request.query_params.get('limit')
        )
        return Response(stations)
    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
def get_base_stations_by_region(request, region_id):
    """Viloyat bo'yicha baza stantsiyalarini olish"""
    try:
        stations = get_formatted_bs_data(region_id=region_id)
        return Response(stations)
    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
def get_base_stations_by_area(request, area_id):
    """Tuman bo'yicha baza stantsiyalarini olish"""
    area = AreaUzb.objects.filter(id=area_id).first()
    if not area:
        return Response({'error': 'Area not found'}, status=status.HTTP_404_NOT_FOUND)
    
    try:
        stations = get_formatted_bs_data(area_id=area_id)
        return Response(stations)
    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
def get_all_regions(request):
    """Barcha viloyatlarni olish"""
    regions = RegionUzb.objects.all().values('id', 'name')
    return Response(list(regions))

@api_view(['GET'])
def get_all_areas(request):
    """Barcha tumanlarni olish"""
    areas = AreaUzb.objects.all().values('id', 'name', 'region_id')
    return Response(list(areas))

@api_view(['GET'])
def get_areas_by_region(request, region_id):
    """Viloyat bo'yicha tumanlarni olish"""
    areas = AreaUzb.objects.filter(region_id=region_id).values('id', 'name', 'region_id')
    return Response(list(areas))

@api_view(['GET'])
def get_all_alarms(request):
    """Barcha avariyalarni olish"""
    alarms = Current_Alarms.objects.all().values('id', 'bsname', 'bscrnc', 'nename', 'netype', 'calctime')
    return Response(list(alarms))

@login_required()
def map_view(request):
    """Xaritani ko'rsatish"""
    # Аввал БС ларни аварияли статуссиз олиш
    bs_stations = get_formatted_bs_data(skip_alarms=True)
    
    # БС лар ва аларм луғати
    bs_by_number = {}
    alarm_details = {}
    
    # БС маълумотларини номер бўйича луғатга жойлаш
    for bs in bs_stations:
        bsnum = getNumberBS(bs['name'])
        if bsnum:
            bs_by_number[bsnum] = bs['id']
    
    # Барча аларм маълумотларини йиғиш
    for alarm in Current_Alarms.objects.all():
        bs_number = getNumberBS(alarm.bsname)
        if not bs_number or bs_number not in bs_by_number:
            continue
            
        bs_id = bs_by_number[bs_number]
        
        # Технология турини аниқлаш
        typeG = "3G"
        if hasattr(alarm, 'bscrnc'):
            if alarm.bscrnc.startswith("BSC"): typeG = "2G"
            elif alarm.bscrnc.startswith("LTE"): typeG = "4G"
        
        # Илк марта кўрилган БС алармини қўшиш
        if bs_id not in alarm_details:
            alarm_details[bs_id] = {
                'status': True,
                'calcTime': str(alarm.calctime) if hasattr(alarm, 'calctime') else None,
                'typeG': typeG,
                'cabinetType': get_cabinet_type(alarm.bsname) 
            }
        # Мавжуд БС га технология турини қўшиш
        elif typeG not in alarm_details[bs_id]['typeG']:
            alarm_details[bs_id]['typeG'] += "/" + typeG
    
    # Аваряли БС ларга статус қўшиш
    for bs in bs_stations:
        if bs['id'] in alarm_details:
            bs.update(alarm_details[bs['id']])
    
    return render(request, 'map.html', {'points_data_json': bs_stations})