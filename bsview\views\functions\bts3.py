def bts3Create(bss, bsName, bsnomer, cellnomer, channel, cid, scr):
    bts = [f'SYSTEM;SITE;LAT;LON;CELL;CH;CID;SCR;DIR\n']
    err = 0
    errbsname=[]
    errbs_pk=[]
    errbs_dp=[]

    for bs in bss:
        num = 0
        errbs_dp900=''
        errbs_dp2100=''
        for cellid in cid:
            sec=''
            Erroriko = False
            if bs.bsnum == bsnomer[num] and int(channel[num]) > 10000 :
                if cellid[-1]=='1' or cellid[-1]=='5' or cellid[-1]=='8':
                    try:
                        if bs.umts2100:
                            sec=bs.umts2100.split('-')[0]
                        elif bs.umtsbesec:
                            sec=bs.umtsbesec.split('-')[0]
                    except:
                        Erroriko=True
                        errbs_dp2100 = 'Umts-2100'
                elif cellid[-1]=='2' or cellid[-1]=='6' or cellid[-1]=='9':
                    try:
                        if bs.umts2100:
                            sec = bs.umts2100.split('-')[1]
                        elif bs.umtsbesec:
                            sec = bs.umtsbesec.split('-')[1]
                    except:
                        Erroriko=True
                        errbs_dp2100 = 'Umts-2100'
                elif cellid[-1]=='3'or cellid[-1]=='7' or cellid[-1]=='0':
                    try:
                        if bs.umts2100:
                            sec = bs.umts2100.split('-')[2]
                        elif bs.umtsbesec:
                            sec = bs.umtsbesec.split('-')[2]
                    except:
                        Erroriko=True
                        errbs_dp2100 = 'Umts-2100'
                elif cellid[-1]=='4':
                    try:
                        if bs.umts2100:
                            sec = bs.umts2100.split('-')[3]
                        elif bs.umtsbesec:
                            sec = bs.umtsbesec.split('-')[3]
                    except:
                        Erroriko=True
                        errbs_dp2100 = 'Umts-2100'

                if Erroriko==True:
                    err +=1
                    errbsname.append(cellid)
                    errbs_pk.append(bs.id)
                    errbs_dp.append(errbs_dp900 + ' | ' + errbs_dp2100)
                else:
                    bts.append(f'UMTS;{bs.bsname};{bs.lat.replace(",",".")};{bs.lon.replace(",",".")};{cellnomer[num]};{channel[num]};{cid[num]};{scr[num]};{sec} \n')
            num = num + 1
    if err > 0:
        #print(f'{errbsname} {errbs_pk} {errbs_dp}')
        errors = {'errbsname': errbsname, 'errbs_pk': errbs_pk , 'errbs_dp': errbs_dp}
        return errors
    else:

        return bts
