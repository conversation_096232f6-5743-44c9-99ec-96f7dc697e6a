from bsview.models import *
def getAzimuth(cell, az):
    cell1 = []
    cell2 = []
    cell3 = []
    cell4 = []
    a1, a2, a3, a4, a5, a6, a7, a8, a9, a0 = 1, 2, 3, 4, 5, 6, 7, 8, 9, 0
    for i in range(1, 11):
        cell1= cell1 + [f'{a1}', f'{a5}', f'{a8}']
        cell2=cell2+ [f'{a2}', f'{a6}', f'{a9}']
        cell3=cell3+[f'{a3}', f'{a7}', f'{a0}']
        cell4.append(f'{a4}')
        a1, a2, a3, a4, a5, a6, a7, a8, a9 = \
            a1 + 10, a2 + 10, a3 + 10, a4 + 10, a5 + 10, a6 + 10, a7 + 10, a8 + 10, a9 + 10
        if a0 == 0:
            a0 = a0 + 20
        else:
            a0 = a0 + 10
    azi = az.split('-')
    az1 = azi[0]
    az2 = azi[1]
    az3 = azi[2]

    print(cell4)

    if cell in cell1:  return az1
    if cell in cell2:  return az2
    if cell in cell3:  return az3

def getCellTech2(cell):
    if cell in ['1','2','3']:
        Tech = 'GSM1800'
    elif cell in  ['5','6','7']:
        Tech = 'GSM900'
    elif cell in  ['8','9','0']:
        Tech = 'GSM1800Bi'
    return Tech

def getCellTech3(cell):
    if cell in ['1','2','3']:
        Tech = 'GSM'
    elif cell in  ['5','6','7']:
        Tech = 'GSM'
    elif cell in  ['8','9','0']:
        Tech = 'GSM'
    return Tech

def getCellTech4(cell):
    if cell in ['1','2','3']:
        Tech = 'GSM'
    elif cell in  ['5','6','7']:
        Tech = 'GSM'
    elif cell in  ['8','9','0']:
        Tech = 'GSM'
    return Tech

def getBsfromRegion(region_id):
    bsnumlist = [];
    allVar = BsBeeline.objects.filter(region_id=region_id)
    for vars in allVar:
        bsnumlist.append(vars.bsnum)
    return bsnumlist

def getRegion(region_id):
    return RegionUzb.objects.get(id=region_id)


def CheckAreaId(areaName):
    try:
        AreaUzb.objects.get(name=areaName)
        return False
    except:
        print(areaName)
        return True


def getAreaId(areaName):
    return AreaUzb.objects.get(name=areaName)

def getNumberBS(text):
  if not isinstance(text, str):
      return ""
  parts = text.rsplit('_', 1)
  if len(parts) > 1:
    return parts[1]
  else:
    return ""



