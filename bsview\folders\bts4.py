def bts4Create(bss, band, bsnomer, cellnomer, channel, cid, pci, tac):
    bts = [f'SYSTEM;SITE;LAT;LON;CELL;CH;CID;PCI;TAC;DIR\n']
    err = 0
    errbsname = []
    errbs_pk = []
    errbs_dp = []

    for bs in bss:
        num = 0
        errbs_lte = ''
        for cellid in cid:
            sec = ''
            Erroriko = False
            if bs.bsnum == bsnomer[num]:
                if cellid[-1] == '1' or cellid[-1] == '5' or cellid[-1] == '8' or cellid[-1] == '11' \
                        or cellid[-1] == '15' or cellid[-1] == '18' or cellid[-1] == '21' or cellid[-1] == '25' \
                        or cellid[-1] == '28' or cellid[-1] == '31' or cellid[-1] == '35' or cellid[-1] == '38' \
                        or cellid[-1] == '41' or cellid[-1] == '45' or cellid[-1] == '48' or cellid[-1] == '91':
                    try:
                        if bs.lte800 and band[num]== '5'or band[num]== '18' or band[num]== '8':
                            sec = bs.lte800.split('-')[0]
                        elif bs.lte1800  and band[num]== '3':
                            sec = bs.lte1800.split('-')[0]
                        elif bs.lte2600 and band[num]== '7'or band[num]== '38' or band[num]== '41':
                            sec = bs.lte2600.split('-')[0]
                        elif bs.lte2300 and band[num]== '40':
                            sec = bs.lte2300.split('-')[0]
                        elif bs.lte2100 and band[num]== '1':
                            sec = bs.lte2100.split('-')[0]
                    except:
                        Erroriko = True
                        errbs_lte = 'LTE'
                if cellid[-1] == '2' or cellid[-1] == '6' or cellid[-1] == '9' or cellid[-1] == '12' \
                        or cellid[-1] == '16' or cellid[-1] == '19' or cellid[-1] == '22' or cellid[-1] == '26' \
                        or cellid[-1] == '29' or cellid[-1] == '32' or cellid[-1] == '36' or cellid[-1] == '39' \
                        or cellid[-1] == '42' or cellid[-1] == '46' or cellid[-1] == '49' or cellid[-1] == '92':

                    try:
                        if bs.lte800 and band[num] == '5' or band[num] == '18' or band[num] == '8':
                            sec = bs.lte800.split('-')[1]
                        elif bs.lte1800 and band[num] == '3':
                            sec = bs.lte1800.split('-')[1]
                        elif bs.lte2600 and band[num] == '7' or band[num] == '38' or band[num] == '41':
                            sec = bs.lte2600.split('-')[1]
                        elif bs.lte2300 and band[num] == '40':
                            sec = bs.lte2300.split('-')[1]
                        elif bs.lte2100 and band[num] == '1':
                            sec = bs.lte2100.split('-')[1]
                    except:
                        Erroriko = True
                        errbs_lte = 'LTE'
                if cellid[-1] == '3' or cellid[-1] == '7' or cellid[-1] == '0' or cellid[-1] == '13' \
                        or cellid[-1] == '17' or cellid[-1] == '20' or cellid[-1] == '23' or cellid[-1] == '27' \
                        or cellid[-1] == '30' or cellid[-1] == '33' or cellid[-1] == '37' or cellid[-1] == '40' \
                        or cellid[-1] == '43' or cellid[-1] == '47' or cellid[-1] == '50' or cellid[-1] == '93':

                    try:
                        if bs.lte800 and band[num] == '5' or band[num] == '18' or band[num] == '8':
                            sec = bs.lte800.split('-')[2]
                        elif bs.lte1800 and band[num] == '3':
                            sec = bs.lte1800.split('-')[2]
                        elif bs.lte2600 and band[num] == '7' or band[num] == '38' or band[num] == '41':
                            sec = bs.lte2600.split('-')[2]
                        elif bs.lte2300 and band[num] == '40':
                            sec = bs.lte2300.split('-')[2]
                        elif bs.lte2100 and band[num] == '1':
                            sec = bs.lte2100.split('-')[2]
                    except:
                        Erroriko = True
                        errbs_lte = 'LTE'

                if Erroriko == True:
                    err += 1
                    errbsname.append(cellid)
                    errbs_pk.append(bs.id)
                    errbs_dp.append(errbs_lte)
                else:
                    bts.append(
                        f'LTE;{bs.bsname};{bs.lat.replace(",",".")};{bs.lon.replace(",",".")};{cellnomer[num]};{channel[num]};{cid[num]};{pci[num]};{tac[num]};{sec} \n')
            num = num + 1
    if err > 0:
        # print(f'{errbsname} {errbs_pk} {errbs_dp}')
        errors = {'errbsname': errbsname, 'errbs_pk': errbs_pk, 'errbs_dp': errbs_dp}
        return errors
    else:

        return bts
