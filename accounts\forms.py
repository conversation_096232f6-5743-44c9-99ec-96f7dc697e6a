from django.contrib.auth.forms import UserCreationForm, UserChangeForm
from .models import *


class CustomUserCreationForm(UserCreationForm):
    class Meta(UserCreationForm):
        model=CustomUser
        fields = ('username', 'first_name', 'last_name','email', 'telegram_id','region', 'privilege',)

class CustomUserChangeForm(UserChangeForm):
    class Meta(UserChangeForm):
        model=CustomUser
        fields = ('username', 'first_name', 'last_name','email', 'telegram_id', 'region', 'privilege',)

from django.contrib.auth.forms import AuthenticationForm

