{% extends 'base.html' %}
{% load static %}


{% block content %}

<div class="jumbotron">
    <p>
</div>
<body>
<form action="{% url 'getkmz' %}" method="post" id="getkmzform">
    {% csrf_token %}

    <div class="container card ">
        <div class="row ">
            <div class="col col-3">
                <p>
                    <b>Выберите область:</b>
                <p>

                <div class="card-header">
                    <select class="form-select" multiple aria-label="multiple select example" name="regiono" id="reg">
                        {% for reg in region %}
                        {% if reg.id == user.region_id %}
                        <option selected value="{{ reg.id }}">{{reg.name}}</option>
                        {% else %}
                        {% if user.privilege_id == 1 or user.privilege_id == 4 %}
                        <option value="{{ reg.id }}">{{reg.name}}</option>
                        {% else %}
                        <option value="{{ reg.id }}" disabled>{{reg.name}}</option>
                        {% endif %}

                        {% endif %}
                        {% endfor %}
                    </select>

                </div>
            </div>
            <div class="col ">
                <p>
                    <b>Выбор цвета для секторов:</b>
                <p>
                <div class="row justify-content-start card-header">
                    <div class="col">
                        <input type="color" id="g900" name="g900" value="#FFF70F">
                        <input type="checkbox" name="Chek[]" value="1" id="Check_g900" checked>
                        <label for="g900">GSM-900</label>
                    </div>
                    <div class="col">
                        <input type="color" id="g1800" name="g1800" value="#FF0000">
                        <input type="checkbox" name="Chek[]" value="2" id="Check_g1800" checked>
                        <label for="g1800">GSM-1800</label>
                    </div>
                    <div class="col">
                        <input type="color" id="u900" name="u900" value="#FF8800">
                        <input type="checkbox" name="Chek[]" value="3" id="Check_U900" checked>
                        <label for="u900">UMTS-900</label>
                    </div>
                    <div class="col">
                        <input type="color" id="u2100" name="u2100" value="#11FF00">
                        <input type="checkbox" name="Chek[]" value="4" id="Check_u2100" checked>
                        <label for="u2100">UMTS-2100</label>
                    </div>
                    <div class="col">
                        <input type="color" id="ubesector" name="ubesector" value="#00AAFF">
                        <input type="checkbox" name="Chek[]" value="5" id="Check_uBisec" checked>
                        <label for="ubesector">UMTS-Bisector</label>
                    </div>
                </div>
                <br>
                <div class="row justify-content-start card-header">
                    <div class="col">
                        <input type="color" id="l800" name="l800" value="#0033FF">
                        <input type="checkbox" name="Chek[]" value="6" id="Check_l800" checked>
                        <label for="l800">LTE-800</label>
                    </div>
                    <div class="col">
                        <input type="color" id="l1800" name="l1800" value="#FF00DD">
                        <input type="checkbox" name="Chek[]" value="7" id="Check_l1800" checked>
                        <label for="l1800">LTE-1800</label>
                    </div>
                    <div class="col">
                        <input type="color" id="l2600" name="l2600" value="#FFFFFF">
                        <input type="checkbox" name="Chek[]" value="8" id="Check_l2600" checked>
                        <label for="l2600">LTE-2600</label>
                    </div>
                    <div class="col">
                        <input type="color" id="l2300" name="l2300" value="#ABABAB">
                        <input type="checkbox" name="Chek[]" value="9" id="Check_2300" checked>
                        <label for="l2300">LTE-2300</label>
                    </div>
                    <div class="col">
                        <input type="color" id="l2100" name="l2100" value="#333333">
                        <input type="checkbox" name="Chek[]" value="10" id="Check_l2100" checked>
                        <label for="l2100">LTE-2100</label>
                    </div>
                </div>


            </div>
            <!--        <div class="col">-->
            <!--            <b>Номер Бисектора:</b>-->
            <!--            <div class="card-header"> {{object.besecnum}}</div>-->
            <!--        </div>-->
        </div>
        <br>
    </div>
    <br>
    <button class="btn btn-primary" type="submit">Создать KMZ файл</button>
    <a href="{% url 'bsview' %}" class="btn btn-primary">Отмена</a>
</form>
<!--<a href="{% url 'getkmz' %}" class="btn btn-primary">Создать KMZ</a>-->
<!--<input class="btn btn-primary" type="button" value="Отмена" onclick="javascript:history.go(-1);">-->

</body>
{% endblock %}

