import pandas as pd
from openpyxl.reader.excel import load_workbook
from django.contrib.auth.decorators import login_required
from django.shortcuts import render, redirect
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import ListView, DetailView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.core.paginator import Paginator
from django.db import connection
from datetime import timedelta
from datetime import datetime
import bsview.models
from .forms import *
from django.http import HttpResponse
from .kmz import *
from .bts2 import *
from .bts3 import *
from .bts4 import *
from .gnet import *
from .reports import *
import datetime as dt

import os
import zipfile
from io import BytesIO
from transliterate import translit


class BsPageView(LoginRequiredMixin, ListView):
    model = BsBeeline
    template_name = 'bsview.html'
    paginate_by = 30

    # bss = Bsbeeline.objects.filter(region='Andijan')
    def get_queryset(self):
        # if self.request.user.privilege_id != 1:
        #     return BsBeeline.objects.filter(region=self.request.user.region)
        # else:
        return BsBeeline.objects.filter(region=self.request.user.region)


class BsAlarmView(LoginRequiredMixin, ListView):
    model = bsview.models.Current_Alarms
    template_name = 'alarms.html'
    paginate_by = 50

    # print(str(bsview.models.Log_Alarms.objects.all().query))
    def get_queryset(self):
        # print(self.request.user.region)
        regionUzb = RegionUzb.objects.get(name=self.request.user.region)
        bscrnc = (regionUzb.bsc + ',' + regionUzb.rnc).split(",")
        # if self.request.user.privilege_id != 1:
        return bsview.models.Current_Alarms.objects.filter(bscrnc__in=bscrnc)

        # else:
        #    return bsview.models.Current_Alarms.objects.all()


def getBsfromRegion(region_id):
    bsnumlist = [];
    allVar = bsview.models.BsBeeline.objects.filter(region_id=region_id)
    for vars in allVar:
        bsnumlist.append(vars.bsnum)
    return bsnumlist


class BsAlarmViewAll(LoginRequiredMixin, ListView):
    model = bsview.models.Log_Alarms_full
    template_name = 'alarmsall.html'
    paginate_by = 50

    def get_queryset(self):
        return bsview.models.Log_Alarms_full.objects.filter(
            bsnumber__in=getBsfromRegion(self.request.user.region)).order_by("alarmname", "appeartime")


class BsAlarmViewAllSelect(LoginRequiredMixin, ListView):
    model = bsview.models.Log_Alarms_full
    template_name = 'alarmsall.html'
    paginate_by = 50

    def get_queryset(self):
        if self.kwargs['alarms']:
            print(self.kwargs['alarms'])
            return bsview.models.Log_Alarms_full.objects.filter(idalarm=self.kwargs['alarms'],
                                                                bsnumber__in=getBsfromRegion(
                                                                    self.request.user.region)).order_by("appeartime")


class AlarmRegionViewAll(LoginRequiredMixin, ListView):
    model = bsview.models.Log_Alarms_full
    template_name = 'alarmregionviewall.html'
    paginate_by = 50

    def get_queryset(self):
        if self.kwargs['region']:
            return bsview.models.Log_Alarms_full.objects.filter(
                bsnumber__in=getBsfromRegion(self.kwargs['region'])).order_by("alarmname", "appeartime")


class AlarmRegionView(LoginRequiredMixin, ListView):
    model = bsview.models.Current_Alarms
    template_name = 'alarmregionview.html'
    paginate_by = 50

    def get_queryset(self):
        if self.kwargs['region']:
            regionUzb = RegionUzb.objects.get(id=self.kwargs['region'])
            bscrnc = (regionUzb.bsc + ',' + regionUzb.rnc).split(",")
            # print(bscrnc)
            return bsview.models.Current_Alarms.objects.filter(bscrnc__in=bscrnc)


class AlarmDeleteView(LoginRequiredMixin, DeleteView):
    model = bsview.models.Current_Alarms
    template_name = 'alm_delete.html'
    success_url = reverse_lazy('alarms')


class AlarmDeleteViewAll(LoginRequiredMixin, DeleteView):
    model = bsview.models.Log_Alarms_full
    template_name = 'almall_delete.html'
    success_url = reverse_lazy('alarmsall')


class BsUpdate(LoginRequiredMixin, UpdateView):
    model = BsBeeline
    template_name = 'updatebs.html'
    form_class = UpdForm


class BsRegionView(LoginRequiredMixin, ListView):
    model = BsBeeline
    template_name = 'bsregionview.html'
    paginate_by = 20

    # bss = BsBeeline.objects.filter(region='Andijan')
    def get_queryset(self):
        if self.kwargs['region']:
            return BsBeeline.objects.filter(region=self.kwargs['region'])


class BsDetailView(LoginRequiredMixin, DetailView, UpdateView):
    model = BsBeeline
    form_class = UpdForm
    template_name = 'viewdetail.html'


class BsDeleteView(LoginRequiredMixin, DeleteView):
    model = BsBeeline
    template_name = 'bs_delete.html'
    success_url = reverse_lazy('bsview')


@login_required
def search_page1(request):
    bsname = request.POST['bsname']
    if request.user.privilege_id == 1 or request.user.privilege_id == 4:
        return render(request, 'search.html', {'baza': BsBeeline.objects.filter(bsname__icontains=bsname)})
    else:
        return render(request, 'search.html',
                      {'baza': BsBeeline.objects.filter(bsname__icontains=bsname, region=request.user.region)})


@login_required
def search_page2(request):
    bsnumber = request.POST['bsnumber']
    if request.user.privilege_id != 1 or request.user.privilege_id != 4:
        return render(request, 'search.html', {'baza': BsBeeline.objects.filter(bsnum=bsnumber)})
    else:
        return render(request, 'search.html',
                      {'baza': BsBeeline.objects.filter(bsnum=bsnumber, region=request.user.region)})


def accessdeny(request):
    return render(request, 'access_deny.html')


def closeWindow(request):
    return render(request, 'closewindow.html')


@login_required
def addBs(request):
    if request.method == 'POST':
        form = BsForm(request.POST)
        if form.is_valid():
            # print(form.cleaned_data)
            try:
                BsBeeline.objects.create(**form.cleaned_data)
                return redirect('bsview')
            except Exception as e:
                response = "<H1> ", str(e.args)[1:40], "<h1>"
                return HttpResponse(response)


    else:
        form = BsForm
    regionid = request.GET.get('region', None)

    if regionid:
        getregion = RegionUzb.objects.get(id=regionid)

    return render(request, 'addbs.html', {'form': form})


@login_required
def editBs(request):
    if request.method == 'POST':
        form = UpdForm(request.POST)
        if form.is_valid():
            form.save()
    else:
        form = BsForm
    return render(request, 'editbs.html', {'form': form})


# AJAX
@login_required
def load_areas(request):
    region_id = request.GET.get('region')
    areas = AreaUzb.objects.filter(region_id=region_id).all()
    return render(request, 'dropdown_area.html', {'areas': areas})
    # return JsonResponse(list(cities.values('id', 'name')), safe=False)


@login_required
def createKMZ(request):
    region = RegionUzb.objects.all()
    return render(request, 'kmzcreate.html', {'region': region})


@login_required
def getKMZ(request):
    # bsnumber = request.POST['bsnumber']
    region = request.POST.get('regiono')
    reg = RegionUzb.objects.get(id=region)
    regionname = request.POST['regiono']
    g900 = "FF" + request.POST.get('g900')[5:] + request.POST.get('g900')[3:5] + request.POST.get('g900')[1:3]
    g1800 = "FF" + request.POST.get('g1800')[5:] + request.POST.get('g1800')[3:5] + request.POST.get('g1800')[1:3]
    u900 = "FF" + request.POST.get('u900')[5:] + request.POST.get('u900')[3:5] + request.POST.get('u900')[1:3]
    u2100 = "FF" + request.POST.get('u2100')[5:] + request.POST.get('u2100')[3:5] + request.POST.get('u2100')[1:3]
    ubesector = "FF" + request.POST.get('ubesector')[5:] + request.POST.get('ubesector')[3:5] + request.POST.get(
        'ubesector')[1:3]
    l800 = "FF" + request.POST.get('l800')[5:] + request.POST.get('l800')[3:5] + request.POST.get('l800')[1:3]
    l1800 = "FF" + request.POST.get('l1800')[5:] + request.POST.get('l1800')[3:5] + request.POST.get('l1800')[1:3]
    l2600 = "FF" + request.POST.get('l2600')[5:] + request.POST.get('l2600')[3:5] + request.POST.get('l2600')[1:3]
    l2300 = "FF" + request.POST.get('l2300')[5:] + request.POST.get('l2300')[3:5] + request.POST.get('l2300')[1:3]
    l2100 = "FF" + request.POST.get('l2100')[5:] + request.POST.get('l2100')[3:5] + request.POST.get('l2100')[1:3]

    Cheks = request.POST.getlist('Chek[]')

    bss = BsBeeline.objects.filter(region_id=region)
    folderpath = 'templates\kml'
    filname = f"kmlfile-{dt.datetime.now().strftime('%d-%m-%Y-%H-%M-%S')}"
    my_kml = open(f'{folderpath}\{filname}.kml', "a+", encoding="utf-8")
    for startkml in startKml(reg):
        my_kml.write(startkml)

    if "1" in Cheks:
        for g900list in createKml(reg, bss, g900, 'gsm900.png', 'GSM 900 Positions', 'Sectors GSM 900', 'gsm900'):
            my_kml.write(g900list)
    if "2" in Cheks:
        for g1800list in createKml(reg, bss, g1800, 'gsm1800.png', 'GSM 1800 Positions', 'Sectors GSM 1800', 'gsm1800'):
            my_kml.write(g1800list)
    if "3" in Cheks:
        for u900list in createKml(reg, bss, u900, 'umts900.png', 'UMTS 900 Positions', 'Sectors UMTS 900', 'umts900'):
            my_kml.write(u900list)
    if "4" in Cheks:
        for u2100list in createKml(reg, bss, u2100, 'umts2100.png', 'UMTS 2100 Positions', 'Sectors UMTS2100',
                                   'umts2100'):
            my_kml.write(u2100list)
    if "5" in Cheks:
        for ubeseclist in createKml(reg, bss, ubesector, 'ubesec.png', 'UMTS BESEC Positions', 'Sectors UMTS BESEC',
                                    'besector'):
            my_kml.write(ubeseclist)
    if "6" in Cheks:
        for l800list in createKml(reg, bss, l800, 'lte800.png', 'LTE 800 Positions', 'Sectors LTE 800', 'lte800'):
            my_kml.write(l800list)
    if "7" in Cheks:
        for l1800list in createKml(reg, bss, l1800, 'lte1800.png', 'LTE 1800 Positions', 'Sectors LTE 1800', 'lte1800'):
            my_kml.write(l1800list)
    if "8" in Cheks:
        for l2600list in createKml(reg, bss, l2600, 'lte2600.png', 'LTE 2600 Positions', 'Sectors LTE 2600', 'lte2600'):
            my_kml.write(l2600list)
    if "9" in Cheks:
        for l2300list in createKml(reg, bss, l2300, 'lte2300.png', 'LTE 2300 Positions', 'Sectors LTE 2300', 'lte2300'):
            my_kml.write(l2300list)
    if "10" in Cheks:
        for l2100list in createKml(reg, bss, l2100, 'lte2100.png', 'LTE 2100 Positions', 'Sectors LTE 2100', 'lte2100'):
            my_kml.write(l2100list)
    for endkml in endKml():
        my_kml.write(endkml)
    my_kml.close()
    # create KMZ from kml
    filenames = []
    filenames.append(f'{folderpath}\{filname}.kml')
    for dirPath, dirNames, fileName in os.walk('templates\pngfiles'):
        for flist in fileName:
            filenames.append(f'templates\pngfiles\{flist}')
    rname = translit(str(reg), 'ru', reversed=True)
    # print(rname)
    zip_filename = f'{rname}-{filname}.kmz'
    byte_data = BytesIO()
    zip_file = zipfile.ZipFile(byte_data, "w")
    for file in filenames:
        filename = os.path.basename(os.path.normpath(file))
        zip_file.write(file, filename)
    zip_file.close()
    response = HttpResponse(byte_data.getvalue(), content_type='application/zip')
    response['Content-Disposition'] = f'attachment; filename={zip_filename}'
    os.remove(f'{folderpath}\{filname}.kml')
    return response


@login_required
def createBTS(request):
    region = RegionUzb.objects.all()
    return render(request, 'btscreate.html', {'region': region})


@login_required
def createGnet(request):
    region = RegionUzb.objects.all()
    return render(request, 'gnetcreate.html', {'region': region})


@login_required
def getGnet(request):
    region = request.POST.get('regiono')
    bss = BsBeeline.objects.filter(region_id=region)
    myfile = bsview.models.ConfFile.objects.latest('id')
    # print(myfile.file2g)
    cellCreate(bss, myfile)
    # file_data = cell
    # response = HttpResponse(file_data, content_type='application/text charset=utf-8')
    # response['Content-Disposition'] = f'attachment; filename = cellfile.txt'
    # return response


@login_required
def getBTS(request):
    region = request.POST.get('regiono')
    reg = RegionUzb.objects.all()
    bss = BsBeeline.objects.filter(region_id=region)
    if request.method == "POST":
        rowsFile = []
        bsName = []
        bsnomer = []
        cellName = []
        channel = []
        bsic = []
        cid = []
        lac = []
        band = []
        tac = []
        # GSM -------------------------------------------------------------------------------------------------------
        if request.POST['inlineRadioOptions'] == 'GSM':
            file2G = request.FILES['File2G']  # .readlines()
            gen = 2
            decoded_file = file2G.read().decode('latin-1').splitlines()
            reader = csv.reader(decoded_file, delimiter=';')
            for row in reader:
                rowsFile.append(row)
            for i in range(len(rowsFile) - 1):
                cellnomer = ""
                cellnomer = rowsFile[i][5]
                bsName.append(rowsFile[i][9])
                bsnomer.append(rowsFile[i][2])
                cellName.append(cellnomer[cellnomer.rfind('_') + 1:])  # Забираем только номер Селла
                channel.append(rowsFile[i][16])
                if len(rowsFile[i][8]) == 1:
                    bsic.append("0" + rowsFile[i][8])
                else:
                    bsic.append(rowsFile[i][8])

                cid.append(rowsFile[i][7])
                lac.append(rowsFile[i][6])
            file_data = bts2Create(bss, gen, bsName, bsnomer, cellName, channel, bsic, cid, lac)
            if type(file_data) != list:
                context = {'region': reg,
                           'allError': zip(file_data['errbsname'], file_data['errbs_dp'], file_data['errbs_pk'])}
                return render(request, 'btscreate.html', context)
            else:
                response = HttpResponse(file_data, content_type='application/text charset=utf-8')
                response[
                    'Content-Disposition'] = f'attachment; filename= Bts_file-2G-{dt.datetime.now().strftime("%d-%m-%Y-%H-%M-%S")}.nbf'
                return response
        # UMTS------------------------------------------------------------------------------------------------------
        if request.POST['inlineRadioOptions'] == 'UMTS':
            file3G = request.FILES['File3G']  # .readlines()
            decoded_file = file3G.read().decode('latin-1').splitlines()
            reader = csv.reader(decoded_file, delimiter=';')
            for row in reader:
                rowsFile.append(row)
            for i in range(len(rowsFile) - 1):
                cellnomer = ""
                cellnomer = rowsFile[i][8]
                bsName.append(rowsFile[i][9])
                bsnomer.append(rowsFile[i][2])
                cellName.append(cellnomer[cellnomer.rfind('_') + 1:])  # Забираем только номер Селла
                channel.append(rowsFile[i][11])
                bsic.append(rowsFile[i][14])  # SCR
                cellid = rowsFile[i][6]
                # print(cellid)
                cid.append(rowsFile[i][4])
                # cid.append(rowsFile[i][2] + rowsFile[i][6][-1])
                # cid.append(rowsFile[i][2] + cellid[-1])

            file_data = bts3Create(bss, bsName, bsnomer, cellName, channel, cid, bsic)
            if type(file_data) != list:
                context = {'region': reg,
                           'allError': zip(file_data['errbsname'], file_data['errbs_dp'], file_data['errbs_pk'])}
                return render(request, 'btscreate.html', context)
            else:
                response = HttpResponse(file_data, content_type='application/text charset=utf-8')
                response[
                    'Content-Disposition'] = f'attachment; filename= Bts_file-3G-{dt.datetime.now().strftime("%d-%m-%Y-%H-%M-%S")}.nbf'
                return response
        # LTE -------------------------------------------------------------------------------------------------------

        if request.POST['inlineRadioOptions'] == 'LTE':
            file4G = request.FILES['File4G']
            decoded_file = file4G.read().decode('latin-1').splitlines()
            reader = csv.reader(decoded_file, delimiter=';')
            for row in reader:
                rowsFile.append(row)
            for i in range(len(rowsFile) - 1):
                cellnomer = ""
                cellnomer = rowsFile[i][6]  # Номер селла
                band.append(rowsFile[i][8])
                bsnomer.append(rowsFile[i][2])
                cellName.append(cellnomer[cellnomer.rfind('_') + 1:])  # Забираем только номер Селла
                channel.append(rowsFile[i][10])
                bsic.append(rowsFile[i][14])  # PCI
                tac.append(rowsFile[i][7])  # PCI

                cid.append(rowsFile[i][2] + rowsFile[i][5])
            file_data = bts4Create(bss, band, bsnomer, cellName, channel, cid, bsic, tac)
            if type(file_data) != list:
                context = {'region': reg,
                           'allError': zip(file_data['errbsname'], file_data['errbs_dp'], file_data['errbs_pk'])}
                return render(request, 'btscreate.html', context)
            else:
                response = HttpResponse(file_data, content_type='application/text charset=utf-8')
                response[
                    'Content-Disposition'] = f'attachment; filename= Bts_file-4G-{dt.datetime.now().strftime("%d-%m-%Y-%H-%M-%S")}.nbf'
                return response


@login_required
def createReport(request):
    region = RegionUzb.objects.all()
    return render(request, 'reportcreate.html', {'region': region})


@login_required
def getReport(request):
    file_stream = BytesIO()

    if request.method == "POST":

        all_or_no = request.POST.get('inlineRadioOptions')
        report = request.POST.get('reportRadioOptions')
        region = request.POST.get('regiono')
        print(all_or_no)
        if all_or_no == 'alluzb':
            bss = BsBeeline.objects.all()
            areaAll = RegionUzb.objects.all()
            areas = False
        else:
            bss = BsBeeline.objects.filter(region_id=region)
            areaAll = AreaUzb.objects.filter(region_id=region)
            areas = True
        if report == 'reportRadio1':
            book = reportbsAzimut(bss)
        elif report == 'reportRadio2':
            book = reportKabinet(areas, region, bss, areaAll, RegionUzb.objects.all())
        book.save(file_stream)
        response = HttpResponse(content=file_stream.getvalue(), content_type='application/ms-excel')
        # response = HttpResponse(content=save_virtual_workbook(book), content_type='application/ms-excel')
        response[
            'Content-Disposition'] = f'attachment; filename= ReportData-{dt.datetime.now().strftime("%d-%m-%Y-%H-%M-%S")}.xlsx'
        return response


from django.shortcuts import render
from django.db.models import Count
from .models import RegionUzb, BsBeeline, Current_Alarms

@login_required
def idlebts(request):

    regions = RegionUzb.objects.annotate(
        bs_count=Count('bsbeeline', distinct=False)
    )

    # Создаем словарь для хранения количества alarm_count и unique_bsnumber для каждого региона
    alarm_count_dict = {region.id: 0 for region in regions}
    unique_bsnumber_dict = {region.id: set() for region in regions}

    # Получаем количество записей от Current_Alarms, которые соответствуют regionUzb.bsc или regionUzb.rnc
    current_alarms = Current_Alarms.objects.all()
    for alarm in current_alarms:
        for region in regions:
            bsc_list = region.bsc.split(',')
            rnc_list = region.rnc.split(',')
            if alarm.bscrnc in bsc_list or alarm.bscrnc in rnc_list:
                alarm_count_dict[region.id] += 1
                unique_bsnumber_dict[region.id].add(alarm.bsnumber)

    total_bs_count = 0
    total_alarm_count = 0
    total_unique_bsnumber = 0

    for region in regions:
        region.alarm_count = alarm_count_dict[region.id]
        region.unique_bsnumber = len(unique_bsnumber_dict[region.id])
        region.bs_percentage = round((region.unique_bsnumber / region.bs_count) * 100, 2) if region.bs_count > 0 else 0
        total_bs_count += region.bs_count
        total_alarm_count += region.alarm_count
        total_unique_bsnumber += region.unique_bsnumber

    return render(request, 'allidlebts.html', {'region': regions,
                                               'total_bs_count' : total_bs_count,
                                               'total_alarm_count' : total_alarm_count,
                                               'total_unique_bsnumber' : total_unique_bsnumber,
                                               'total_bs_percentage': round(
                                                   (total_unique_bsnumber / total_bs_count) * 100,
                                                   2) if total_bs_count > 0 else 0,
                                               })


@login_required
def alarmview(request):
    region = RegionUzb.objects.all()
    return render(request, 'alarmview.html', {'region': region})


@login_required
def getalarm(request):
    file_stream = BytesIO()
    if request.method == "POST":
        region = request.POST.get('regiono')
        alarm_type = request.POST.get('inlineRadioOptions')
        dates = request.POST.get('daterange')

        # print(dates, region, alarm_type)
        dat1 = datetime.datetime(year=int(dates[6:10]), month=int(dates[3:5]), day=int(dates[0:2]), second=1)
        dat2 = datetime.datetime(year=int(dates[19:23]), month=int(dates[16:18]), day=int(dates[13:15]), hour=23,
                                 minute=59, second=59)
        # print(dat1)
        # print(dat2)
        # print(dat2-dat1)
        regionUzb = RegionUzb.objects.get(id=region)
        bscrnc = (regionUzb.bsc + ',' + regionUzb.rnc).split(",")

        if alarm_type == "oml":
            alarms = bsview.models.Log_Alarms.objects.filter(bscrnc__in=bscrnc, alarmname="OML Fault",
                                                             appeartime__gte=dat1, appeartime__lte=dat2)

            book = reportAlarmOml(alarms, len(alarms), getRegion(region_id=region).name, dat1, dat2, alarm_type)

        elif alarm_type == "nodeb":
            alarms = bsview.models.Log_Alarms.objects.filter(bscrnc__in=bscrnc, alarmname="NodeB Unavailable",
                                                             appeartime__gte=dat1, appeartime__lte=dat2)
            book = reportAlarmOml(alarms, len(alarms), getRegion(region_id=region).name, dat1, dat2, alarm_type)

        elif alarm_type == "full":
            alarms = bsview.models.Log_Alarms_arxiv.objects.filter(bscrnc__in=bscrnc,
                                                                   appeartime__gte=dat1, appeartime__lte=dat2)
            book = reportAlarmFull(alarms, len(alarms), getRegion(region_id=region).name, dat1, dat2)
        book.save(file_stream)
        response = HttpResponse(content=file_stream.getvalue(), content_type='application/ms-excel')
        response[
            'Content-Disposition'] = f'attachment; filename= ReportData-{dt.datetime.now().strftime("%d-%m-%Y-%H-%M-%S")}.xlsx'
        return response


def getRegion(region_id):
    return bsview.models.RegionUzb.objects.get(id=region_id)


def CheckAreaId(areaName):
    try:
        bsview.models.AreaUzb.objects.get(name=areaName)
        return False
    except:
        print(areaName)
        return True


def getAreaId(areaName):
    return bsview.models.AreaUzb.objects.get(name=areaName)


@login_required
def alarmstat(request):
    region = RegionUzb.objects.all()
    alarms = bsview.models.Alarms.objects.all()
    return render(request, 'alarmstat.html', {'region': region, 'alarms': alarms})


@login_required
def searchAlarm(request):
    alms = bsview.models.Alarms.objects.all()
    if request.method == "POST":
        region = request.user.region
        alarm_type = request.POST.get('select_id')
        dates = request.POST.get('daterange')
        dat1 = datetime.datetime(year=int(dates[6:10]), month=int(dates[3:5]), day=int(dates[0:2]), second=1)
        dat2 = datetime.datetime(year=int(dates[19:23]), month=int(dates[16:18]), day=int(dates[13:15]), hour=23,
                                 minute=59, second=59)
        dateval1 = dates[3:5] + "/" + dates[0:2] + "/" + dates[6:10]
        dateval2 = dates[16:18] + "/" + dates[13:15] + "/" + dates[19:23]
        bscrnc = (region.bsc + ',' + region.rnc).split(",")
        bsnomer = request.POST.get('bsnumber')
        day = dat2 - dat1
        if int(day.days) + 1 > 31:
            return HttpResponse("<h1>Days are more than 31 </h1> <a href='alarmstat.html'' alt='Назад'>")
        else:
            if alarm_type == "21825":
                if request.user.privilege_id != 1:
                    alarms = bsview.models.Log_Alarms.objects.filter(bscrnc__in=bscrnc, alarmname="OML Fault",
                                                                     bsnumber=bsnomer, appeartime__gte=dat1,
                                                                     appeartime__lte=dat2)
                else:
                    alarms = bsview.models.Log_Alarms.objects.filter(alarmname="OML Fault",
                                                                     bsnumber=bsnomer, appeartime__gte=dat1,
                                                                     appeartime__lte=dat2)
            elif alarm_type == "22214":
                if request.user.privilege_id != 1:
                    alarms = bsview.models.Log_Alarms.objects.filter(bscrnc__in=bscrnc, alarmname="NodeB Unavailable",
                                                                     bsnumber=bsnomer, appeartime__gte=dat1,
                                                                     appeartime__lte=dat2)
                else:
                    alarms = bsview.models.Log_Alarms.objects.filter(alarmname="NodeB Unavailable",
                                                                     bsnumber=bsnomer, appeartime__gte=dat1,
                                                                     appeartime__lte=dat2)

            elif alarm_type == "00000":
                if request.user.privilege_id != 1:
                    alarms = bsview.models.Log_Alarms_arxiv.objects.filter(bscrnc__in=bscrnc,
                                                                           bsnumber=bsnomer, appeartime__gte=dat1,
                                                                           appeartime__lte=dat2)
                else:
                    alarms = bsview.models.Log_Alarms_arxiv.objects.filter(bsnumber=bsnomer, appeartime__gte=dat1,
                                                                           appeartime__lte=dat2)

            else:
                if request.user.privilege_id != 1:
                    alarms = bsview.models.Log_Alarms_arxiv.objects.filter(bscrnc__in=bscrnc, idalarm=alarm_type,
                                                                           bsnumber=bsnomer, appeartime__gte=dat1,
                                                                           appeartime__lte=dat2)
                else:
                    alarms = bsview.models.Log_Alarms_arxiv.objects.filter(idalarm=alarm_type,
                                                                           bsnumber=bsnomer, appeartime__gte=dat1,
                                                                           appeartime__lte=dat2)

            kolAlm = len(alarms)
            sumTime = datetime.timedelta(seconds=0)
            if alarm_type == "21825" or alarm_type == "22214" \
                    or alarm_type == "65033" or alarm_type == "29201" \
                    or alarm_type == "26529" or alarm_type == "26594" \
                    or alarm_type == "26234" and len(alarms) > 0:
                for al in alarms:
                    sumTime = sumTime + al.downtime

            paginator = Paginator(alarms, 100)
            page_number = request.GET.get('page', 1)
            posts = paginator.page(page_number)

            return render(request, "searchalarms.html", {"page_obj": posts, "bsnumber": bsnomer,
                                                         'select_id': alarm_type, "dat": dates, 'alms': alms,
                                                         'kolAlm': kolAlm, 'sumTime': sumTime,
                                                         'dateval1': dateval1, 'dateval2': dateval2})


@login_required
def importData(request):
    region = RegionUzb.objects.all()
    return render(request, 'import.html', {'region': region})


@login_required
def getfromexcel(request):
    region = request.POST.get('regiono')
    if request.method == "POST":
        fileExcel = request.FILES['FileExcel']
        workbook = load_workbook(fileExcel)
        sheet = workbook.active
        errorbs = []
        errorarea = []
        error = False
        for row in sheet.iter_rows(min_row=2):
            if CheckAreaId(row[20].value):
                errorbs.append(row[1].value)
                errorarea.append(row[20].value)
                error = True
        if error:
            return render(request, 'bs_import.html', {'error': error, 'allError': zip(errorbs, errorarea)})
        else:
            with connection.cursor() as cursor:
                cursor.execute("DELETE FROM bsview_bsbeeline WHERE region_id = '" + region + "'")
                cursor.fetchone()  # получаем одну строку
                for row in sheet.iter_rows(min_row=2):
                    cursor.execute(f"INSERT INTO bsview_bsbeeline("
                                   f"bsname, bsnum, besecnum, lat, lon, modem, address, gsm900, gsm1800, gsmbi1800, umts900, "
                                   f"umts2100, umtsbesec, lte800, lte1800, ltebi1800, lte2600, lte2300, lte2100, "
                                   f"area_id, region_id, author_id)"
                                   f" VALUES ('{row[1].value}', '{row[2].value}', '{row[3].value}', '{row[4].value}'"
                                   f", '{row[5].value}', '{row[18].value}', '{row[19].value}', '{row[6].value}', '{row[7].value}'"
                                   f", '{row[8].value}', '{row[9].value}', '{row[10].value}', '{row[11].value}', '{row[12].value}'"
                                   f", '{row[13].value}', '{row[14].value}', '{row[15].value}', '{row[16].value}', '{row[17].value}'"
                                   f", '{getAreaId(row[20].value).id}', '{region}', '{request.user.id}');")
                    cursor.fetchone()
            return render(request, 'bs_import.html', {'error': error, 'message': "Обновление данных выполнена успешно"})


def conf_files(request):
    files = bsview.models.ConfFile.objects.all().order_by('-dat').values()
    return render(request, 'conf_file.html', {'files': files})


@login_required
def analyze_alarm(request):
    region = RegionUzb.objects.all()
    return render(request, 'alarmanalyze.html', {'region': region})


@login_required
def alarm_analyze_view(request):
    alms = bsview.models.Alarms.objects.all()
    if request.method == "POST":
        region = request.user.region
        bsnomer = request.POST.get('bsnumber')
        dates = request.POST.get('daterange')
        start_date = datetime.datetime.strptime(f"{dates[6:10]}-{dates[3:5]}-{dates[0:2]}", "%Y-%m-%d")
        end_date = datetime.datetime.strptime(f"{dates[19:23]}-{dates[16:18]}-{dates[13:15]}", "%Y-%m-%d")
        D = 'D'
        date_list = pd.date_range(start_date, end_date, freq=D)
        print(start_date, end_date)
        dateval1 = dates[3:5] + "/" + dates[0:2] + "/" + dates[6:10]
        dateval2 = dates[16:18] + "/" + dates[13:15] + "/" + dates[19:23]
        # bscrnc = (region.bsc + ',' + region.rnc).split(",")
        # bsnomer = request.POST.get('bsnumber')
        # day = dat2 - dat1
        hours = []
        for i in range(24):
            hours.append(format(str(i).zfill(2)))

    return render(request, "alarmanalyzeview.html",
                  {'date_list': date_list, 'start_date': dateval1, 'end_date': dateval2, 'hours': hours,
                   'bsnomer': bsnomer})


@login_required
def analyze_alarm_day(request):
    region = RegionUzb.objects.all()
    return render(request, 'alarmanalyzeday.html', {'region': region})


@login_required
def alarm_analyze_view_day(request):
    region = RegionUzb.objects.all()
    if request.method == "POST":
        # reg = BsBeeline.objects.get(region='1')
        regionUzb = RegionUzb.objects.get(id=request.POST.get('regiono'))
        bscrnc = (regionUzb.bsc + ',' + regionUzb.rnc).split(",")
        dates = request.POST.get('datepicker')
        start_time = datetime.datetime.strptime(f"{dates[6:10]}-{dates[3:5]}-{dates[0:2]}", "%Y-%m-%d")
        end_time = start_time + timedelta(hours=23, minutes=59)
        print(start_time, " ", end_time, " ", bscrnc)
        # alarms = bsview.models.Log_Alarms_arxiv.objects.filter(bscrnc__in=bscrnc, idalarm="65033")
        req_region = request.POST.get('regiono')
        print(req_region)
        alarms = bsview.models.Log_Alarms_arxiv.objects.filter(appeartime__gte=start_time, appeartime__lte=end_time, \
                                                               bsnumber__in=BsBeeline.objects.filter(
                                                                   region=req_region).values('bsnum'), \
                                                               idalarm="65033")
        dat = dates[3:5] + "/" + dates[0:2] + "/" + dates[6:10]
        for alarm in alarms:
            print(alarm.bsname, alarm.alarmname, alarm.appeartime, alarm.cleartime)

    return render(request, "alarmanalyzeviewday.html", {'region': region, "dat": dat, 'alarms': alarms})
