{% extends 'base.html' %}
{% load static %}


{% block content %}

<div class="jumbotron">
    <p>
</div>
<body>
<form action="{% url 'getbts' %}" method="post" id="getbtsform" enctype="multipart/form-data">
    {% csrf_token %}

    <div class="container card ">
        <div class="row ">
            <div class="col col-3">
                <p>
                    <b>Выберите область:</b>
                <p>
                <div class="card-header w-100 p-3">
                    <select class="form-select" multiple aria-label="multiple select example" name="regiono" id="reg">
                        {% for reg in region %}
                        {% if reg.id == user.region_id %}
                        <option selected value="{{ reg.id }}">{{reg.name}}</option>
                        {% else %}
                        {% if user.privilege_id == 1 %}
                        <option value="{{ reg.id }}">{{reg.name}}</option>
                        {% else %}
                        <option value="{{ reg.id }}" disabled>{{reg.name}}</option>
                        {% endif %}

                        {% endif %}
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="col ">

                <p><b>Выберите диапазон:</b></p>
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="inlineRadioOptions" id="inlineRadio1"
                           value="GSM" onclick="change()" checked>
                    <label class="form-check-label" for="inlineRadio1">GSM</label>
                </div>
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="inlineRadioOptions" id="inlineRadio2"
                           value="UMTS" onclick="change2()">
                    <label class="form-check-label" for="inlineRadio2">UMTS</label>
                </div>
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="inlineRadioOptions" id="inlineRadio3"
                           value="LTE" onclick="change3()">
                    <label class="form-check-label" for="inlineRadio3">LTE</label>
                </div>
                <br>
                <br>
                <p>
                    <b id="vibor">Выбор конфигурационного файла 2G:</b>
                    <br>

                <p>
                <div class="input-group mb-3">
                    <label class="input-group-text" for="Filevibor" id="vid">Конф. файл 2G</label>
                    <input type="file" class="form-control" id="Filevibor" name="File2G" required>
                    <label class="input-group-text" for="Filevibor">Загрузка</label>
                </div>

                <p>
                    <!--                <div class="input-group mb-3">-->
                    <!--                    <label class="input-group-text" for="File3g">Конф. файл 3G</label>-->
                    <!--                    <input type="file" class="form-control" id="File3g" name="File3G">-->
                    <!--                    <label class="input-group-text" for="File3g">Загрузка</label>-->
                    <!--                </div>-->
                    <!--                <p>-->
                    <!--                <div class="input-group mb-3">-->
                    <!--                    <label class="input-group-text" for="File4G">Конф. файл 4G</label>-->
                    <!--                    <input type="file" class="form-control" id="File4G" name="File4G">-->
                    <!--                    <label class="input-group-text" for="File4G">Загрузка</label>-->
                    <!--                </div>-->


            </div>
            <!--        <div class="col">-->
            <!--            <b>Номер Бисектора:</b>-->
            <!--            <div class="card-header"> {{object.besecnum}}</div>-->
            <!--        </div>-->
        </div>
        <br>
    </div>
    <br>
    <button class="btn btn-primary" type="submit" id="knopka">Создать BTS файл для 2G</button>
    <!--    <input type="submit" value="Upload" name="pmUpload" id="pmUpload" class="button">-->
    <a href="{% url 'bsview' %}" class="btn btn-primary">Отмена</a>
</form>
<!--<a href="{% url 'getkmz' %}" class="btn btn-primary">Создать KMZ</a>-->
<!--<input class="btn btn-primary" type="button" value="Отмена" onclick="javascript:history.go(-1);">-->
<br>
{% if allError %}
<table class="table table-striped">
    <tr>
        <td width="10%">№</td>
        <td width="50%">Ошибка</td>
        <td width="20%">CELL_ID</td>
        <td width="20%">Изменить</td>
    </tr>

    {% for errbsname, errbs_dp, errbs_pk in allError %}

    <tr>
    <td >{{ forloop.counter }}</td>
        <td>Базе данных не указаны сектора для диапазона {{ errbs_dp }} </td>
        <td>{{ errbsname }}</td>
        <td><a href="{% url 'viewdetail' errbs_pk %}">Просмотр</a></td>
<!--        <td><a href="{% url 'bsedit' errbs_pk %}" target ="_blank">Изменить</a></td>-->

    </tr>
    {% endfor %}
</table>
{% endif %}

<script>
    const vid = document.getElementById("vid");
    const vibor = document.getElementById("vibor");
    const knopka = document.getElementById("knopka");
    const filevibor = document.getElementById("Filevibor");
    const change = () => {
          vid.innerText = "Конф. Файл 2G"
          vibor.innerText = "Выбор конфигурационного файла 2G"
          knopka.innerText = "Создать BTS файл для 2G"
          filevibor.setAttribute("name", "File2G");

    };
    const change2 = () => {
         vid.innerText = "Конф. Файл 3G"
         vibor.innerText = "Выбор конфигурационного файла 3G"
         knopka.innerText = "Создать BTS файл для 3G"
         filevibor.setAttribute("name", "File3G");
    };
    const change3 = () => {
         vid.innerText = "Конф. Файл 4G"
         vibor.innerText = "Выбор конфигурационного файла 4G"
         knopka.innerText = "Создать BTS файл для 4G"
         filevibor.setAttribute("name", "File4G");
    };







</script>


</body>
{% endblock %}

