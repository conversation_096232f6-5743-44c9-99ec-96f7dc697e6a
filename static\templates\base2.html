<!DOCTYPE html>
{% load static %}

{% block extrahead %}
<script>window.CKEDITOR_BASEPATH = '/static/ckeditor/ckeditor/';</script>
{{ block.super }}
{% endblock %}

<html lang="en">
<head>
    <meta charset="UTF-8">
    <title> {% block title %}Beeline-BS Uzbekistan {% endblock title %} </title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="{% static 'css/bootstrap.min.css' %}" rel="stylesheet">
    <link type="image/x-icon" href="{% static 'img/signal.ico' %}" rel="shortcut icon">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background-color: #f8f9fa;
        }
		
		.dropdown-menu {
    background-color: #343a40 !important;
}

.dropdown-menu .dropdown-item {
    color: #fff !important;
}

.dropdown-menu .dropdown-item:hover {
    background-color: #ffc107 !important;
    color: #343a40 !important;
}
        .navbar {
            background-color: #343a40;
        }
        .navbar-brand b {
            color: #ffc107;
        }
        .navbar-nav .nav-link {
            color: #fff;
            transition: color 0.3s, background-color 0.3s;
            min-width: 120px; /* Set minimum width */
            text-align: center; /* Center text */
        }
        .navbar-nav .nav-link:hover {
            color: #343a40;
            background-color: #ffc107;
        }
        .dropdown-menu {
            background-color: #343a40;
        }
        .dropdown-menu .dropdown-item {
            color: #fff;
            min-width: 150px; /* Set minimum width */
            text-align: left; /* Center text */
        }
        .dropdown-menu .dropdown-item:hover {
            background-color: #ffc107;
            color: #343a40;
        }
        .dropdown-menu .dropdown-item.active,
        .dropdown-menu .dropdown-item:active,
        .dropdown-menu .dropdown-item:focus {
            background-color: #343a40; /* Keep background color */
            color: #fff; /* Keep text color */
        }
        .btn-outline-primary {
            color: #ffc107;
            border-color: #ffc107;
        }
        .btn-outline-primary:hover {
            background-color: #ffc107;
            color: #343a40;
        }
        .dropdown:hover .dropdown-menu {
            display: block;
            margin-top: 0;
        }



    </style>
</head>

<body>
<nav class="navbar navbar-expand-lg navbar-dark">
    <div class="container-fluid">
        <img src="{% static 'img/logo2.png' %}" alt="Logo">
        <a class="navbar-brand" href="{% url 'bsview' %}"><b>Beeline-BS-UZ</b></a>
        {% if user.is_authenticated %}
        <ul class="navbar-nav">
            <li class="nav-item">
                <a class="nav-link active" aria-current="page" href="{% url 'bsview' %}">Список БС</a>
            </li>
            {% if user.privilege_id == 1 or user.privilege_id == 2 %}
            <li class="nav-item">
                <a class="nav-link" href="{% url 'addbs' %}">Добавить БС</a>
            </li>
            {% endif %}
            <li class="nav-item">
                <a class="nav-link" href="{% url 'createreport' %}">Разные Отчеты</a>
            </li>
            {% if user.privilege_id == 1 or user.privilege_id == 2 %}
            <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" id="alarmsDropdown1" role="button"
                   data-bs-toggle="dropdown" aria-expanded="false">
                    Алармы
                </a>
                <ul class="dropdown-menu" aria-labelledby="alarmsDropdown">
                    <li><a class="dropdown-item" href="{% url 'alarms' %}">Актив Алармы</a></li>
                    <li><a class="dropdown-item" href="{% url 'alarmsall' %}">Все Актив Алармы</a></li>
                    {% if user.privilege_id == 1 %}
                    <li><a class="dropdown-item" href="{% url 'idlebts' %}">Аварии по областям</a></li>
                    {% endif %}
                    <li><a class="dropdown-item" href="{% url 'alarmstat' %}">Аларм истории</a></li>
                    <li><a class="dropdown-item" href="{% url 'alarmview' %}">Выгрузка истории</a></li>
                    <li><a class="dropdown-item" href="{% url 'alarmanalyze' %}">Анализ Аварий по БС</a></li>
                    <li><a class="dropdown-item" href="{% url 'alarmanalyzeday' %}">Анализ Аварий по Дате</a></li>
                </ul>
            </li>
            <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" id="alarmsDropdown2" role="button"
                   data-bs-toggle="dropdown" aria-expanded="false">
                    БТС файлы
                </a>
                <ul class="dropdown-menu" aria-labelledby="alarmsDropdown">
                    <li><a class="dropdown-item" href="{% url 'createbts' %}">Делаем файл BTS для Nemo</a></li>
                    <li><a class="dropdown-item" href="{% url 'createkmz' %}">Делаем KMZ файл для Google Earth</a></li>
                    <li><a class="dropdown-item" href="{% url 'createGnet' %}">Cell файл для G-NetTrack</a></li>
                </ul>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'conffiles' %}"><b>Conf. файлы</b></a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'importdata' %}">Импорт данных</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'nriview' %}">Данные с NRI</a>
            </li>

            <li class="nav-item">
                <a class="nav-link" href="{% url 'mapview' %}">MAP view</a>
            </li>


            {% endif %}
            {% if user.privilege_id == 1 %}
            <li class="nav-item">
                <a class="nav-link" href="{% url 'users' %}">Пользователи</a>
            </li>
            {% endif %}
        </ul>
        {% endif %}

        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNavDropdown"
                aria-controls="navbarNavDropdown" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNavDropdown">
            {% if user.is_authenticated %}
            <ul class="navbar-nav ms-auto">
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown"
                       aria-expanded="false">
                        {{user.first_name}} {{user.last_name}}
                    </a>
                    <ul class="dropdown-menu">
                        {% if user.privilege_id == 1 %}
                        <li><a class="dropdown-item" href="{% url 'signup' %}">Добавить пользователя</a></li>
                        {% endif %}
                        <li><a class="dropdown-item" href="{% url 'changepassword' %}">Смена пароля</a></li>
                        <li><a class="dropdown-item" href="{% url 'logout' %}">Выход</a></li>
                    </ul>
                </li>
            </ul>
            {% else %}
            <!--      <form class="form-inline ms-auto">
                     <a class="btn btn-outline-primary" href="{% url 'login' %}">Вход</a>
                 </form> -->
            {% endif %}
        </div>
    </div>
</nav>

<main>
    <div class="container-xl">
        {% block content %}
        {% endblock content %}
    </div>
</main>

<script src="{% static 'js/bootstrap.bundle.min.js' %}"></script>
</body>
</html>