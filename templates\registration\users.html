{% extends 'base.html' %}
{% load static %}

{% block content %}

<div class="jumbotron">
    <p>
</div>
<body>
<table class="table table-striped">
    <tr>
        <td width="4"> № </td>
        <td width="25%">
            <form action="{% url 'search_page1' %}" method="post">
                {% csrf_token %}
                <div class="input-group">
                    <input type="text" class="form-control" placeholder="Имя пользователя"
                           aria-label="Имя Пользователя" aria-describedby="button-addon1" name="username">
                    <button class="btn btn-outline-secondary" type="submit" id="button-addon1">Поиск</button>
                </div>
            </form>
        </td>
        <td width="25%">ФИО Сотрудника</td>
        <td width="25%">Регион</td>
        <td width="10%">Telegram ID</td>
        <td width="15%">Привилегии</td>
        <td><img src="{% static 'img/trash1.png' %}"></Td>
        <td><img src="{% static 'img/file.png' %}"></Td>
    </TR>

    {% for item in object_list %}

    <tr>
        <td>{{ forloop.counter0|add:page_obj.start_index }}</td>
        <td><a href="{% url 'userschange' item.pk %}">{{ item.username }}</a></td>
        <td>{{ item.first_name}} {{ item.last_name }}</td>
        <td>{{ item.region }}</td>
        <td>{{ item.telegram_id }}</td>
        <td>{{ item.privilege }}</td>
        <td><a href="{% url 'userdelete' item.pk %}"><img src="{% static 'img/trash1.png' %}"  alt="Удалить"></a></td>
        <td><a href="{% url 'userschange' item.pk %}"><img src="{% static 'img/file.png' %}"  alt="Редактировать"></a></td>


    </tr>
    {% endfor %}
</table>
<div class="pagination">
    <span class="step-links">
        {% if page_obj.has_previous %}
            <a href="?page=1">&laquo; first</a>
            <a href="?page={{ page_obj.previous_page_number }}">previous</a>
        {% endif %}

        <span class="current">
            Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}.
        </span>

        {% if page_obj.has_next %}
            <a href="?page={{ page_obj.next_page_number }}">next</a>
            <a href="?page={{ page_obj.paginator.num_pages }}">last &raquo;</a>
        {% endif %}
    </span>
</div>
</body>
{% endblock content %}

