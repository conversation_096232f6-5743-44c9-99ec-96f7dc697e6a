{% extends 'base.html' %}
{% load static %}


{% block content %}

<div class="jumbotron">
    <p>
</div>
<body>
<form action="{% url 'getfromexcel' %}" method="post" id="getfromexcel" enctype="multipart/form-data">
    {% csrf_token %}

    <div class="container card ">
        <div class="row ">
            <div class="col col-3">
                <p>
                    <b>Выберите область:</b>
                <p>
                <div class="card-header w-100 p-3">
                    <select class="form-select" multiple aria-label="multiple select example" name="regiono" id="reg">
                        {% for reg in region %}
                        {% if reg.id == user.region_id %}
                        <option selected value="{{ reg.id }}">{{reg.name}}</option>
                        {% else %}
                        {% if user.privilege_id == 1 %}
                        <option value="{{ reg.id }}">{{reg.name}}</option>
                        {% else %}
                        <option value="{{ reg.id }}" disabled>{{reg.name}}</option>
                        {% endif %}

                        {% endif %}
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="col ">


                <div class="form-check form-check-inline">

                </div>
                <div class="form-check form-check-inline">

                </div>
                <div class="form-check form-check-inline">

                </div>
                <br>
                <br>
                <p>
                    <b id="vibor">Выбор Excel файла с азимутами:</b>
                    <br>

                <p>
                <div class="input-group mb-3">
                    <label class="input-group-text" for="Filevibor" id="vid">Excel файл с азимутами</label>
                    <input type="file" accept=".xlsx, .xls" class="form-control" id="Filevibor" name="FileExcel" required>
                    <label class="input-group-text" for="Filevibor">Загрузка</label>
                </div>

                <p>
            </div>
        </div>
        <br>
    </div>
    <br>
<!-- Button trigger modal -->
<button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#exampleModal">Импортировать</button>
    <a href="{% url 'bsview' %}" class="btn btn-primary">Отмена</a>
    <!-- Modal -->
<div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">Подтверждение ... </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        Вы действительно хотите обновить данные?
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Нет</button>
<!--        <button type="button" class="btn btn-primary" type="submit" id="knopka1">Да</button>-->
          <button class="btn btn-primary" type="submit" id="knopka1">Импортировать данные</button>
      </div>
    </div>
  </div>
</div>

    <!--    <button class="btn btn-primary" type="submit" id="knopka1">Импортировать данные</button>-->
<!--    <a href="{% url 'bsview' %}" class="btn btn-primary">Отмена</a>-->
</form>
<!--<a href="{% url 'getkmz' %}" class="btn btn-primary">Создать KMZ</a>-->
<!--<input class="btn btn-primary" type="button" value="Отмена" onclick="javascript:history.go(-1);">-->
<br>

</body>
{% endblock %}

