##########################


API_KEY = "AIzaSyBNOsJcoqfSRrNyz5Z0H3UsoM7oA-32rX0"

def relef_view(request):
    """
    <PERSON><PERSON><PERSON> sa<PERSON> - front-endda Excel yuklash formasi, JS bilan natija ko'rsatish.
    `templates/app/index.html` ni render qiladi.
    """

    return render(request, 'elevation.html')


# def get_elevation(lat, lon, retries=3, delay=2):
#     api_key = "AIzaSyBNOsJcoqfSRrNyz5Z0H3UsoM7oA-32rX0"  # ваш ключ Elevation API
#     api_url = f"https://maps.googleapis.com/maps/api/elevation/json?locations={lat},{lon}&key={api_key}"
#     for attempt in range(retries):
#         try:
#             response = requests.get(api_url)
#             response.raise_for_status()  # Проверка статуса 200 OK
#             data = response.json()
#             # Проверка, есть ли 'results'
#             if 'results' in data and len(data['results']) > 0:
#                 return data['results'][0]['elevation']
#             return None
#         except requests.exceptions.RequestException as e:
#             # Логгирование в реальном проекте
#             if attempt < retries - 1:
#                 sleep(delay)
#             else:
#                 return None
#         except ValueError:
#             return None

# def get_elevation(lat, lon, retries=3, delay=2):
#     api_key = "AIzaSyBNOsJcoqfSRrNyz5Z0H3UsoM7oA-32rX0"
#     api_url = f"https://maps.googleapis.com/maps/api/elevation/json?locations={lat},{lon}&key={api_key}"
#     for attempt in range(retries):
#         try:
#             response = requests.get(api_url)
#             response.raise_for_status()
#             print(f"API response: {response.text}")
#             data = response.json()
#             return data['results'][0]['elevation']
#         except Exception as e:
#             print(e)
#             if attempt < retries - 1:
#                 sleep(delay)
#             else:
#                 return None
#         except ValueError as e:
#             print(f"Ошибка при обработке JSON: {e}")
#             return None
def get_elevation(lat, lon):
    api_key = "AIzaSyBNOsJcoqfSRrNyz5Z0H3UsoM7oA-32rX0"
    url = "https://maps.googleapis.com/maps/api/elevation/json"
    params = {
        "locations": f"{lat},{lon}",
        "key": api_key
    }
    result = requests.get(url, params=params).json()
    if result['status'] == 'OK':
        return result['results'][0]['elevation']
    else:
        return None




# ----- 2) View для рендеринга формы и обработки загрузки -----

def upload_excel(request):
    if request.method == 'GET':
        # Просто рендерим HTML-шаблон с формой
        return render(request, 'upload.html')

    if request.method == 'POST':
        # 1. Проверяем, пришёл ли файл
        file = request.FILES.get('file')
        if not file:
            return HttpResponse("Файл не выбран.", status=400)

        # 2. Проверяем, действительно ли это Excel
        if not (file.name.endswith('.xlsx') or file.name.endswith('.xls')):
            return HttpResponse("Требуется Excel-файл (.xlsx/.xls).", status=400)
        results =[]
        try:
            fileExcel = request.FILES['file']
            workbook = load_workbook(fileExcel)
            sheet = workbook.active
            for row in sheet.iter_rows(min_row=2):
              if row[0].value !="" or row[1] != "" or row[2] != "":
                part = []
                if row[0] != "": part.append(row[0].value)
                if row[0] != "": part.append(row[1].value)
                if row[0] != "": part.append(row[2].value)
                # print(part)
                # print(len(part))
                if len(part) != 3:
                    raise ValueError("Неверный формат 'coords' (ожидается широта долгота; азимут; дистанция).")

                coords_part = part[0].split()
                # print(coords_part)
                if len(coords_part) != 2:
                    raise ValueError("Неверный формат координат (ожидается 'lat lon').")

                lat, lon = map(float, coords_part)
                azimuth = int(part[1])
                distance = int(part[2])

                # проверяем диапазоны
                if not (-90 <= lat <= 90):
                    raise ValueError("Широта должна быть от -90 до 90.")
                if not (-180 <= lon <= 180):
                    raise ValueError("Долгота должна быть от -180 до 180.")
                if not (0 <= azimuth <= 360):
                    raise ValueError("Азимут 0..360")
                if distance < 0:
                    raise ValueError("Дистанция положительна.")

                # Вычисляем конечные координаты (без учёта кривизны Земли)
                R = 6371e3
                delta_lat = distance * math.cos(math.radians(azimuth)) / R
                delta_lon = distance * math.sin(math.radians(azimuth)) / (R * math.cos(math.radians(lat)))

                end_lat = lat + math.degrees(delta_lat)
                end_lon = lon + math.degrees(delta_lon)

                # Получаем высоты
                start_elev = get_elevation(lat, lon)
                end_elev = get_elevation(end_lat, end_lon)
                if start_elev is None or end_elev is None:
                    raise ValueError("Не удалось получить высоту для одной из точек.")

                # Разница
                diff = end_elev - start_elev
                if diff > 0:
                    diff = -abs(diff)
                else:
                    diff = abs(diff)

                results.append(diff)
                print(lat, lon, azimuth, distance, end_lat, end_lon)
            # Добавляем столбец
            sheet['Elevation Difference'] = results

            # 6. Сохраняем итоговый Excel
            out_temp = tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx')
            sheet.to_excel(out_temp.name, index=False)
            out_temp.close()

            # 7. Возвращаем пользователю файл
            with open(out_temp.name, 'rb') as f:
                response = HttpResponse(f.read(),
                                        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
                response['Content-Disposition'] = 'attachment; filename="elevation_results.xlsx"'
            return response

        except ValueError as ve:
            return HttpResponse(f"Ошибка ввода: {ve}", status=400)
        except Exception as e:
            return HttpResponse(f"Произошла ошибка: {e}", status=500)

    return HttpResponse("Method not allowed", status=405)
