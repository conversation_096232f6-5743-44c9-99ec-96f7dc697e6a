from django.shortcuts import render
import json # Понадобится для передачи данных в шаблон безопасно
from bsview.views.imports_file import *
from django.shortcuts import render
from rest_framework import viewsets, status
from rest_framework.decorators import api_view
from rest_framework.response import Response
# from functions.serializers import RegionSerializer, AreaSerializer, BaseStationSerializer, AlarmSerializer

# Frontend uchun asosiy view
def mapindex(request):
    """Asosiy sahifani ko'rsatish"""
    return render(request, 'map3.html')

# API Views
@api_view(['GET'])
def get_all_base_stations(request):
    """Barcha baza stantsiyalarini olish"""
    # Limit parametrini olish
    limit = request.query_params.get('limit')
    offset = request.query_params.get('offset', 0)

    # Barcha baza stantsiyalarini olish
    queryset = BsBeeline.objects.all()

    # Agar limit berilgan bo'lsa, faqat shu miqdordagi ma'lumotlarni olish
    if limit:
        try:
            limit = int(limit)
            offset = int(offset)
            queryset = queryset[offset:offset+limit]
        except ValueError:
            pass

    serializer = BaseStationSerializer(queryset, many=True)
    return Response(serializer.data)

@api_view(['GET'])
def get_base_stations_by_region(request, region_id):
    """Viloyat bo'yicha baza stantsiyalarini olish"""
    base_stations = BsBeeline.objects.filter(region_id=region_id)
    serializer = BaseStationSerializer(base_stations, many=True)
    return Response(serializer.data)

@api_view(['GET'])
def get_base_stations_by_area(request, area_id):
    """Tuman bo'yicha baza stantsiyalarini olish"""
    base_stations = BsBeeline.objects.filter(area_id=area_id)
    serializer = BaseStationSerializer(base_stations, many=True)
    return Response(serializer.data)

@api_view(['GET'])
def get_base_stations_by_status(request, status_value):
    """Status bo'yicha baza stantsiyalarini olish"""
    if status_value not in ['online', 'offline']:
        return Response(
            {"error": "Invalid status. Must be 'online' or 'offline'"},
            status=status.HTTP_400_BAD_REQUEST
        )

    # Barcha stantsiyalarni olish
    base_stations = BsBeeline.objects.all()
    serializer = BaseStationSerializer(base_stations, many=True)

    # Status bo'yicha filtrlash
    filtered_stations = [station for station in serializer.data if station['status'] == status_value]

    return Response(filtered_stations)

@api_view(['GET'])
def get_all_regions(request):
    """Barcha viloyatlarni olish"""
    regions = RegionUzb.objects.all()
    serializer = RegionSerializer(regions, many=True)
    return Response(serializer.data)

@api_view(['GET'])
def get_all_areas(request):
    """Barcha tumanlarni olish"""
    areas = AreaUzb.objects.all()
    serializer = AreaSerializer(areas, many=True)
    return Response(serializer.data)

@api_view(['GET'])
def get_areas_by_region(request, region_id):
    """Viloyat bo'yicha tumanlarni olish"""
    areas = AreaUzb.objects.filter(region_id=region_id)
    serializer = AreaSerializer(areas, many=True)
    return Response(serializer.data)

@api_view(['GET'])
def get_all_alarms(request):
    """Barcha avariyalarni olish"""
    alarms = Current_Alarms.objects.all()
    serializer = AlarmSerializer(alarms, many=True)
    return Response(serializer.data)


@login_required()
def map_view(request):
    bsData = BsBeeline.objects.all()
    almData = Current_Alarms.objects.all()
    points_for_template = []

    for item in bsData:
        status=False
        for alm in almData:
            if item.bsnum == getNumberBS(alm.bsname):
                status=True
                break
        points_for_template.append({
            'name': item.bsname,
            'lat': item.lat.replace(",","."),
            'lon': item.lon.replace(",","."),
            # 'status': item['status']
            'status': status
        })
    # print(json.dumps(points_for_template))

    context = {
        # Используем json.dumps и json_script для безопасной передачи данных в JS
        'points_data_json': points_for_template
    }
    return render(request, 'map2.html', context)




