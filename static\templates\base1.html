<!DOCTYPE html>
{% load static %}

{% block extrahead %}
<script>window.CKEDITOR_BASEPATH = '/static/ckeditor/ckeditor/';</script>
{{ block.super }}
{% endblock %}

<html lang="en">
<head>
    <meta charset="UTF-8">
    <title> {% block title %}Beeline-BS Uzbekistan {% endblock title %} </title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="{% static 'css/bootstrap.min.css' %}" rel="stylesheet">
    <link type="image/x-icon" href="{% static 'img/signal.ico' %}" rel="shortcut icon">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background-color: #f8f9fa;
        }

        .navbar {
            background-color: #343a40; /* Asosiy fon rangi */
            padding-top: 0.5rem; /* Navbar tepa/past padding ozroq */
            padding-bottom: 0.5rem;
        }

        .navbar-brand img {
            max-height: 30px; /* Logo balandligini chegaralash */
            margin-right: 8px; /* Logo va tekst orasidagi masofa */
        }

        .navbar-brand b {
            color: #ffc107; /* Brend rangi */
        }

        .navbar-nav .nav-link {
            color: #fff; /* Linklar rangi */
            padding: 0.5rem 1rem; /* Linklar ichki paddingi (tepa/past va yon) */
            margin: 0 0.25rem; /* Linklar orasidagi tashqi margin (yon) */
            border-radius: 0.25rem; /* Link chekkalarini yumshoqlash */
            transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out; /* Silliq o'tish effekti */
            text-align: center; /* Matnni markazga joylash (agar kerak bo'lsa) */
             /* white-space: nowrap; <-- Bu qator olib tashlandi */
        }

        .navbar-nav .nav-link:hover,
        .navbar-nav .nav-link:focus {
            color: #343a40; /* Hover/focus holatida tekst rangi */
            background-color: #ffc107; /* Hover/focus holatida fon rangi */
        }

        /* Aktiv link uchun (agar kerak bo'lsa) */
        .navbar-nav .nav-link.active {
            color: #343a40;
            background-color: #ffc107;
            font-weight: bold;
        }

        .dropdown-menu {
            background-color: #343a40; /* Dropdown menyu fon rangi */
            border: none; /* Chegara chizig'ini olib tashlash */
            margin-top: 0.25rem !important; /* Navbar va dropdown orasidagi masofa */
            border-radius: 0.25rem; /* Dropdown chekkalarini yumshoqlash */
        }

        .dropdown-menu .dropdown-item {
            color: #fff; /* Dropdown elementlari rangi */
            padding: 0.5rem 1rem; /* Dropdown elementlari ichki paddingi */
            transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out; /* Silliq o'tish effekti */
        }

        .dropdown-menu .dropdown-item:hover,
        .dropdown-menu .dropdown-item:focus {
            background-color: #ffc107; /* Hover/focus holatida fon rangi */
            color: #343a40; /* Hover/focus holatida tekst rangi */
        }

        /* O'ng tarafdagi user menyusi uchun qo'shimcha margin */
        .navbar-nav.ms-auto {
            margin-left: 1rem; /* Asosiy menyu va user menyusi orasidagi masofa */
        }

        /* Toggler (kichik ekranlar uchun) */
        .navbar-toggler {
            border-color: rgba(255, 255, 255, 0.1);
        }
        .navbar-toggler-icon {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 193, 7, 1%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e"); /* Toggler ikonkasini sariq qilish */
        }

    </style>
</head>

<body>
<nav class="navbar navbar-expand-lg navbar-dark">
    <div class="container-fluid">
        <!-- Logoni navbar-brand ichiga olib kirish yaxshiroq -->
        <a class="navbar-brand d-flex align-items-center" href="{% url 'bsview' %}">
            <img src="{% static 'img/logo2.png' %}" alt="Logo">
            <b>Beeline-BS</b>
        </a>

        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNavDropdown"
                aria-controls="navbarNavDropdown" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarNavDropdown">
            {% if user.is_authenticated %}
            <ul class="navbar-nav me-auto mb-2 mb-lg-0"> <!-- me-auto chapga yopishish uchun, mb- kichik ekranda joy -->
                <li class="nav-item">
                    <!-- Misol: Hozirgi sahifaga qarab 'active' klassini qo'shish mumkin -->
                    <a class="nav-link {% if request.resolver_match.url_name == 'bsview' %}active{% endif %}" aria-current="page" href="{% url 'bsview' %}">Список БС</a>
                </li>
                {% if user.privilege_id == 1 or user.privilege_id == 2 %}
                <li class="nav-item">
                    <a class="nav-link {% if request.resolver_match.url_name == 'addbs' %}active{% endif %}" href="{% url 'addbs' %}">Добавить БС</a>
                </li>
                {% endif %}
                <li class="nav-item">
                    <a class="nav-link {% if request.resolver_match.url_name == 'createreport' %}active{% endif %}" href="{% url 'createreport' %}">Разные Отчеты</a>
                </li>
                {% if user.privilege_id == 1 or user.privilege_id == 2 %}
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="alarmsDropdown1" role="button"
                       data-bs-toggle="dropdown" aria-expanded="false">
                        Алармы
                    </a>
                    <ul class="dropdown-menu" aria-labelledby="alarmsDropdown1">
                        <li><a class="dropdown-item" href="{% url 'alarms' %}">Актив Алармы</a></li>
                        <li><a class="dropdown-item" href="{% url 'alarmsall' %}">Все Актив Алармы</a></li>
                        {% if user.privilege_id == 1 %}
                        <li><a class="dropdown-item" href="{% url 'idlebts' %}">Аварии по областям</a></li>
                        {% endif %}
                        <li><a class="dropdown-item" href="{% url 'alarmstat' %}">Аларм истории</a></li>
                        <li><a class="dropdown-item" href="{% url 'alarmview' %}">Выгрузка истории</a></li>
                        <li><a class="dropdown-item" href="{% url 'alarmanalyze' %}">Анализ Аварий по БС</a></li>
                        <li><a class="dropdown-item" href="{% url 'alarmanalyzeday' %}">Анализ Аварий по Дате</a></li>
                    </ul>
                </li>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="btsDropdown" role="button"
                       data-bs-toggle="dropdown" aria-expanded="false">
                        БТС файлы
                    </a>
                    <ul class="dropdown-menu" aria-labelledby="btsDropdown">
                        <li><a class="dropdown-item" href="{% url 'createbts' %}">Делаем файл BTS для Nemo</a></li>
                        <li><a class="dropdown-item" href="{% url 'createkmz' %}">Делаем KMZ файл для Google Earth</a></li>
                        <li><a class="dropdown-item" href="{% url 'createGnet' %}">Cell файл для G-NetTrack</a></li>
                    </ul>
                </li>
                <li class="nav-item">
                    <a class="nav-link fw-bold {% if request.resolver_match.url_name == 'conffiles' %}active{% endif %}" href="{% url 'conffiles' %}">Conf. файлы</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if request.resolver_match.url_name == 'importdata' %}active{% endif %}" href="{% url 'importdata' %}">Импорт данных</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if request.resolver_match.url_name == 'nriview' %}active{% endif %}" href="{% url 'nriview' %}">Данные с NRI</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if request.resolver_match.url_name == 'mapview' %}active{% endif %}" href="{% url 'mapview' %}">MAP view</a>
                </li>
                {% endif %}
                {% if user.privilege_id == 1 %}
                <li class="nav-item">
                    <a class="nav-link {% if request.resolver_match.url_name == 'users' %}active{% endif %}" href="{% url 'users' %}">Пользователи</a>
                </li>
                {% endif %}
            </ul>

            <!-- User dropdown menyusi -->
            <ul class="navbar-nav ms-auto"> <!-- ms-auto o'ngga suradi -->
                <li class="nav-item dropdown">
                    <!-- ##### XATO TUZATILGAN QISM ##### -->
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown"
                       aria-expanded="false">
                        {{user.first_name}} {{user.last_name}}
                    </a>
                     <!-- ##### XATO TUZATILGAN QISM TUGADI ##### -->
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown"> <!-- dropdown-menu-end o'ngga ochilishi uchun, ID ga moslashtirildi -->
                        {% if user.privilege_id == 1 %}
                        <li><a class="dropdown-item" href="{% url 'signup' %}">Добавить пользователя</a></li>
                        {% endif %}
                        <li><a class="dropdown-item" href="{% url 'changepassword' %}">Смена пароля</a></li>
                        <li><hr class="dropdown-divider" style="border-color: rgba(255,193,7,0.5);"></li> <!-- Chiziq rangi -->
                        <li><a class="dropdown-item" href="{% url 'logout' %}">Выход</a></li>
                    </ul>
                </li>
            </ul>
            {% else %}
                 <!-- Login tugmasi (agar kerak bo'lsa) -->
                 <ul class="navbar-nav ms-auto">
                     <li class="nav-item">
                          <a class="btn btn-outline-warning" href="{% url 'login' %}">Вход</a> <!-- btn-outline-warning sariq rang uchun -->
                     </li>
                 </ul>
            {% endif %}
        </div>
    </div>
</nav>

<main>
    <div class="container-xl mt-4"> <!-- Kontentga tepadan ozroq joy -->
        {% block content %}
        {% endblock content %}
    </div>
</main>

<script src="{% static 'js/bootstrap.bundle.min.js' %}"></script>
</body>
</html>