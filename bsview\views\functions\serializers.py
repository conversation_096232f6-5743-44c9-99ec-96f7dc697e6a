from rest_framework import serializers
from datetime import datetime
from bsview.views.imports_file import *

class RegionSerializer(serializers.ModelSerializer):
    """Viloyat/shahar uchun serializer"""
    class Meta:
        model = RegionUzb
        fields = ['id', 'name']

class AreaSerializer(serializers.ModelSerializer):
    """Tuman uchun serializer"""
    class Meta:
        model = AreaUzb
        fields = ['id', 'name', 'region_id']

class AlarmSerializer(serializers.ModelSerializer):
    """Avariya uchun serializer"""
    type = serializers.SerializerMethodField()
    duration = serializers.SerializerMethodField()

    class Meta:
        model = Current_Alarms
        fields = ['id', 'bsname', 'bscrnc', 'appeartime', 'type', 'duration']

    def get_type(self, obj):
        """Avariya turini aniqlash"""
        return 'OML_fault' if obj.bscrnc.startswith('BSC') else 'Nodeb_unavailable'

    def get_duration(self, obj):
        """Avariya davomiyligini hisoblash"""
        appear_time = obj.appeartime
        now = datetime.datetime.now()
        diff = now - appear_time.replace(tzinfo=None)
        hours = diff.seconds // 3600
        minutes = (diff.seconds % 3600) // 60
        return f"{hours}h {minutes}m"

class BaseStationSerializer(serializers.Serializer):
    """Baza stantsiyasi uchun serializer"""
    id = serializers.IntegerField()
    bsName = serializers.CharField(source='bsname')
    lat = serializers.SerializerMethodField()
    lon = serializers.SerializerMethodField()
    azimut = serializers.SerializerMethodField()
    area = serializers.SerializerMethodField()
    region = serializers.SerializerMethodField()
    areaId = serializers.IntegerField(source='area_id')
    regionId = serializers.IntegerField(source='region_id')
    status = serializers.SerializerMethodField()
    alarms = serializers.SerializerMethodField()

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.regions_map = {}
        self.areas_map = {}
        self.alarms_map = {}

        # Barcha viloyatlarni olish
        regions = RegionUzb.objects.all()
        for region in regions:
            self.regions_map[region.id] = region.name

        # Barcha tumanlarni olish
        areas = AreaUzb.objects.all()
        for area in areas:
            self.areas_map[area.id] = area.name

        # Barcha avariyalarni olish
        alarms = Current_Alarms.objects.all()
        for alarm in alarms:
            if alarm.bsname not in self.alarms_map:
                self.alarms_map[alarm.bsname] = []
            self.alarms_map[alarm.bsname].append(alarm)

    def get_region(self, obj):
        """Viloyat nomini olish"""
        return self.regions_map.get(obj.region_id, "")

    def get_area(self, obj):
        """Tuman nomini olish"""
        return self.areas_map.get(obj.area_id, "")

    def get_lat(self, obj):
        """Latitude ni float formatga o'girish"""
        try:
            return float(obj.lat.replace(',', '.'))
        except (ValueError, AttributeError):
            return 0.0

    def get_lon(self, obj):
        """Longitude ni float formatga o'girish"""
        try:
            return float(obj.lon.replace(',', '.'))
        except (ValueError, AttributeError):
            return 0.0

    def get_azimut(self, obj):
        """Birinchi mavjud azimutni olish"""
        for field in ['gsm900', 'gsm1800', 'gsmbi1800', 'umts900', 'umts2100',
                     'umtsbesec', 'lte800', 'lte1800', 'ltebi1800', 'lte2600',
                     'lte2300', 'lte2100']:
            value = getattr(obj, field, None)
            if value:
                return value
        return '0-120-240'  # Default azimut

    def get_status(self, obj):
        """Stantsiya statusini aniqlash"""
        return 'offline' if obj.bsname in self.alarms_map else 'online'

    def get_alarms(self, obj):
        """Stantsiya avariyalarini olish"""
        if obj.bsname in self.alarms_map:
            alarms = self.alarms_map[obj.bsname]
            return AlarmSerializer(alarms, many=True).data
        return None
