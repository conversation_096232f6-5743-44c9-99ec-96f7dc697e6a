{% extends 'base.html' %}
{% load static %}
{% load crispy_forms_tags %}

{% block content %}

<div class="jumbotron">
    <p>
</div>
<body>
<div class="container card ">
    <div class="row ">
        <div class="col">
            <b>Наименование БС:</b>
            <div class="card-header"> {{object.bsname}}</div>
        </div>
        <div class="col">
            <b>Номер БС:</b>
            <div class="card-header"> {{object.bsnum}}</div>
        </div>
        <div class="col">
            <b>Номер Бисектора:</b>
            <div class="card-header"> {{object.besecnum}}</div>
        </div>
    </div>

    <div class="row">
        <div class="col">
            <b>Широта :</b>
            <div class="card-header"> {{object.lat}}</div>
        </div>
        <div class="col">
            <b>Долгота :</b>
            <div class="card-header">{{object.lon}}</div>
        </div>
        <div class="col">
            <b>Номер модема (АСКУЭ):</b>
            <div class="card-header"> {{object.modem}}&nbsp</div>
        </div>
    </div>

    <div class="row">
        <div class="col">
            <b>Область :</b>
            <div class="card-header">{{object.region}}</div>
        </div>
        <div class="col">
            <b>Район:</b>
            <div class="card-header">{{object.area}}&nbsp</div>
        </div>
    </div>

    <div class="row">
        <div class="col">
            <b>Адрес БС:</b>
            <div class="card-header">{{object.address}}&nbsp</div>
        </div>
    </div>

    <div class="row">
        <div class="col">
            <b>GSM-900 :</b>
            <div class="card-header">{{object.gsm900}}&nbsp</div>
        </div>
        <div class="col">
            <b>GSM-1800 :</b>
            <div class="card-header"> {{object.gsm1800}}&nbsp</div>
        </div>
        <div class="col">
            <b>GSMBi-1800 :</b>
            <div class="card-header"> {{object.gsmbi1800}}&nbsp</div>
        </div>
        <div class="col">
            <b>UMTS-900 :</b>
            <div class="card-header">{{object.umts900}}&nbsp</div>
        </div>
        <div class="col">
            <b>UMTS-2100 :</b>
            <div class="card-header"> {{object.umts2100}}&nbsp</div>
        </div>
        <div class="col">
            <b>UMTS Бисектор :</b>
            <div class="card-header"> {{object.umtsbesec}}&nbsp</div>
        </div>
    </div>

    <div class="row">
        <div class="col">
            <b>LTE-800 :</b>
            <div class="card-header">{{object.lte800}}&nbsp</div>
        </div>
        <div class="col">
            <b>LTE-1800 :</b>
            <div class="card-header">{{object.lte1800}}&nbsp</div>
        </div>
        <div class="col">
            <b>LTEBi-1800 :</b>
            <div class="card-header">{{object.ltebi1800}}&nbsp</div>
        </div>
        <div class="col">
            <b>LTE-2600 :</b>
            <div class="card-header">{{object.lte2600}}&nbsp</div>
        </div>
        <div class="col">
            <b>LTE-2300 :</b>
            <div class="card-header"> {{object.lte2300}} &nbsp</div>
        </div>
        <div class="col">
            <b>LTE-2100 :</b>
            <div class="card-header"> {{object.lte2100}} &nbsp</div>
        </div>
    </div>
    <br>
</div>
<br>
{% if user.privilege_id == 1 or user.privilege_id == 2 %}
<a href="{% url 'bsedit' object.pk %}" class="btn btn-primary">Изменить</a>
<a href="{% url 'bsdelete' object.pk %}" class="btn btn-primary">Удалить</a>
{% endif %}
<a href="{% url 'bsview' %}" class="btn btn-primary">Назад</a>

{% include 'updatemodal.html' %}

<script src="{% static 'js/jquery-3.3.1.min.js' %}"></script>
<script>
    $("#id_region").change(function () {
      var url = $("#editForm").attr("data-areas-url");
      var regionId = $(this).val();

      $.ajax({
        url: url,
        data: {
          'region': regionId
        },
        success: function (data) {
          $("#id_area").html(data);
        }
      });

    });
</script>

<style>
    .btn-primary {
        transition: background-color 0.3s, transform 0.3s;
    }
    .btn-primary:hover {
        background-color: #0056b3; /* Hover color */
        transform: scale(1.05); /* Scale effect */
    }
</style>

</body>
{% endblock %}