{% extends 'base.html' %}
{% load crispy_forms_tags %}
{% block title %} Map View BS Beeline {% endblock title %}

{% block content %}

<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
    integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin="" />
<link rel="stylesheet" href="/static/css/styles.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/leaflet-search/4.0.0/leaflet-search.min.css" />

<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
    integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/leaflet-search/4.0.0/leaflet-search.min.js"></script>

<style>
    /* Body stillarini o'rnatamiz */
    body {
        margin: 0;
        padding: 0;
        overflow: hidden;
    }

    /* Loader stillarini o'rnatamiz */
    .loader-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.85);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        transition: opacity 0.5s, visibility 0.5s;
    }

    .loader {
        width: 120px;
        height: 120px;
        border: 5px solid transparent;
        border-top-color: #F9A825;
        border-radius: 50%;
        animation: spin 1.5s linear infinite;
        position: relative;
    }

    .loader:before,
    .loader:after {
        content: "";
        position: absolute;
        border: 5px solid transparent;
        border-radius: 50%;
    }

    .loader:before {
        top: 5px;
        left: 5px;
        right: 5px;
        bottom: 5px;
        border-top-color: #E53935;
        animation: spin 2s linear infinite;
    }

    .loader:after {
        top: 15px;
        left: 15px;
        right: 15px;
        bottom: 15px;
        border-top-color: #64B5F6;
        animation: spin 1s linear infinite;
    }

    .loader-text {
        margin-top: 30px;
        font-family: 'Arial', sans-serif;
        font-size: 28px;
        color: white;
        letter-spacing: 3px;
        position: relative;
    }

    .loader-text span {
        display: inline-block;
        opacity: 0;
        animation: fadeIn 1s ease-in-out infinite;
    }

    .loader-text span:nth-child(1) {
        animation-delay: 0.05s;
    }

    .loader-text span:nth-child(2) {
        animation-delay: 0.1s;
    }

    .loader-text span:nth-child(3) {
        animation-delay: 0.15s;
    }

    .loader-text span:nth-child(4) {
        animation-delay: 0.2s;
    }

    .loader-text span:nth-child(5) {
        animation-delay: 0.25s;
    }

    .loader-text span:nth-child(6) {
        animation-delay: 0.3s;
    }

    .loader-text span:nth-child(7) {
        animation-delay: 0.35s;
    }

    .loader-text span:nth-child(8) {
        animation-delay: 0.4s;
    }

    .loader-text span:nth-child(9) {
        animation-delay: 0.45s;
    }

    .loader-text span:nth-child(10) {
        animation-delay: 0.5s;
    }

    .loader-text span:nth-child(11) {
        animation-delay: 0.55s;
    }

    .loader-text span:nth-child(12) {
        animation-delay: 0.6s;
    }

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }

        100% {
            transform: rotate(360deg);
        }
    }

    @keyframes fadeIn {

        0%,
        100% {
            opacity: 0;
            transform: translateY(5px);
        }

        50% {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Navbar stillarini o'zgartiramiz */
    .navbar {
        margin-bottom: 0 !important;
        z-index: 2000;
        position: relative;
    }

    /* Карта контейнери учун стиллар */
    #mapid {
        height: 100vh;
        width: calc(100% - 300px);
        margin: 0;
        padding: 0;
        position: fixed;
        top: 0;
        left: 300px;
        right: 0;
        bottom: 0;
        z-index: 95;
    }

    /* Zoom tugmalarini to'g'rilash */
    .leaflet-control-container .leaflet-top {
        top: 80px;
    }

    .leaflet-control-container .leaflet-right {
        right: 10px;
    }

    .leaflet-control-zoom {
        margin-right: 10px;
    }

    /* Bo'sh joyni yo'qotish uchun */
    .container-fluid {
        padding: 0;
        margin: 0;
        position: relative;
    }

    /* Стиль для сайдбара */
    .sidebar {
        width: 300px;
        max-width: 300px;
        height: calc(100vh - 70px);
        background-color: white;
        box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
        display: flex;
        flex-direction: column;
        overflow: hidden;
        position: fixed;
        top: 70px;
        left: 0;
        z-index: 1000;
        border-radius: 0;
    }

    .sidebar-header {
        padding: 15px;
        background-color: #2c3e50;
        color: white;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .sidebar-header h2 {
        margin: 0;
        font-size: 18px;
        font-weight: 500;
    }

    /* Til tanlash tugmalari uchun stillar */
    .language-selector {
        display: flex;
    }

    .lang-btn {
        background-color: transparent;
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.3);
        margin-left: 5px;
        padding: 2px 6px;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.2s;
    }

    .lang-btn:hover {
        background-color: rgba(255, 255, 255, 0.2);
    }

    .lang-btn.active {
        background-color: #f39c12;
        border-color: #f39c12;
        color: #2c3e50;
        font-weight: bold;
    }

    .sidebar-content {
        padding: 0 8px 8px;
        overflow-y: auto;
        flex: 1;
        position: relative;
    }

    .stats-container {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;
        background-color: #f5f5f5;
        border-radius: 5px;
        padding: 10px;
    }

    .stat-item {
        text-align: center;
        flex: 1;
    }

    .stat-value {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 5px;
    }

    .stat-label {
        font-size: 12px;
        color: #666;
    }

    .stat-item.online .stat-value {
        color: #4caf50;
    }

    .stat-item.offline .stat-value {
        color: #f44336;
    }

    .filter-group {
        margin-bottom: 10px;
    }

    .filter-group label {
        display: block;
        margin-bottom: 3px;
        font-weight: 500;
        font-size: 12px;
        color: #555;
    }

    .filter-group select,
    .filter-group input {
        width: 100%;
        padding: 6px 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 13px;
    }

    .filter-group select:focus {
        outline: none;
        border-color: #2c3e50;
    }

    .filter-group select:disabled {
        background-color: #f5f5f5;
        cursor: not-allowed;
    }

    .map-type-control {
        background-color: white;
        border-radius: 4px;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
        padding: 3px;
        position: absolute;
        top: 80px;
        /* Под навбаром */
        right: 10px;
        z-index: 1000;
    }

    /* Qidiruv elementi uchun stillar */
    .search-container {
        display: flex;
        margin-bottom: 3px;
        width: 100%;
    }

    .search-input {
        flex-grow: 1;
        padding: 6px 8px;
        border: 1px solid #ddd;
        border-radius: 4px 0 0 4px;
        font-size: 13px;
        height: 36px;
        box-sizing: border-box;
    }

    .search-button {
        background-color: white;
        border: 1px solid #ddd;
        border-left: none;
        border-radius: 0 4px 4px 0;
        padding: 6px 10px;
        cursor: pointer;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
    }

    .search-button:hover {
        background-color: #f4f4f4;
    }

    .map-controls-row {
        display: flex;
        flex-direction: row;
        gap: 3px;
    }

    .map-source-buttons,
    .map-type-buttons {
        display: flex;
    }

    .map-type-control button {
        background-color: white;
        border: none;
        padding: 6px 6px;
        cursor: pointer;
        font-size: 14px;
        outline: none;
        transition: all 0.2s ease;
        color: #555;
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .map-source-logo {
        width: 24px;
        height: 24px;
        object-fit: contain;
    }

    .map-source-buttons button:first-child {
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;
    }

    .map-source-buttons button:last-child {
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
    }

    .map-type-buttons button:first-child {
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;
    }

    .map-type-buttons button:last-child {
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
    }

    .map-type-control button.active {
        background-color: #4285F4;
        color: white;
        font-weight: 500;
    }

    .map-type-control button:hover:not(.active) {
        background-color: #f4f4f4;
    }

    /* Вилоятлар статистикаси учун стиллар */
    .regions-stats {
        margin-top: 0;
        border-top: none;
        padding-top: 0;
    }

    .region-item {
        margin-bottom: 5px;
        padding: 8px;
        background-color: #f9f9f9;
        border-radius: 5px;
        cursor: pointer;
        transition: background-color 0.2s;
    }

    .region-item:hover {
        background-color: #f0f0f0;
    }

    .region-name {
        font-weight: bold;
        color: #2c3e50;
    }

    .region-stats {
        display: flex;
        justify-content: space-between;
    }

    .region-stat {
        flex: 1;
        text-align: center;
    }

    .region-stat-value {
        font-weight: bold;
    }

    .region-stat-label {
        font-size: 12px;
        color: #666;
    }

    .total-stat {
        color: #333;
    }

    .online-stat {
        color: #4caf50;
    }

    .offline-stat {
        color: #f44336;
    }

    .region-item.active {
        background-color: #e3f2fd;
        border-left: 3px solid #2196f3;
    }

    /* Вилоятлар статистикаси жадвал кўриниши */
    .regions-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        margin-top: 0;
        font-size: 13px;
    }

    .regions-table thead {
        position: sticky;
        top: 0;
        z-index: 10;
        background-color: white;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .regions-table th {
        background-color: #f3f3f3;
        padding: 8px 5px;
        text-align: center;
        font-weight: 600;
        border-bottom: 2px solid #ddd;
    }

    .regions-table td {
        padding: 6px 5px;
        text-align: center;
        border-bottom: 1px solid #eee;
    }

    .regions-table tr:hover {
        background-color: #f5f5f5;
        cursor: pointer;
    }

    .regions-table .region-name-cell {
        text-align: left;
        font-weight: 500;
    }

    .regions-table tr.active {
        background-color: #e3f2fd;
    }

    /* БС номи стилини яхшилаш */
    .bs-name-tooltip {
        background: transparent;
        border: none;
        box-shadow: none;
        color: #000000;
        font-weight: bold;
        font-size: 10px;
        padding: 0;
        text-shadow: 1px 1px 1px rgba(255, 255, 255, 0.7);
        white-space: nowrap;
        border-radius: 0;
        transition: font-size 0.2s;
    }

    /* Турли карта масштаплари учун БС номлари ўлчамлари */
    .zoom-level-low .bs-name-tooltip {
        font-size: 8px;
    }

    .zoom-level-medium .bs-name-tooltip {
        font-size: 10px;
    }

    .zoom-level-high .bs-name-tooltip {
        font-size: 12px;
    }

    /* БС номи стилини яхшилаш учун Leaflet стиллари орқали ўзгартириш */
    .bs-name-tooltip:before {
        display: none;
    }

    /* БС номи стилини яхшилаш */
    .leaflet-tooltip.bs-name-tooltip {
        background: transparent;
        border: none;
        box-shadow: none;
    }

    /* Картадаги атрибуцияни яшириш учун */
    .leaflet-control-attribution {
        display: none !important;
    }

    /* Копирайт учун стил */
    .custom-copyright {
        position: absolute;
        bottom: 5px;
        right: 10px;
        background-color: rgba(255, 255, 255, 0.7);
        padding: 3px 8px;
        border-radius: 3px;
        font-size: 11px;
        color: #333;
        z-index: 1000;
        font-weight: 500;
    }
</style>
</head>

<body>
    <!-- Loader -->
    <div class="loader-container" id="loader">
        <div class="loader"></div>
        <div class="loader-text">
            <span>Q</span>
            <span>u</span>
            <span>a</span>
            <span>l</span>
            <span>i</span>
            <span>t</span>
            <span>y</span>
            <span>&nbsp;</span>
            <span>t</span>
            <span>e</span>
            <span>a</span>
            <span>m</span>
        </div>
    </div>

    <div class="container-fluid">
        <div class="sidebar">
            <div class="sidebar-header">
                <h2 id="stats-header">Статистика</h2>
                <div class="language-selector">
                    <button id="lang-ru" class="lang-btn active">RU</button>
                    <button id="lang-en" class="lang-btn">EN</button>
                </div>
            </div>
            <div class="sidebar-content">
                <!-- Вилоятлар статистикаси -->
                <div class="regions-stats">
                    <table class="regions-table">
                        <thead>
                            <tr>
                                <th id="region-header">Регион</th>
                                <th>All</th>
                                <th>On</th>
                                <th>Off</th>
                            </tr>
                        </thead>
                        <tbody id="regions-stats-container">
                            <!-- Вилоятлар статистикаси динамик равишда қўшилади -->
                        </tbody>
                    </table>
                </div>

                <!-- Ажратиш чизиғи -->
                <div style="border-top: 1px solid #ddd; margin: 10px 0;"></div>

                <!-- Қолган фильтрлар -->
                <div class="filter-group">
                    <label for="area" id="area-label">Район:</label>
                    <select id="area" disabled>
                        <option value="" id="all-areas">Все районы</option>
                        <!-- Районы будут добавлены динамически -->
                    </select>
                </div>

                <!-- Поиск по имени или номеру БС -->
                <div class="filter-group">
                    <label for="bs-search" id="bs-search-label">Поиск БС:</label>
                    <input type="text" id="bs-search" class="form-control" placeholder="Введите название или номер БС">
                </div>

                <div class="filter-group" style="margin-bottom: 5px;">
                    <button id="reset-filters" class="btn btn-danger btn-sm w-100 mb-2"
                        style="font-size: 12px; padding: 6px 0;">Сбросить фильтры</button>
                </div>

                <!-- Яширин селектлар (логика учун) -->
                <div style="display: none;">
                    <select id="region">
                        <option value="" id="all-regions">Все регионы</option>
                        <!-- Регионы будут добавлены динамически -->
                    </select>
                    <select id="status">
                        <option value="all" id="status-all">Все</option>
                        <option value="online" id="status-online">Онлайн</option>
                        <option value="offline" id="status-offline">Оффлайн</option>
                    </select>
                    <!-- Яширин статистика элементлари -->
                    <div id="total-bs-count">0</div>
                    <div id="active-bs-count">0</div>
                    <div id="inactive-bs-count">0</div>
                </div>

                <!-- Карта режими тугмалари сайдбардан олиб ташланди -->
            </div>
        </div>
    </div>

    <div id="mapid"></div>

    <!-- Копирайт элементи -->
    <div class="custom-copyright">© 2025 Quality team</div>

    <!-- Карта манбаси ва режими бошқаруви -->
    <div class="map-type-control">
        <!-- Qidiruv elementi -->
        <div class="search-container">
            <input type="text" id="location-search" class="search-input" placeholder="Search location">
            <button id="search-button" class="search-button" title="Search"><i class="fas fa-search"></i></button>
        </div>
        <div class="map-controls-row">
            <!-- Карта манбаси -->
            <div class="map-source-buttons">
                <button id="osm-source-button" class="active" title="OpenStreetMap"><img
                        src="https://upload.wikimedia.org/wikipedia/commons/thumb/b/b0/Openstreetmap_logo.svg/256px-Openstreetmap_logo.svg.png"
                        alt="OpenStreetMap" class="map-source-logo"></button>
                <button id="yandex-source-button" title="Яндекс Карты"><img
                        src="https://upload.wikimedia.org/wikipedia/commons/thumb/5/58/Yandex_icon.svg/200px-Yandex_icon.svg.png"
                        alt="Яндекс Карты" class="map-source-logo"></button>
            </div>
            <!-- Карта режими -->
            <div class="map-type-buttons">
                <button id="map-button" class="active" title="Карта"><i class="fas fa-map"></i></button>
                <button id="satellite-button" title="Спутник"><i class="fas fa-satellite"></i></button>
            </div>
        </div>
    </div>

    {{ points_data_json|json_script:"points-data" }}

    <script>
        // Til ma'lumotlari
        const translations = {
            'ru': {
                'stats_header': 'Статистика',
                'region_header': 'Регион',
                'area_label': 'Район:',
                'bs_search_label': 'Поиск БС:',
                'bs_search_placeholder': 'Введите название или номер БС',
                'reset_filters': 'Сбросить фильтры',
                'all_regions': 'Все регионы',
                'all_areas': 'Все районы',
                'status_all': 'Все',
                'status_online': 'Онлайн',
                'status_offline': 'Оффлайн',
                'map_title': 'Карта',
                'satellite_title': 'Спутник',
                'bs_active': 'БС активен',
                'bs_inactive': 'БС не работает',
                'osm_title': 'OpenStreetMap',
                'yandex_title': 'Яндекс Карты',
                'search_location': 'Поиск местоположения',
                'search_button': 'Поиск',
                'location_not_found': 'Местоположение не найдено',
                'search_error': 'Ошибка при поиске'
            },
            'en': {
                'stats_header': 'Statistics',
                'region_header': 'Region',
                'area_label': 'Area:',
                'bs_search_label': 'Search BS:',
                'bs_search_placeholder': 'Enter BS name or number',
                'reset_filters': 'Reset filters',
                'all_regions': 'All regions',
                'all_areas': 'All areas',
                'status_all': 'All',
                'status_online': 'Online',
                'status_offline': 'Offline',
                'map_title': 'Map',
                'satellite_title': 'Satellite',
                'bs_active': 'BS is active',
                'bs_inactive': 'BS is not working',
                'osm_title': 'OpenStreetMap',
                'yandex_title': 'Yandex Maps',
                'search_location': 'Search location',
                'search_button': 'Search',
                'location_not_found': 'Location not found',
                'search_error': 'Error during search'
            }
        };

        // Viloyat va tumanlar tarjimalari
        const regionTranslations = {
            'город Ташкент': 'Tashkent city',
            'Ташкентская область': 'Tashkent region',
            'Андижанская область': 'Andijan region',
            'Бухарская область': 'Bukhara region',
            'город Коканд': 'Kokand city',
            'Джиззахская область': 'Jizzakh region',
            'Кашкадарьинская область': 'Kashkadarya region',
            'Наваийская область': 'Navoi region',
            'Наманганская область': 'Namangan region',
            'Рес.Каракалпакстан': 'Republic of Karakalpakstan',
            'Самаркандская область': 'Samarkand region',
            'Сурхандарьинская область': 'Surkhandarya region',
            'Сырдарьинская область': 'Syrdarya region',
            'Ферганская область': 'Fergana region',
            'Хорезмская область': 'Khorezm region',
            'Все регионы': 'All regions',
            'город Нукус': 'Nukus city',
            'Амударьинский район': 'Amudaryo district',
            'Берунийский район': 'Beruniy district',
            'Канлыкульский район': 'Qanlikol district',
            'Караузякский район': 'Qoraozak district',
            'Кегейлийский район': 'Kegeyli district',
            'Кунградский район': 'Qongirot district',
            'Муйнакский район': 'Muynoq district',
            'Нукусский район': 'Nukus district',
            'Тахиаташский район': 'Taxiatosh district',
            'Тахтакупырский район': 'Taxtakopir district',
            'Турткульский район': 'Tortkol district',
            'Ходжейлийский район': 'Xojayli district',
            'Чимбайский район': 'Chimboy district',
            'Шуманайский район': 'Shumanay district',
            'Элликкалинский район': 'Ellikqala district',
            'Бозатауский район': 'Bozatov district',
            // Qoraqalpog'iston ustida kelgan qo'shimchasiz nomlar
            'Ходжейлийский': 'Xojayli district',
            'Тахиаташский': 'Taxiatosh district',
            'Кунградский': 'Qongirot district',
            'Турткульский': 'Tortkol district',
            'Чимбайский': 'Chimboy district',
            'Берунийский': 'Beruniy district',
            'г. Нукус': 'Nukus city',
            'Амударьинский': 'Amudaryo district',
            'Кегейлийский': 'Kegeyli district',
            'Шуманайский': 'Shumanay district',
            'Элликкалинский': 'Ellikqala district',
            'Нукусский': 'Nukus district',
            'Караузякский': 'Qoraozak district',
            'Тахтакупырский': 'Taxtakopir district',
            'Бозатауский': 'Bozatov district',
            'Канлыкульский': 'Qanlikol district',
            'Муйнакский': 'Muynoq district',

            // Xorazm viloyati
            'город Ургенч': 'Urgench city',
            'Багатский район': 'Bog\'ot district',
            'Гурленский район': 'Gurlan district',
            'Ханкийский район': 'Xonqa district',
            'Ханкинский район': 'Xonqa district'
        };

        // Tuman nomlar tarjimalari
        const areaTranslations = {
            'Все районы': 'All areas',
            // Toshkent shahri
            'Учтепинский район': 'Uchtepa district',
            'Мирзо-Улугбекский район': 'Mirzo Ulugbek district',
            'Мирабадский район': 'Mirabad district',
            'Шайхантахурский район': 'Shaykhantaur district',
            'Юнусабадский район': 'Yunusabad district',
            'Яшнабадский район': 'Yashnabad district',
            'Сергелийский район': 'Sergeli district',
            'Алмазарский район': 'Almazar district',
            'Яккасарайский район': 'Yakkasaray district',
            'Чиланзарский район': 'Chilanzar district',
            'Бектемирский район': 'Bektemir district',

            // Toshkent viloyati
            'Бостанлыкский район': 'Bostanlyk district',
            'Чиназский район': 'Chinaz district',
            'город Бекабад': 'Bekabad city',
            'Уртачирчикский район': 'Urtachirchik district',
            'Юкоричирчикский район': 'Yukorichirchik district',
            'Зангиатинский район': 'Zangiata district',
            'город Чирчик': 'Chirchik city',
            'Янгиюльский район': 'Yangiyul district',
            'Ахангаранский район': 'Akhangaran district',
            'Паркентский район': 'Parkent district',
            'Кибрайский район': 'Kibray district',
            'Куйичирчикский район': 'Kuyichirchik district',
            'Аккурганский район': 'Akkurgan district',
            'Бекабадский район': 'Bekabad district',

            // Samarqand viloyati
            'город Самарканд': 'Samarkand city',
            'Пахтачийский район': 'Pakhtachi district',
            'город Каттакурган': 'Kattakurgan city',
            'Булунгурский район': 'Bulungur district',
            'Акдарьинский район': 'Akdarya district',
            'Нарпайский район': 'Narpay district',
            'Нурабадский район': 'Nurabad district',
            'Тайлакский район': 'Taylak district',
            'Ургутский район': 'Urgut district',
            'Каттакурганский район': 'Kattakurgan district',
            'Пайарыкский район': 'Payarik district',
            'Самаркандский район': 'Samarkand district',
            'Иштыханский район': 'Ishtykhan district',
            'Джамбайский район': 'Jambay district',
            'Пастдаргомский район': 'Pastdargom district',
            'Кошрабадский район': 'Koshrabot district',

            // Buxoro viloyati
            'Гиждуванский район': 'Gijduvan district',
            'Каракульский район': 'Karakul district',
            'город Бухара': 'Bukhara city',
            'Вабкентский район': 'Vobkent district',
            'Караулбазарский район': 'Karaulbazar district',
            'город Каган': 'Kagan city',
            'Алатский район': 'Olot district',
            'Ромитанский район': 'Romitan district',
            'Бухарский район': 'Bukhara district',
            'Жондорский район': 'Jondor district',
            'Шафирканский район': 'Shofirkon district',
            'Каганский район': 'Kagan district',
            'Пешкунский район': 'Peshku district',
            // Qoraqalpog'iston Respublikasi
            'город Нукус': 'Nukus city',
            'Амударьинский район': 'Amudaryo district',
            'Берунийский район': 'Beruniy district',
            'Канлыкульский район': 'Qanlikol district',
            'Караузякский район': 'Qoraozak district',
            'Кегейлийский район': 'Kegeyli district',
            'Кунградский район': 'Qongirot district',
            'Муйнакский район': 'Muynoq district',
            'Нукусский район': 'Nukus district',
            'Тахиаташский район': 'Taxiatosh district',
            'Тахтакупырский район': 'Taxtakopir district',
            'Турткульский район': 'Tortkol district',
            'Ходжейлийский район': 'Xojayli district',
            'Чимбайский район': 'Chimboy district',
            'Шуманайский район': 'Shumanay district',
            'Элликкалинский район': 'Ellikqala district',
            'Бозатауский район': 'Bozatov district',
            // Qoraqalpog'iston ustida kelgan qo'shimchasiz nomlar
            'Ходжейлийский': 'Xojayli district',
            'Тахиаташский': 'Taxiatosh district',
            'Кунградский': 'Qongirot district',
            'Турткульский': 'Tortkol district',
            'Чимбайский': 'Chimboy district',
            'Берунийский': 'Beruniy district',
            'г. Нукус': 'Nukus city',
            'Амударьинский': 'Amudaryo district',
            'Кегейлийский': 'Kegeyli district',
            'Шуманайский': 'Shumanay district',
            'Элликкалинский': 'Ellikqala district',
            'Нукусский': 'Nukus district',
            'Караузякский': 'Qoraozak district',
            'Тахтакупырский': 'Taxtakopir district',
            'Бозатауский': 'Bozatov district',
            'Канлыкульский': 'Qanlikol district',
            'Муйнакский': 'Muynoq district',

            // "город" bilan boshlanadigan shaharlar
            'город Ташкент': 'Tashkent city',
            'город Бекабад': 'Bekabad city',
            'город Чирчик': 'Chirchik city',
            'город Самарканд': 'Samarkand city',
            'город Каттакурган': 'Kattakurgan city',
            'город Бухара': 'Bukhara city',
            'город Каган': 'Kagan city',
            'город Андижан': 'Andijan city',
            'город Фергана': 'Fergana city',
            'город Коканд': 'Kokand city',
            'город Маргилан': 'Marg\'ilon city',
            'город Кувасай': 'Quvasoy city',
            'город Наманган': 'Namangan city',
            'город Джизак': 'Jizzakh city',
            'город Карши': 'Qarshi city',
            'город Навои': 'Navoi city',
            'город Зарафшан': 'Zarafshon city',
            'город Гулистан': 'Guliston city',
            'город Ширин': 'Shirin city',
            'город Янгиер': 'Yangiyer city',
            'город Термез': 'Termez city',
            'город Ургенч': 'Urgench city',
            'город Хива': 'Xiva city',

            // G. bilan boshlanadigan shaharlar
            'г. Ташкент': 'Tashkent city',
            'г. Бекабад': 'Bekabad city',
            'г. Чирчик': 'Chirchik city',
            'г. Самарканд': 'Samarkand city',
            'г. Каттакурган': 'Kattakurgan city',
            'г. Бухара': 'Bukhara city',
            'г. Каган': 'Kagan city',
            'г. Андижан': 'Andijan city',
            'г. Фергана': 'Fergana city',
            'г. Коканд': 'Kokand city',
            'г. Маргилан': 'Marg\'ilon city',
            'г. Кувасай': 'Quvasoy city',
            'г. Наманган': 'Namangan city',
            'г. Джизак': 'Jizzakh city',
            'г. Карши': 'Qarshi city',
            'г. Навои': 'Navoi city',
            'г. Зарафшан': 'Zarafshon city',
            'г. Гулистан': 'Guliston city',
            'г. Ширин': 'Shirin city',
            'г. Янгиер': 'Yangiyer city',
            'г. Термез': 'Termez city',
            'г. Ургенч': 'Urgench city',
            'г. Хива': 'Xiva city',

            // Xorazm viloyati
            'город Ургенч': 'Urgench city',
            'Багатский район': 'Bog\'ot district',
            'Гурленский район': 'Gurlan district',
            'Ханкийский район': 'Xonqa district',
            'Ханкинский район': 'Xonqa district',

            // Qo'shimcha shaharlar va tumanlar
            'город Алмалык': 'Olmaliq city',
            'г. Алмалык': 'Olmaliq city',
            'город Ангрен': 'Angren city',
            'г. Ангрен': 'Angren city',
            'город Асака': 'Asaka city',
            'г. Асака': 'Asaka city',
            'город Денау': 'Denov city',
            'г. Денау': 'Denov city',
            'город Акташ': 'Oqtosh city',
            'г. Акташ': 'Oqtosh city',
            'город Шахрисабз': 'Shahrisabz city',
            'г. Шахрисабз': 'Shahrisabz city',
            'город Китаб': 'Kitob city',
            'г. Китаб': 'Kitob city',
            'город Чуст': 'Chust city',
            'г. Чуст': 'Chust city',
            'город Риштан': 'Rishton city',
            'г. Риштан': 'Rishton city',
            'город Янгиюль': 'Yangiyul city',
            'г. Янгиюль': 'Yangiyul city'
        };

        // Joriy til
        let currentLanguage = localStorage.getItem('mapLanguage') || 'ru';

        // Loader учун скрипт
        document.addEventListener('DOMContentLoaded', function () {
            // Барча ҳарфлар дастлабда кўриниши учун
            setTimeout(function () {
                const loaderTextSpans = document.querySelectorAll('.loader-text span');
                loaderTextSpans.forEach(span => {
                    span.style.opacity = '1';
                    span.style.animation = 'none';
                });
            }, 800);

            // Барча манбалар юкланишини кутиш
            window.addEventListener('load', function () {
                setTimeout(function () {
                    const loader = document.getElementById('loader');
                    loader.style.opacity = '0';
                    loader.style.visibility = 'hidden';
                }, 800); // 0.8 секунддан сўнг лоадерни яшириш
            });
        });

        // БС статусларини глобал маълумотлар билан синхронлаштириш функцияси
        function syncStationsWithGlobalStatus(stations, points) {
            const stationStatusMap = {};
            points.forEach(point => {
                stationStatusMap[point.bsName || point.name] = point.status;
            });

            return stations.map(station => {
                const stationName = station.bsName || station.name;
                const statusFromGlobal = stationStatusMap[stationName];
                return {
                    ...station,
                    status: statusFromGlobal !== undefined ? statusFromGlobal : (station.status === 'offline')
                };
            });
        }

        // Станциялар координаталари бўйича марказни ҳисоблаш
        function calculateCenter(stations) {
            if (!stations || stations.length === 0) return null;

            let totalLat = 0;
            let totalLon = 0;
            let validStations = 0;

            stations.forEach(station => {
                if (station.lat && station.lon && !isNaN(parseFloat(station.lat)) && !isNaN(parseFloat(station.lon))) {
                    totalLat += parseFloat(station.lat);
                    totalLon += parseFloat(station.lon);
                    validStations++;
                }
            });

            if (validStations === 0) return null;

            return {
                lat: totalLat / validStations,
                lon: totalLon / validStations
            };
        }

        // БС маркерини яратиш функцияси
        function createBSMarker(point, statusValue) {
            // Координаталар тўғри эканини текшириш
            if (!point.lat || !point.lon || isNaN(parseFloat(point.lat)) || isNaN(parseFloat(point.lon))) {
                console.warn('Нотўғри координаталар:', point);
                return null;
            }

            const pointName = point.bsName || point.name;
            let pointColor = point.status === true ? 'red' : 'green';

            // Определяем, нужно ли показывать постоянную подпись (только для аварийных БС)
            const permanentTooltip = statusValue === 'offline' && point.status === true;

            const circleMarker = L.circleMarker([point.lat, point.lon], {
                radius: getCircleRadius(mymap.getZoom()),
                fillColor: pointColor,
                color: "#000",
                weight: 1,
                opacity: 1,
                fillOpacity: 0.8,
                pointName: pointName // Сохраняем имя точки в опциях маркера
            });

            // Добавляем всплывающую подсказку с улучшенным содержимым
            let tooltipContent = "";
            if (permanentTooltip) {
                // Для постоянных подсказок только имя БС
                tooltipContent = pointName;
            } else {
                // Для остальных - имя БС и статус
                let statusText = point.status === true ?
                    translations[currentLanguage]['bs_inactive'] :
                    translations[currentLanguage]['bs_active'];
                tooltipContent = "<b>" + pointName + "</b><br>" + statusText;
            }

            circleMarker.bindTooltip(tooltipContent, {
                permanent: permanentTooltip,
                direction: permanentTooltip ? 'bottom' : 'top',
                offset: permanentTooltip ? [0, 3] : [0, -10],
                className: permanentTooltip ? 'bs-name-tooltip' : ''
            });

            return circleMarker;
        }

        // Фильтр қийматларини сақлаш учун функция
        function saveFilterState() {
            const filterState = {
                region: document.getElementById('region').value,
                area: document.getElementById('area').value,
                bsSearch: document.getElementById('bs-search').value,
                status: document.getElementById('status').value
            };
            localStorage.setItem('filterState', JSON.stringify(filterState));
        }

        // Фильтр қийматларини тиклаш учун функция
        function restoreFilterState() {
            const savedState = localStorage.getItem('filterState');
            if (savedState) {
                const filterState = JSON.parse(savedState);
                const regionId = filterState.region;
                const areaId = filterState.area;
                const bsSearch = filterState.bsSearch || '';
                const statusValue = filterState.status;

                // Устанавливаем значения в селектах и полях
                document.getElementById('region').value = regionId;
                document.getElementById('bs-search').value = bsSearch;
                document.getElementById('status').value = statusValue;

                if (regionId) {
                    // Загружаем районы для выбранного региона
                    fetch(`/api/areas/region/${regionId}/`)
                        .then(response => response.json())
                        .then(areas => {
                            const areaSelect = document.getElementById('area');
                            areaSelect.innerHTML = '<option value="">' + translations[currentLanguage]['all_areas'] + '</option>';
                            areas.forEach(area => {
                                const option = document.createElement('option');
                                option.value = area.id;
                                const areaName = area.name;
                                option.dataset.originalName = areaName; // Сохраняем оригинальное имя для перевода
                                // Применяем перевод в зависимости от текущего языка
                                option.textContent = currentLanguage === 'ru' ?
                                    areaName :
                                    translateAreaName(areaName);
                                areaSelect.appendChild(option);
                            });
                            areaSelect.disabled = false;
                            areaSelect.value = areaId;

                            // Определяем источник данных в зависимости от фильтров
                            let dataPromise;

                            if (areaId) {
                                // Если выбран район
                                dataPromise = fetch(`/api/base-stations/area/${areaId}/`)
                                    .then(response => response.json());
                            } else {
                                // Если выбран только регион
                                dataPromise = fetch(`/api/base-stations/region/${regionId}/`)
                                    .then(response => response.json());
                            }

                            dataPromise.then(stations => {
                                if (stations.length > 0) {
                                    // Умумий БС маълумотларидан статус қийматларини олиш
                                    const stationStatusMap = {};
                                    points.forEach(point => {
                                        // БС номи бўйича статус қийматини сақлаш
                                        stationStatusMap[point.bsName || point.name] = point.status;
                                    });

                                    stations = stations.map(station => {
                                        // БС номи бўйича умумий маълумотлардан статусни олиш
                                        const stationName = station.bsName || station.name;
                                        const statusFromGlobal = stationStatusMap[stationName];

                                        return {
                                            ...station,
                                            // Агар умумий маълумотларда статус топилса, шуни ишлатиш, акс ҳолда API дан қайтган статусни ишлатиш
                                            status: statusFromGlobal !== undefined ? statusFromGlobal : (station.status === 'offline')
                                        };
                                    });

                                    // Если задан поиск по имени БС, применяем фильтр
                                    if (bsSearch) {
                                        stations = filterByBsName(stations, bsSearch);
                                    }

                                    // Если есть отфильтрованные станции
                                    if (stations.length > 0) {
                                        // Вычисляем центр
                                        let totalLat = 0;
                                        let totalLon = 0;
                                        stations.forEach(station => {
                                            totalLat += parseFloat(station.lat);
                                            totalLon += parseFloat(station.lon);
                                        });
                                        const centerLat = totalLat / stations.length;
                                        const centerLon = totalLon / stations.length;

                                        // Центрируем карту
                                        const zoomLevel = areaId ? 11 : 10;
                                        mymap.setView([centerLat, centerLon], zoomLevel);

                                        // Применяем фильтр по статусу
                                        applyStatusFilter(stations, statusValue);
                                    } else {
                                        // Если нет станций после фильтрации по имени
                                        pointLayers.forEach(marker => mymap.removeLayer(marker));
                                        pointLayers.length = 0;

                                        // Обновляем статистику
                                        document.getElementById('total-bs-count').textContent = '0';
                                        document.getElementById('active-bs-count').textContent = '0';
                                        document.getElementById('inactive-bs-count').textContent = '0';
                                    }
                                }
                            });
                        });
                } else {
                    // Если регион не выбран, применяем фильтры к общим данным
                    let filteredPoints = [...points];

                    // Если задан поиск по имени БС, применяем фильтр
                    if (bsSearch) {
                        filteredPoints = filterByBsName(filteredPoints, bsSearch);
                    }

                    // Применяем фильтр по статусу
                    applyStatusFilter(filteredPoints, statusValue);
                }
            } else {
                // Если нет сохраненных фильтров, показываем все точки
                applyStatusFilter(points, 'all');
            }
        }

        // Умумий статистика қийматлари
        let globalTotalBsCount = 0;
        let globalActiveBsCount = 0;
        let globalInactiveBsCount = 0;

        // Ҳар бир вилоят учун статистика тайёрлаш
        let regionsStatsData = {};

        // Вилоят статистикасини янгилаш функцияси
        function updateRegionsStats() {
            const container = document.getElementById('regions-stats-container');
            container.innerHTML = '';

            // Фаол вилоят id-сини олиш
            const activeRegionId = document.getElementById('region').value;

            // Республика Ўзбекистон сатрини жадвалнинг бошига қўшиш
            const uzbekistanRow = document.createElement('tr');
            uzbekistanRow.className = activeRegionId === '' ? 'active' : '';
            uzbekistanRow.dataset.regionId = '';

            // Ўзбекистон номи
            const uzNameCell = document.createElement('td');
            uzNameCell.className = 'region-name-cell';
            uzNameCell.textContent = translations[currentLanguage]['all_regions'];
            uzNameCell.dataset.statType = 'all';
            uzbekistanRow.appendChild(uzNameCell);

            // Умумий БС сони
            const uzTotalCell = document.createElement('td');
            uzTotalCell.className = 'total-stat';
            uzTotalCell.textContent = globalTotalBsCount;
            uzTotalCell.dataset.statType = 'all';
            uzbekistanRow.appendChild(uzTotalCell);

            // Онлайн БС сони
            const uzOnlineCell = document.createElement('td');
            uzOnlineCell.className = 'online-stat';
            uzOnlineCell.textContent = globalActiveBsCount;
            uzOnlineCell.dataset.statType = 'online';
            uzbekistanRow.appendChild(uzOnlineCell);

            // Оффлайн БС сони
            const uzOfflineCell = document.createElement('td');
            uzOfflineCell.className = 'offline-stat';
            uzOfflineCell.textContent = globalInactiveBsCount;
            uzOfflineCell.dataset.statType = 'offline';
            uzbekistanRow.appendChild(uzOfflineCell);

            container.appendChild(uzbekistanRow);

            // Ўзбекистон сатрига клик қўшиш
            uzbekistanRow.addEventListener('click', function (e) {
                const statType = e.target.dataset.statType || 'all';

                // Умумий фильтр танлаш ва тозалаш
                document.getElementById('region').value = '';
                document.getElementById('area').value = '';
                document.getElementById('area').disabled = true;
                document.getElementById('status').value = statType;

                // Картани умумий кўринишга қайтариш
                mymap.setView([41.3, 69.3], 6);

                // Умумий БС лар фильтрини қўллаш
                let filteredPoints = [...points];
                applyStatusFilter(filteredPoints, statType);

                // Фильтр ҳолатини сақлаш
                saveFilterState();

                // Вилоятлар статистикасини янгилаш
                updateRegionsStats();
            });

            // Ҳар бир вилоят учун элементлар яратиш
            Object.keys(regionsStatsData).forEach(regionId => {
                const region = regionsStatsData[regionId];
                const isActive = activeRegionId === regionId;

                const row = document.createElement('tr');
                row.className = isActive ? 'active' : '';
                row.dataset.regionId = regionId;

                // Вилоят номи - use originalName for translation if available
                const nameCell = document.createElement('td');
                nameCell.className = 'region-name-cell';
                const regionName = region.originalName || region.name;
                nameCell.textContent = currentLanguage === 'ru' ?
                    regionName : translateRegionName(regionName);
                nameCell.dataset.statType = 'all';
                row.appendChild(nameCell);

                // Умумий БС сони
                const totalCell = document.createElement('td');
                totalCell.className = 'total-stat';
                totalCell.textContent = region.total;
                totalCell.dataset.statType = 'all';
                row.appendChild(totalCell);

                // Онлайн БС сони
                const onlineCell = document.createElement('td');
                onlineCell.className = 'online-stat';
                onlineCell.textContent = region.online;
                onlineCell.dataset.statType = 'online';
                row.appendChild(onlineCell);

                // Оффлайн БС сони
                const offlineCell = document.createElement('td');
                offlineCell.className = 'offline-stat';
                offlineCell.textContent = region.offline;
                offlineCell.dataset.statType = 'offline';
                row.appendChild(offlineCell);

                container.appendChild(row);

                // Вилоят саторига клик event listener қўшиш
                row.addEventListener('click', function (e) {
                    const regionId = this.dataset.regionId;
                    const statType = e.target.dataset.statType || 'all';

                    // Вилоят танлаш
                    document.getElementById('region').value = regionId;

                    // Районлар селектини тозалаш
                    const areaSelect = document.getElementById('area');
                    areaSelect.innerHTML = '<option value=\"\" id=\"all-areas\">' + translations[currentLanguage]['all_areas'] + '</option>';

                    // Статус фильтрини ўрнатиш
                    document.getElementById('status').value = statType;

                    // Вилоят учун районларни юклаш
                    fetch(`/api/areas/region/${regionId}/`)
                        .then(response => response.json())
                        .then(areas => {
                            areas.forEach(area => {
                                const option = document.createElement('option');
                                option.value = area.id;
                                const areaName = area.name;
                                option.dataset.originalName = areaName;
                                option.textContent = currentLanguage === 'ru' ?
                                    areaName : translateAreaName(areaName);
                                areaSelect.appendChild(option);
                            });
                            areaSelect.disabled = false;
                        });

                    // Вилоят учун БС ларни юклаш ва картани вилоят бўйича марказлаштириш
                    fetch(`/api/base-stations/region/${regionId}/`)
                        .then(response => response.json())
                        .then(stations => {
                            if (stations.length > 0) {
                                // Глобал маълумотлар билан станциялар статусини синхронлаштириш
                                stations = syncStationsWithGlobalStatus(stations, points);

                                // Вилоят марказини ҳисоблаш
                                const center = calculateCenter(stations);
                                if (center) {
                                    mymap.setView([center.lat, center.lon], 10);
                                } else if (regionCenters[regionId]) {
                                    mymap.setView(regionCenters[regionId], 10);
                                }

                                // Статус фильтрини қўллаш
                                applyStatusFilter(stations, statType);
                            } else {
                                // Агар вилоят учун БС маълумотлар бўлмаса
                                mymap.setView(regionCenters[regionId], 10);
                            }
                        });

                    // Фильтр ҳолатини сақлаш
                    saveFilterState();

                    // Вилоятлар статистикасини янгилаш
                    updateRegionsStats();
                });
            });
        }

        // Вилоятлар статистикасини тайёрлаш функцияси
        function prepareRegionsStats(callback) {
            // Регионлар номларини олиш
            fetch('/api/regions/')
                .then(response => response.json())
                .then(regions => {
                    // Рес. Узбекистан регионини фильтрлаб ташлаш
                    regions = regions.filter(region => region.name !== 'Рес. Узбекистан');

                    // Ҳар бир регион учун БС ларни олиш
                    const promises = regions.map(region => {
                        return fetch(`/api/base-stations/region/${region.id}/`)
                            .then(response => response.json())
                            .then(stations => {
                                // Статусларни синхронлаштириш
                                stations = syncStationsWithGlobalStatus(stations, points);

                                const total = stations.length;
                                const offline = stations.filter(station => station.status === true).length;
                                const online = total - offline;

                                // Save original name to allow proper translation
                                regionsStatsData[region.id] = {
                                    name: region.name,
                                    originalName: region.name, // Store original Russian name
                                    total: total,
                                    online: online,
                                    offline: offline
                                };
                            });
                    });

                    // Барча сўровлар тугагандан сўнг статистика элементларини яратиш
                    Promise.all(promises).then(() => {
                        updateRegionsStats();
                        // If there's a callback, execute it
                        if (typeof callback === 'function') {
                            callback();
                        }
                    });
                });
        }

        // Маълумотларни янгилаш функцияси
        function refreshData() {
            // Сохраняем текущее состояние фильтров перед обновлением
            const currentRegion = document.getElementById('region').value;
            const currentArea = document.getElementById('area').value;
            const currentBsSearch = document.getElementById('bs-search').value;
            const currentStatus = document.getElementById('status').value;

            // Сохраняем состояние в localStorage
            saveFilterState();

            fetch(window.location.href)
                .then(response => response.text())
                .then(html => {
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');
                    const newPointsData = JSON.parse(doc.getElementById('points-data').textContent);

                    // Сохраняем старые данные для сравнения
                    const oldPoints = [...points];

                    // Получаем все точки для дальнейшей фильтрации
                    points = newPointsData;

                    // Обновляем общую статистику (умумий статистика)
                    totalBsCount = points.length;
                    inactiveBsCount = points.filter(point => point.status === true).length;
                    activeBsCount = totalBsCount - inactiveBsCount;

                    // Глобал статистика қийматларини янгилаш
                    globalTotalBsCount = totalBsCount;
                    globalActiveBsCount = activeBsCount;
                    globalInactiveBsCount = inactiveBsCount;

                    // Pass a callback to apply language changes after stats are updated
                    prepareRegionsStats(function () {
                        // After regions are loaded, ensure proper language is shown
                        if (currentLanguage === 'en') {
                            // Apply translations to region names in select options
                            const regionSelect = document.getElementById('region');
                            Array.from(regionSelect.options).forEach(option => {
                                if (option.value !== '') {
                                    const originalName = option.dataset.originalName || option.textContent;
                                    option.textContent = translateRegionName(originalName);
                                }
                            });

                            // Apply translations to area names in select options
                            const areaSelect = document.getElementById('area');
                            Array.from(areaSelect.options).forEach(option => {
                                if (option.value !== '') {
                                    const originalName = option.dataset.originalName || option.textContent;
                                    option.textContent = translateAreaName(originalName);
                                }
                            });

                            // Update BS status tooltips
                            updateBSStatusText();
                        }
                    });

                    // Восстанавливаем состояние фильтров
                    document.getElementById('region').value = currentRegion;
                    document.getElementById('area').value = currentArea;
                    document.getElementById('bs-search').value = currentBsSearch;
                    document.getElementById('status').value = currentStatus;

                    // Применяем фильтры в зависимости от текущего состояния
                    const regionId = currentRegion ? parseInt(currentRegion) : null;
                    const areaId = currentArea ? parseInt(currentArea) : null;

                    // Применяем фильтры в зависимости от выбранных значений
                    if (regionId) {
                        // Если выбран регион
                        if (areaId) {
                            // Если выбран район
                            fetch(`/api/base-stations/area/${areaId}/`)
                                .then(response => response.json())
                                .then(stations => {
                                    // Синхронизируем статусы с глобальными данными
                                    const stationStatusMap = {};
                                    points.forEach(point => {
                                        stationStatusMap[point.bsName || point.name] = point.status;
                                    });

                                    stations = stations.map(station => {
                                        const stationName = station.bsName || station.name;
                                        const statusFromGlobal = stationStatusMap[stationName];
                                        return {
                                            ...station,
                                            status: statusFromGlobal !== undefined ? statusFromGlobal : (station.status === 'offline')
                                        };
                                    });

                                    // Применяем фильтр по имени/номеру БС
                                    if (currentBsSearch) {
                                        stations = filterByBsName(stations, currentBsSearch);
                                    }

                                    // Применяем фильтр по статусу
                                    applyStatusFilter(stations, currentStatus);

                                    // Если включен английский язык, обновляем переводы названий районов
                                    if (currentLanguage === 'en') {
                                        const areaSelect = document.getElementById('area');
                                        Array.from(areaSelect.options).forEach(option => {
                                            if (option.value !== '') {
                                                const originalName = option.dataset.originalName || option.textContent;
                                                option.textContent = translateAreaName(originalName);
                                            }
                                        });
                                    }
                                });
                        } else {
                            // Если выбран только регион
                            fetch(`/api/base-stations/region/${regionId}/`)
                                .then(response => response.json())
                                .then(stations => {
                                    // Синхронизируем статусы с глобальными данными
                                    const stationStatusMap = {};
                                    points.forEach(point => {
                                        stationStatusMap[point.bsName || point.name] = point.status;
                                    });

                                    stations = stations.map(station => {
                                        const stationName = station.bsName || station.name;
                                        const statusFromGlobal = stationStatusMap[stationName];
                                        return {
                                            ...station,
                                            status: statusFromGlobal !== undefined ? statusFromGlobal : (station.status === 'offline')
                                        };
                                    });

                                    // Применяем фильтр по имени/номеру БС
                                    if (currentBsSearch) {
                                        stations = filterByBsName(stations, currentBsSearch);
                                    }

                                    // Применяем фильтр по статусу
                                    applyStatusFilter(stations, currentStatus);

                                    // Если выбран регион, загружаем соответствующие районы и переводим их на инглиз
                                    fetch(`/api/areas/region/${regionId}/`)
                                        .then(response => response.json())
                                        .then(areas => {
                                            const areaSelect = document.getElementById('area');
                                            // Сначала сохраняем текущий выбор
                                            const currentAreaValue = areaSelect.value;
                                            // Очищаем список
                                            areaSelect.innerHTML = '<option value="">' + translations[currentLanguage]['all_areas'] + '</option>';
                                            // Заполняем список районов с учётом перевода
                                            areas.forEach(area => {
                                                const option = document.createElement('option');
                                                option.value = area.id;
                                                const areaName = area.name;
                                                option.dataset.originalName = areaName;
                                                option.textContent = currentLanguage === 'ru' ?
                                                    areaName : translateAreaName(areaName);
                                                areaSelect.appendChild(option);
                                            });
                                            // Восстанавливаем выбор
                                            areaSelect.value = currentAreaValue;
                                            areaSelect.disabled = false;
                                        });
                                });
                        }
                    } else {
                        // Если не выбран ни регион, ни район
                        // Фильтруем по имени/номеру БС если задано
                        let filteredPoints = [...points];
                        if (currentBsSearch) {
                            filteredPoints = filterByBsName(filteredPoints, currentBsSearch);
                            // Если есть результаты поиска и они есть на карте, обновляем только изменившиеся
                            if (filteredPoints.length > 0) {
                                updateChangedMarkers(oldPoints, filteredPoints, currentStatus);
                            } else {
                                // Очищаем карту если нет результатов
                                pointLayers.forEach(marker => mymap.removeLayer(marker));
                                pointLayers.length = 0;

                                // Обновляем статистику
                                document.getElementById('total-bs-count').textContent = '0';
                                document.getElementById('active-bs-count').textContent = '0';
                                document.getElementById('inactive-bs-count').textContent = '0';
                            }
                        } else {
                            // Обновляем только маркеры, у которых изменился статус
                            updateChangedMarkers(oldPoints, points, currentStatus);
                        }
                    }
                })
                .catch(error => {
                    console.error('Маълумотларни янгилашда хато:', error);
                    // Хато бўлганда ҳам эски маркерларни сақлаб қолиш учун
                    if (points && points.length > 0) {
                        const statusValue = document.getElementById('status').value;
                        applyStatusFilter(points, statusValue, true);
                    }
                });
        }

        // Функция для фильтрации БС по имени или номеру
        function filterByBsName(stations, searchText) {
            searchText = searchText.toLowerCase();
            return stations.filter(station => {
                const bsName = (station.bsName || station.name || '').toLowerCase();
                const bsNumber = (station.bsNumber || station.number || '').toString().toLowerCase();
                return bsName.includes(searchText) || bsNumber.includes(searchText);
            });
        }

        // Функция для обновления только изменившихся маркеров (для режима все регионы)
        function updateChangedMarkers(oldPoints, newPoints, statusValue) {
            // Создаем карту для быстрого поиска точек по имени
            const oldPointsMap = {};
            oldPoints.forEach(point => {
                const pointName = point.bsName || point.name;
                oldPointsMap[pointName] = point;
            });

            // Фильтруем точки в соответствии с выбранным статусом
            let filteredNewPoints = [...newPoints];
            if (statusValue === 'online') {
                filteredNewPoints = newPoints.filter(point => point.status === false);
            } else if (statusValue === 'offline') {
                filteredNewPoints = newPoints.filter(point => point.status === true);
            }

            // Обновляем статистику
            const totalDisplayed = filteredNewPoints.length;
            const inactiveDisplayed = filteredNewPoints.filter(point => point.status === true).length;
            const activeDisplayed = totalDisplayed - inactiveDisplayed;

            document.getElementById('total-bs-count').textContent = totalDisplayed;
            document.getElementById('active-bs-count').textContent = activeDisplayed;
            document.getElementById('inactive-bs-count').textContent = inactiveDisplayed;

            // Создаем карту точек на карте по имени для быстрого доступа
            const markersMap = {};
            pointLayers.forEach(marker => {
                const pointName = marker.options.pointName;
                markersMap[pointName] = marker;
            });

            // Проверяем и обновляем существующие маркеры
            filteredNewPoints.forEach(point => {
                const pointName = point.bsName || point.name;

                // Если этот маркер уже есть на карте - обновляем его
                if (markersMap[pointName]) {
                    const marker = markersMap[pointName];
                    const oldPoint = oldPointsMap[pointName];

                    // Проверяем, изменился ли статус
                    if (oldPoint && oldPoint.status !== point.status) {
                        // Изменяем цвет маркера
                        const newColor = point.status === true ? 'red' : 'green';
                        marker.setStyle({ fillColor: newColor });

                        // Определяем, нужно ли показывать постоянную подпись (только для аварийных БС)
                        const permanentTooltip = statusValue === 'offline' && point.status === true;

                        // Удаляем старую подсказку
                        marker.unbindTooltip();

                        // Добавляем новую подсказку с улучшенным содержимым
                        let tooltipContent = "";
                        if (permanentTooltip) {
                            // Для постоянных подсказок только имя БС
                            tooltipContent = pointName;
                        } else {
                            // Для остальных - имя БС и статус
                            let statusText = point.status === true ?
                                translations[currentLanguage]['bs_inactive'] :
                                translations[currentLanguage]['bs_active'];
                            tooltipContent = "<b>" + pointName + "</b><br>" + statusText;
                        }

                        marker.bindTooltip(tooltipContent, {
                            permanent: permanentTooltip,
                            direction: permanentTooltip ? 'bottom' : 'top',
                            offset: permanentTooltip ? [0, 3] : [0, -10],
                            className: permanentTooltip ? 'bs-name-tooltip' : ''
                        });

                        // Выделяем изменившиеся маркеры анимацией
                        marker.setStyle({ fillOpacity: 1 });
                        setTimeout(() => {
                            marker.setStyle({ fillOpacity: 0.8 });
                        }, 500);
                    }
                    // Помечаем маркер как обработанный
                    markersMap[pointName] = null;
                }
                // Если такого маркера нет, создаем новый
                else {
                    // Создаем маркер с помощью функции
                    const circleMarker = createBSMarker(point, statusValue);
                    if (circleMarker) {
                        circleMarker.addTo(mymap);
                        pointLayers.push(circleMarker);
                    }
                }
            });

            // Удаляем маркеры, которых больше нет в новых данных
            const newPointNames = filteredNewPoints.map(p => p.bsName || p.name);

            for (let i = pointLayers.length - 1; i >= 0; i--) {
                const marker = pointLayers[i];
                const pointName = marker.options.pointName;

                if (!newPointNames.includes(pointName)) {
                    mymap.removeLayer(marker);
                    pointLayers.splice(i, 1);
                }
            }
        }

        // Функция для применения фильтра по статусу и отображения точек
        function applyStatusFilter(pointsToFilter, statusValue, isRefresh = false) {
            // Если это не рефреш или выбран регион/район, удаляем все маркеры и создаем заново
            if (!isRefresh || document.getElementById('region').value) {
                // Удаляем все существующие маркеры с карты
                pointLayers.forEach(marker => mymap.removeLayer(marker));
                pointLayers.length = 0;
            }

            console.log('Фильтрга келган маълумотлар:', pointsToFilter);
            let displayPoints = [...pointsToFilter];

            // Фильтр по статусу - это второй уровень фильтрации после региона/района
            if (statusValue === 'online') {
                displayPoints = pointsToFilter.filter(point => point.status === false);
                console.log('Онлайн фильтр натижаси:', displayPoints);
            } else if (statusValue === 'offline') {
                displayPoints = pointsToFilter.filter(point => point.status === true);
                console.log('Оффлайн фильтр натижаси:', displayPoints);
            }

            // Обновляем только локальную статистику для текущего выбора
            const totalDisplayed = displayPoints.length;
            const inactiveDisplayed = displayPoints.filter(point => point.status === true).length;
            const activeDisplayed = totalDisplayed - inactiveDisplayed;
            console.log('Фильтр натижаси - Жами:', totalDisplayed, 'Онлайн:', activeDisplayed, 'Оффлайн:', inactiveDisplayed);

            // Обновляем статистику в сайдбаре
            document.getElementById('total-bs-count').textContent = totalDisplayed;
            document.getElementById('active-bs-count').textContent = activeDisplayed;
            document.getElementById('inactive-bs-count').textContent = inactiveDisplayed;

            // Если это рефреш и не выбран регион/район, обновление маркеров выполняет updateChangedMarkers
            if (isRefresh && !document.getElementById('region').value) {
                return;
            }

            // Отображаем отфильтрованные точки на карте
            if (displayPoints && displayPoints.length > 0) {
                displayPoints.forEach(function (point) {
                    // Создаем маркер с помощью функции
                    const circleMarker = createBSMarker(point, statusValue);
                    if (circleMarker) {
                        circleMarker.addTo(mymap);
                        pointLayers.push(circleMarker);
                    }
                });
                console.log('Картага қўшилган нуқталар сони:', pointLayers.length);
            } else {
                console.warn('Кўрсатиш учун нуқталар мавжуд эмас!');
            }
        }

        // Ҳар 10 секундда маълумотларни янгилаш
        setInterval(refreshData, 10000);

        // 1. Инициализация карты
        var mymap = L.map('mapid').setView([41.3, 69.3], 6) // Марказ Ўзбекистон - умумий кўриниш

        // Qidiruv markeri
        var searchMarker = null;

        // Qidiruv funksiyasi
        function searchLocation(query) {
            // Agar bo'sh so'rov bo'lsa, hech narsa qilmaymiz
            if (!query.trim()) return;

            // Joriy tilga qarab API so'rovini sozlash
            const acceptLanguage = currentLanguage === 'en' ? 'en' : 'ru';

            // Nominatim API orqali qidiruv - accept-language parametri qo'shildi
            fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(query)}&accept-language=${acceptLanguage}`)
                .then(response => response.json())
                .then(data => {
                    if (data && data.length > 0) {
                        const result = data[0];
                        const lat = parseFloat(result.lat);
                        const lon = parseFloat(result.lon);

                        // Kartani topilgan joyga ko'chirish
                        mymap.setView([lat, lon], 13);

                        // Agar oldingi marker bo'lsa, uni o'chiramiz
                        if (searchMarker) {
                            mymap.removeLayer(searchMarker);
                        }

                        // Yangi marker qo'shamiz
                        searchMarker = L.marker([lat, lon]).addTo(mymap);

                        // Natija nomini joriy tilga moslashtirish
                        let locationName = result.display_name;

                        // Marker popup matnini o'rnatish
                        searchMarker.bindPopup(locationName).openPopup();
                    } else {
                        // Xabarni joriy tilga moslashtirish
                        const errorMessage = translations[currentLanguage]['location_not_found'];
                        alert(errorMessage);
                    }
                })
                .catch(error => {
                    console.error('Qidiruv xatosi:', error);
                    // Xabarni joriy tilga moslashtirish
                    const errorMessage = translations[currentLanguage]['search_error'];
                    alert(errorMessage);
                });
        }

        // Qidiruv tugmasiga hodisa qo'shish
        document.getElementById('search-button').addEventListener('click', function () {
            const query = document.getElementById('location-search').value;
            searchLocation(query);
        });

        // Enter tugmasini bosish orqali qidiruv
        document.getElementById('location-search').addEventListener('keypress', function (e) {
            if (e.key === 'Enter') {
                const query = this.value;
                searchLocation(query);
            }
        });

        // Til o'zgartirilganda qidiruv matnini yangilash
        function updateSearchPlaceholder() {
            document.getElementById('location-search').placeholder = translations[currentLanguage]['search_location'];
            document.getElementById('search-button').title = translations[currentLanguage]['search_button'];
        }

        // 2. Добавление базовых слоев карты
        // OpenStreetMap слои
        var osmMapLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(mymap);

        var osmSatelliteLayer = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
            attribution: '&copy; Esri &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community',
            maxZoom: 20
        });

        // Яндекс карта слои
        var yandexMapLayer = L.tileLayer('https://core-renderer-tiles.maps.yandex.net/tiles?l=map&x={x}&y={y}&z={z}', {
            attribution: '&copy; <a href="https://yandex.ru/maps">Яндекс Карты</a>',
            maxZoom: 19
        });

        var yandexSatelliteLayer = L.tileLayer('https://core-sat.maps.yandex.net/tiles?l=sat&x={x}&y={y}&z={z}', {
            attribution: '&copy; <a href="https://yandex.ru/maps">Яндекс Карты</a>',
            maxZoom: 19
        });

        // Текущие активные слои
        var currentMapLayer = osmMapLayer;
        var currentSatelliteLayer = osmSatelliteLayer;
        var isMapMode = true; // По умолчанию режим карты

        // 4. Получение данных о точках
        const pointsDataElement = document.getElementById('points-data');
        var points = JSON.parse(pointsDataElement.textContent);
        console.log('Умумий БС маълумотлари:', points);

        // Подсчет статистики БС
        var totalBsCount = points.length;
        var inactiveBsCount = points.filter(point => point.status === true).length;
        var activeBsCount = totalBsCount - inactiveBsCount;
        console.log('Умумий БС сони:', totalBsCount);
        console.log('Оффлайн БС сони:', inactiveBsCount);
        console.log('Онлайн БС сони:', activeBsCount);

        // Глобал статистика қийматларини сақлаш
        globalTotalBsCount = totalBsCount;
        globalActiveBsCount = activeBsCount;
        globalInactiveBsCount = inactiveBsCount;

        // Отображение статистики
        document.getElementById('total-bs-count').textContent = totalBsCount;
        document.getElementById('active-bs-count').textContent = activeBsCount;
        document.getElementById('inactive-bs-count').textContent = inactiveBsCount;

        // Функция для определения размера круга в зависимости от уровня зума
        function getCircleRadius(zoom) {
            if (zoom < 6) {
                return 2;
            } else if (zoom < 10) {
                return 4;
            } else {
                return 10;
            }
        }

        // Функция для обновления размера кругов и текста на карте
        function updateCircleSizes() {
            const currentZoom = mymap.getZoom();
            const newRadius = getCircleRadius(currentZoom);

            // Определяем класс для масштаба текста
            let zoomClass = '';
            if (currentZoom < 8) {
                zoomClass = 'zoom-level-low';
            } else if (currentZoom < 12) {
                zoomClass = 'zoom-level-medium';
            } else {
                zoomClass = 'zoom-level-high';
            }

            // Сначала удаляем все классы масштаба
            document.body.classList.remove('zoom-level-low', 'zoom-level-medium', 'zoom-level-high');
            // Добавляем нужный класс
            document.body.classList.add(zoomClass);

            // Обновляем радиус всех маркеров
            mymap.eachLayer(function (layer) {
                if (layer instanceof L.CircleMarker) {
                    layer.setRadius(newRadius);

                    // Если это аварийный БС с постоянной подсказкой, обновляем offset подсказки
                    if (layer.getTooltip() && layer.getTooltip().options.permanent) {
                        // Обновляем offset в зависимости от зума
                        let offsetValue = 3;
                        if (currentZoom < 8) {
                            offsetValue = 2;
                        } else if (currentZoom >= 12) {
                            offsetValue = 4;
                        }

                        // Получаем текущий контент подсказки
                        const tooltipContent = layer.getTooltip().getContent();

                        // Удаляем старую подсказку
                        layer.unbindTooltip();

                        // Создаем новую подсказку с обновленным offset
                        layer.bindTooltip(tooltipContent, {
                            permanent: true,
                            direction: 'bottom',
                            offset: [0, offsetValue],
                            className: 'bs-name-tooltip'
                        });
                    }
                }
            });
        }

        // Создаем и добавляем маркеры на карту
        const pointLayers = []; // Массив для хранения слоев маркеров
        const pointsByRegion = {}; // Объект для хранения точек по регионам
        const pointsByArea = {}; // Объект для хранения точек по районам

        // Координаты центров регионов
        const regionCenters = {
            1: [41.311081, 69.240562], // Тошкент шаҳри
            2: [40.783555, 72.350891], // Андижон
            3: [40.384240, 71.785690], // Фарғона
            4: [41.001071, 71.672278], // Наманган
            5: [39.768083, 64.421710], // Бухоро
            6: [40.121462, 67.842194], // Жиззах
            7: [38.839802, 65.781462], // Қашқадарё
            8: [40.103922, 65.374260], // Навоий
            9: [41.248990, 69.333240], // Тошкент вилояти
            10: [40.837641, 68.661338], // Сирдарё
            11: [37.940552, 67.510929], // Сурхондарё
            12: [41.552080, 60.631622], // Хоразм
            13: [43.804363, 59.018464], // Қорақалпоғистон
            14: [39.654388, 66.975824]  // Самарқанд
        };

        // Загрузка регионов
        fetch('/api/regions/')
            .then(response => response.json())
            .then(regions => {
                const regionSelect = document.getElementById('region');
                regions.forEach(region => {
                    const option = document.createElement('option');
                    option.value = region.id;
                    option.textContent = region.name;
                    option.dataset.originalName = region.name; // Keep original name for translation
                    regionSelect.appendChild(option);
                });

                // Вилоятлар статистикасини тайёрлаш
                prepareRegionsStats();
            });

        // Обработчик изменения региона
        document.getElementById('region').addEventListener('change', function () {
            const regionId = parseInt(this.value);
            const statusValue = document.getElementById('status').value;
            const bsSearch = document.getElementById('bs-search').value.trim();
            saveFilterState();
            const areaSelect = document.getElementById('area');

            // Очистка списка районов
            areaSelect.innerHTML = '<option value="" id="all-areas">' + translations[currentLanguage]['all_areas'] + '</option>';

            // Если выбрано "Все регионы" или значение пустое
            if (!regionId) {
                // Отключаем выбор района
                areaSelect.disabled = true;
                // Возвращаем карту к общему виду Узбекистана
                mymap.setView([41.3, 69.3], 6); // Марказ Ўзбекистон - умумий кўриниш

                // Применяем фильтр ко всем точкам
                let filteredPoints = [...points];

                // Применяем фильтр по имени/номеру БС если задан
                if (bsSearch) {
                    filteredPoints = filterByBsName(filteredPoints, bsSearch);
                }

                // Применяем фильтр по статусу
                applyStatusFilter(filteredPoints, statusValue);
                return;
            }

            if (regionId) {
                // Загрузка районов для выбранного региона
                fetch(`/api/areas/region/${regionId}/`)
                    .then(response => response.json())
                    .then(areas => {
                        areas.forEach(area => {
                            const option = document.createElement('option');
                            option.value = area.id;
                            const areaName = area.name;
                            option.dataset.originalName = areaName; // Keep original name for translation
                            // Apply translation based on current language
                            option.textContent = currentLanguage === 'ru' ?
                                areaName :
                                translateAreaName(areaName);
                            areaSelect.appendChild(option);
                        });
                        areaSelect.disabled = false;
                    });

                // Загрузка базовых станций для выбранного региона и центрирование карты
                fetch(`/api/base-stations/region/${regionId}/`)
                    .then(response => response.json())
                    .then(stations => {
                        if (stations.length > 0) {
                            // Синхронизируем статусы с глобальными данными
                            stations = syncStationsWithGlobalStatus(stations, points);

                            // Фильтрация по имени/номеру БС если задано
                            let filteredStations = stations;
                            if (bsSearch) {
                                filteredStations = filterByBsName(stations, bsSearch);
                            }

                            // Если есть станции после фильтрации
                            if (filteredStations.length > 0) {
                                // Вычисляем центр региона на основе координат станций
                                const center = calculateCenter(filteredStations);
                                if (center) {
                                    // Центрируем карту на вычисленном центре региона
                                    mymap.setView([center.lat, center.lon], 10);
                                } else if (regionCenters[regionId]) {
                                    // Используем предопределенный центр региона
                                    mymap.setView(regionCenters[regionId], 10);
                                }

                                // Применяем фильтр по статусу к станциям региона
                                applyStatusFilter(filteredStations, statusValue);
                            } else {
                                // Если нет станций после фильтрации
                                pointLayers.forEach(marker => mymap.removeLayer(marker));
                                pointLayers.length = 0;

                                // Обновляем статистику
                                document.getElementById('total-bs-count').textContent = '0';
                                document.getElementById('active-bs-count').textContent = '0';
                                document.getElementById('inactive-bs-count').textContent = '0';

                                // Используем центр региона из заранее определенных координат
                                if (regionCenters[regionId]) {
                                    mymap.setView(regionCenters[regionId], 10);
                                }
                            }
                        }
                    });
            } else {
                // Если регион не выбран, отключаем выбор района
                areaSelect.disabled = true;

                // Возвращаем карту к общему виду
                mymap.setView([41.3, 69.3], 6);

                // Применяем фильтр ко всем точкам
                let filteredPoints = [...points];

                // Применяем фильтр по имени/номеру БС если задан
                if (bsSearch) {
                    filteredPoints = filterByBsName(filteredPoints, bsSearch);
                }

                // Применяем фильтр по статусу
                applyStatusFilter(filteredPoints, statusValue);
            }
        });

        // Обработчик изменения района
        document.getElementById('area').addEventListener('change', function () {
            const areaId = parseInt(this.value);
            const statusValue = document.getElementById('status').value;
            const bsSearch = document.getElementById('bs-search').value.trim();
            saveFilterState();

            if (areaId) {
                // Загрузка базовых станций для выбранного района
                fetch(`/api/base-stations/area/${areaId}/`)
                    .then(response => response.json())
                    .then(stations => {
                        if (stations.length > 0) {
                            // Синхронизируем статусы с глобальными данными
                            const stationStatusMap = {};
                            points.forEach(point => {
                                stationStatusMap[point.bsName || point.name] = point.status;
                            });

                            stations = stations.map(station => {
                                const stationName = station.bsName || station.name;
                                const statusFromGlobal = stationStatusMap[stationName];
                                return {
                                    ...station,
                                    status: statusFromGlobal !== undefined ? statusFromGlobal : (station.status === 'offline')
                                };
                            });

                            // Фильтрация по имени/номеру БС если задано
                            let filteredStations = stations;
                            if (bsSearch) {
                                filteredStations = filterByBsName(stations, bsSearch);
                            }

                            // Если есть станции после фильтрации
                            if (filteredStations.length > 0) {
                                // Вычисляем центр района на основе координат станций
                                let totalLat = 0;
                                let totalLon = 0;
                                filteredStations.forEach(station => {
                                    totalLat += parseFloat(station.lat);
                                    totalLon += parseFloat(station.lon);
                                });
                                const centerLat = totalLat / filteredStations.length;
                                const centerLon = totalLon / filteredStations.length;

                                // Центрируем карту на вычисленном центре района
                                mymap.setView([centerLat, centerLon], 11);

                                // Применяем фильтр по статусу к станциям района
                                applyStatusFilter(filteredStations, statusValue);
                            } else {
                                // Если нет станций после фильтрации
                                pointLayers.forEach(marker => mymap.removeLayer(marker));
                                pointLayers.length = 0;

                                // Обновляем статистику
                                document.getElementById('total-bs-count').textContent = '0';
                                document.getElementById('active-bs-count').textContent = '0';
                                document.getElementById('inactive-bs-count').textContent = '0';
                            }
                        }
                    });
            } else {
                // Если район не выбран, возвращаемся к виду региона
                const regionId = parseInt(document.getElementById('region').value);
                if (regionId) {
                    // Загружаем станции для выбранного региона
                    fetch(`/api/base-stations/region/${regionId}/`)
                        .then(response => response.json())
                        .then(stations => {
                            if (stations.length > 0) {
                                // Синхронизируем статусы с глобальными данными
                                const stationStatusMap = {};
                                points.forEach(point => {
                                    stationStatusMap[point.bsName || point.name] = point.status;
                                });

                                stations = stations.map(station => {
                                    const stationName = station.bsName || station.name;
                                    const statusFromGlobal = stationStatusMap[stationName];
                                    return {
                                        ...station,
                                        status: statusFromGlobal !== undefined ? statusFromGlobal : (station.status === 'offline')
                                    };
                                });

                                // Фильтрация по имени/номеру БС если задано
                                let filteredStations = stations;
                                if (bsSearch) {
                                    filteredStations = filterByBsName(stations, bsSearch);
                                }

                                // Если есть станции после фильтрации
                                if (filteredStations.length > 0) {
                                    if (regionCenters[regionId]) {
                                        mymap.setView(regionCenters[regionId], 8);
                                    } else {
                                        // Вычисляем центр региона на основе координат станций
                                        let totalLat = 0;
                                        let totalLon = 0;
                                        filteredStations.forEach(station => {
                                            totalLat += parseFloat(station.lat);
                                            totalLon += parseFloat(station.lon);
                                        });
                                        const centerLat = totalLat / filteredStations.length;
                                        const centerLon = totalLon / filteredStations.length;
                                        mymap.setView([centerLat, centerLon], 8);
                                    }

                                    // Применяем фильтр по статусу к станциям региона
                                    applyStatusFilter(filteredStations, statusValue);
                                } else {
                                    // Если нет станций после фильтрации
                                    pointLayers.forEach(marker => mymap.removeLayer(marker));
                                    pointLayers.length = 0;

                                    // Обновляем статистику
                                    document.getElementById('total-bs-count').textContent = '0';
                                    document.getElementById('active-bs-count').textContent = '0';
                                    document.getElementById('inactive-bs-count').textContent = '0';

                                    // Используем центр региона из заранее определенных координат
                                    if (regionCenters[regionId]) {
                                        mymap.setView(regionCenters[regionId], 8);
                                    }
                                }
                            }
                        });
                } else {
                    // Если ни регион, ни район не выбраны
                    mymap.setView([41.3, 69.3], 6); // Марказ Ўзбекистон - умумий кўриниш

                    // Применяем фильтр ко всем точкам
                    let filteredPoints = [...points];

                    // Применяем фильтр по имени/номеру БС если задан
                    if (bsSearch) {
                        filteredPoints = filterByBsName(filteredPoints, bsSearch);
                    }

                    // Применяем фильтр по статусу
                    applyStatusFilter(filteredPoints, statusValue);
                }
            }
        });

        // Статистика теперь обновляется в функции applyStatusFilter

        // Функция для сброса всех фильтров
        function resetAllFilters() {
            // Сбрасываем значения в селектах
            document.getElementById('region').value = '';
            document.getElementById('area').value = '';
            document.getElementById('bs-search').value = '';
            document.getElementById('status').value = 'all';

            // Отключаем выбор района
            document.getElementById('area').disabled = true;

            // Возвращаем карту к общему виду Узбекистана
            mymap.setView([41.3, 69.3], 6);

            // Показываем все точки
            applyStatusFilter(points, 'all');

            // Барча вилоятлар статистикасини янгилаш
            updateRegionsStats();

            // Сохраняем сброшенные фильтры в localStorage
            saveFilterState();
        }

        // Tilni o'zgartirish funksiyasi
        function changeLanguage(lang) {
            currentLanguage = lang;
            localStorage.setItem('mapLanguage', lang);

            // Statik elementlarni tarjima qilish
            document.getElementById('stats-header').textContent = translations[lang]['stats_header'];
            document.getElementById('region-header').textContent = translations[lang]['region_header'];
            document.getElementById('area-label').textContent = translations[lang]['area_label'];
            document.getElementById('bs-search-label').textContent = translations[lang]['bs_search_label'];
            document.getElementById('bs-search').placeholder = translations[lang]['bs_search_placeholder'];
            document.getElementById('reset-filters').textContent = translations[lang]['reset_filters'];
            document.getElementById('all-regions').textContent = translations[lang]['all_regions'];
            document.getElementById('all-areas').textContent = translations[lang]['all_areas'];
            document.getElementById('status-all').textContent = translations[lang]['status_all'];
            document.getElementById('status-online').textContent = translations[lang]['status_online'];
            document.getElementById('status-offline').textContent = translations[lang]['status_offline'];
            document.getElementById('map-button').title = translations[lang]['map_title'];
            document.getElementById('satellite-button').title = translations[lang]['satellite_title'];
            document.getElementById('osm-source-button').title = translations[lang]['osm_title'];
            document.getElementById('yandex-source-button').title = translations[lang]['yandex_title'];

            // Regionlar nomlarini yangilash
            const regionSelect = document.getElementById('region');
            Array.from(regionSelect.options).forEach(option => {
                if (option.value !== '') { // Agar "Vse regioni" bo'lmasa
                    const originalName = option.dataset.originalName || option.textContent;
                    option.textContent = lang === 'ru' ? originalName : translateRegionName(originalName);
                }
            });

            // Tumanlar nomlarini yangilash
            const areaSelect = document.getElementById('area');
            Array.from(areaSelect.options).forEach(option => {
                if (option.value !== '') { // Agar "Vse rayoni" bo'lmasa
                    const originalName = option.dataset.originalName || option.textContent;
                    option.textContent = lang === 'ru' ? originalName : translateAreaName(originalName);
                }
            });

            // Viloyatlar jadvalini yangilash
            updateRegionsStats();

            // Kartadagi BS statuslarini yangilash
            updateBSStatusText();

            // Qidiruv elementlarini yangilash
            updateSearchPlaceholder();

            // Til tugmalarini yangilash
            document.getElementById('lang-ru').classList.toggle('active', lang === 'ru');
            document.getElementById('lang-en').classList.toggle('active', lang === 'en');

            // Joriy filtrlarga muvofiq, kartadagi ma'lumotlarni yangilash
            const currentRegion = document.getElementById('region').value;
            const currentArea = document.getElementById('area').value;
            const currentBsSearch = document.getElementById('bs-search').value;
            const currentStatus = document.getElementById('status').value;

            if (currentRegion) {
                // Agar viloyat tanlangan bo'lsa
                if (currentArea) {
                    // Agar tuman tanlangan bo'lsa
                    fetch(`/api/base-stations/area/${currentArea}/`)
                        .then(response => response.json())
                        .then(stations => {
                            // Statuslarni global ma'lumotlar bilan sinxronlashtirish
                            stations = syncStationsWithGlobalStatus(stations, points);

                            // BS nomi bo'yicha filter qo'llash
                            if (currentBsSearch) {
                                stations = filterByBsName(stations, currentBsSearch);
                            }

                            // Status bo'yicha filter qo'llash va kartaga chizish
                            applyStatusFilter(stations, currentStatus);
                        });
                } else {
                    // Agar faqat viloyat tanlangan bo'lsa
                    fetch(`/api/base-stations/region/${currentRegion}/`)
                        .then(response => response.json())
                        .then(stations => {
                            // Statuslarni global ma'lumotlar bilan sinxronlashtirish
                            stations = syncStationsWithGlobalStatus(stations, points);

                            // BS nomi bo'yicha filter qo'llash
                            if (currentBsSearch) {
                                stations = filterByBsName(stations, currentBsSearch);
                            }

                            // Status bo'yicha filter qo'llash va kartaga chizish
                            applyStatusFilter(stations, currentStatus);
                        });
                }

                // Vil tanlangan bo'lsa, tumanlarni o'zgartirish
                fetch(`/api/areas/region/${currentRegion}/`)
                    .then(response => response.json())
                    .then(areas => {
                        const areaSelect = document.getElementById('area');
                        const currentAreaValue = areaSelect.value; // Joriy tanlangan tuman

                        // Tumanlar ro'yxatini yangilash
                        areaSelect.innerHTML = '<option value="">' + translations[currentLanguage]['all_areas'] + '</option>';

                        areas.forEach(area => {
                            const option = document.createElement('option');
                            option.value = area.id;
                            const areaName = area.name;
                            option.dataset.originalName = areaName;
                            option.textContent = currentLanguage === 'ru' ?
                                areaName : translateAreaName(areaName);
                            areaSelect.appendChild(option);
                        });

                        // Oldingi tanlangan tumanni qayta tanlash
                        areaSelect.value = currentAreaValue;
                        areaSelect.disabled = false;
                    });
            } else {
                // Agar viloyat tanlanmagan bo'lsa, hamma nuqtalarni filter qilish
                let filteredPoints = [...points];

                // BS nomi bo'yicha filter qo'llash
                if (currentBsSearch) {
                    filteredPoints = filterByBsName(filteredPoints, currentBsSearch);
                }

                // Status bo'yicha filter qo'llash va kartaga chizish
                applyStatusFilter(filteredPoints, currentStatus);
            }

            // saveFilterState funksiyasini chaqirish (filter holatini saqlash)
            saveFilterState();
        }

        // Маълумотларни янгилаш функцияси
        function refreshData() {
            // Сохраняем текущее состояние фильтров перед обновлением
            const currentRegion = document.getElementById('region').value;
            const currentArea = document.getElementById('area').value;
            const currentBsSearch = document.getElementById('bs-search').value;
            const currentStatus = document.getElementById('status').value;

            // Сохраняем состояние в localStorage
            saveFilterState();

            fetch(window.location.href)
                .then(response => response.text())
                .then(html => {
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');
                    const newPointsData = JSON.parse(doc.getElementById('points-data').textContent);

                    // Сохраняем старые данные для сравнения
                    const oldPoints = [...points];

                    // Получаем все точки для дальнейшей фильтрации
                    points = newPointsData;

                    // Обновляем общую статистику (умумий статистика)
                    totalBsCount = points.length;
                    inactiveBsCount = points.filter(point => point.status === true).length;
                    activeBsCount = totalBsCount - inactiveBsCount;

                    // Глобал статистика қийматларини янгилаш
                    globalTotalBsCount = totalBsCount;
                    globalActiveBsCount = activeBsCount;
                    globalInactiveBsCount = inactiveBsCount;

                    // Pass a callback to apply language changes after stats are updated
                    prepareRegionsStats(function () {
                        // After regions are loaded, ensure proper language is shown
                        if (currentLanguage === 'en') {
                            // Apply translations to region names in select options
                            const regionSelect = document.getElementById('region');
                            Array.from(regionSelect.options).forEach(option => {
                                if (option.value !== '') {
                                    const originalName = option.dataset.originalName || option.textContent;
                                    option.textContent = translateRegionName(originalName);
                                }
                            });

                            // Apply translations to area names in select options
                            const areaSelect = document.getElementById('area');
                            Array.from(areaSelect.options).forEach(option => {
                                if (option.value !== '') {
                                    const originalName = option.dataset.originalName || option.textContent;
                                    option.textContent = translateAreaName(originalName);
                                }
                            });

                            // Update BS status tooltips
                            updateBSStatusText();
                        }
                    });

                    // Восстанавливаем состояние фильтров
                    document.getElementById('region').value = currentRegion;
                    document.getElementById('area').value = currentArea;
                    document.getElementById('bs-search').value = currentBsSearch;
                    document.getElementById('status').value = currentStatus;

                    // Применяем фильтры в зависимости от текущего состояния
                    const regionId = currentRegion ? parseInt(currentRegion) : null;
                    const areaId = currentArea ? parseInt(currentArea) : null;

                    // Применяем фильтры в зависимости от выбранных значений
                    if (regionId) {
                        // Если выбран регион
                        if (areaId) {
                            // Если выбран район
                            fetch(`/api/base-stations/area/${areaId}/`)
                                .then(response => response.json())
                                .then(stations => {
                                    // Синхронизируем статусы с глобальными данными
                                    const stationStatusMap = {};
                                    points.forEach(point => {
                                        stationStatusMap[point.bsName || point.name] = point.status;
                                    });

                                    stations = stations.map(station => {
                                        const stationName = station.bsName || station.name;
                                        const statusFromGlobal = stationStatusMap[stationName];
                                        return {
                                            ...station,
                                            status: statusFromGlobal !== undefined ? statusFromGlobal : (station.status === 'offline')
                                        };
                                    });

                                    // Применяем фильтр по имени/номеру БС
                                    if (currentBsSearch) {
                                        stations = filterByBsName(stations, currentBsSearch);
                                    }

                                    // Применяем фильтр по статусу
                                    applyStatusFilter(stations, currentStatus);

                                    // Если включен английский язык, обновляем переводы названий районов
                                    if (currentLanguage === 'en') {
                                        const areaSelect = document.getElementById('area');
                                        Array.from(areaSelect.options).forEach(option => {
                                            if (option.value !== '') {
                                                const originalName = option.dataset.originalName || option.textContent;
                                                option.textContent = translateAreaName(originalName);
                                            }
                                        });
                                    }
                                });
                        } else {
                            // Если выбран только регион
                            fetch(`/api/base-stations/region/${regionId}/`)
                                .then(response => response.json())
                                .then(stations => {
                                    // Синхронизируем статусы с глобальными данными
                                    const stationStatusMap = {};
                                    points.forEach(point => {
                                        stationStatusMap[point.bsName || point.name] = point.status;
                                    });

                                    stations = stations.map(station => {
                                        const stationName = station.bsName || station.name;
                                        const statusFromGlobal = stationStatusMap[stationName];
                                        return {
                                            ...station,
                                            status: statusFromGlobal !== undefined ? statusFromGlobal : (station.status === 'offline')
                                        };
                                    });

                                    // Применяем фильтр по имени/номеру БС
                                    if (currentBsSearch) {
                                        stations = filterByBsName(stations, currentBsSearch);
                                    }

                                    // Применяем фильтр по статусу
                                    applyStatusFilter(stations, currentStatus);

                                    // Если выбран регион, загружаем соответствующие районы и переводим их на инглиз
                                    fetch(`/api/areas/region/${regionId}/`)
                                        .then(response => response.json())
                                        .then(areas => {
                                            const areaSelect = document.getElementById('area');
                                            // Сначала сохраняем текущий выбор
                                            const currentAreaValue = areaSelect.value;
                                            // Очищаем список
                                            areaSelect.innerHTML = '<option value="">' + translations[currentLanguage]['all_areas'] + '</option>';
                                            // Заполняем список районов с учётом перевода
                                            areas.forEach(area => {
                                                const option = document.createElement('option');
                                                option.value = area.id;
                                                const areaName = area.name;
                                                option.dataset.originalName = areaName;
                                                option.textContent = currentLanguage === 'ru' ?
                                                    areaName : translateAreaName(areaName);
                                                areaSelect.appendChild(option);
                                            });
                                            // Восстанавливаем выбор
                                            areaSelect.value = currentAreaValue;
                                            areaSelect.disabled = false;
                                        });
                                });
                        }
                    } else {
                        // Если не выбран ни регион, ни район
                        // Фильтруем по имени/номеру БС если задано
                        let filteredPoints = [...points];
                        if (currentBsSearch) {
                            filteredPoints = filterByBsName(filteredPoints, currentBsSearch);
                            // Если есть результаты поиска и они есть на карте, обновляем только изменившиеся
                            if (filteredPoints.length > 0) {
                                updateChangedMarkers(oldPoints, filteredPoints, currentStatus);
                            } else {
                                // Очищаем карту если нет результатов
                                pointLayers.forEach(marker => mymap.removeLayer(marker));
                                pointLayers.length = 0;

                                // Обновляем статистику
                                document.getElementById('total-bs-count').textContent = '0';
                                document.getElementById('active-bs-count').textContent = '0';
                                document.getElementById('inactive-bs-count').textContent = '0';
                            }
                        } else {
                            // Обновляем только маркеры, у которых изменился статус
                            updateChangedMarkers(oldPoints, points, currentStatus);
                        }
                    }
                })
                .catch(error => {
                    console.error('Маълумотларни янгилашда хато:', error);
                    // Хато бўлганда ҳам эски маркерларни сақлаб қолиш учун
                    if (points && points.length > 0) {
                        const statusValue = document.getElementById('status').value;
                        applyStatusFilter(points, statusValue, true);
                    }
                });
        }

        // BS statuslari matnlarini yangilash
        function updateBSStatusText() {
            mymap.eachLayer(function (layer) {
                if (layer instanceof L.CircleMarker && layer.getTooltip()) {
                    const pointName = layer.options.pointName;
                    const isOffline = layer.options.fillColor === 'red';

                    // Doimiy tooltiplar uchun o'zgartirishlar shart emas (faqat BS nomi ko'rsatiladi)
                    if (!layer.getTooltip().options.permanent) {
                        const statusText = isOffline ?
                            translations[currentLanguage]['bs_inactive'] :
                            translations[currentLanguage]['bs_active'];

                        // Eski tooltip-ni o'chirib, yangi matn bilan yangisini yaratish
                        layer.unbindTooltip();
                        layer.bindTooltip("<b>" + pointName + "</b><br>" + statusText, {
                            permanent: false,
                            direction: 'top',
                            offset: [0, -10]
                        });
                    }
                }
            });
        }

        // Viloyat/tuman nomini tarjima qilish funksiyasi
        function translateRegionName(name) {
            if (currentLanguage === 'ru') return name;

            // Agar ingliz tiliga tarjima mavjud bo'lsa, uni qaytarish
            return regionTranslations[name] || name;
        }

        // Tuman nomini tarjima qilish
        function translateAreaName(name) {
            if (currentLanguage === 'ru') return name;

            console.log('Tarjima qilinmoqda:', name);

            // Agar ingliz tiliga tarjima mavjud bo'lsa, uni qaytarish
            if (areaTranslations[name]) {
                console.log('Tayyor tarjima topildi:', areaTranslations[name]);
                return areaTranslations[name];
            }

            // ASCII/lotin harflaridagi "gorod" va "g." so'zlarini aniqlash
            if (name.match(/^gorod\s+(.+)$/i)) {
                const cityName = name.replace(/^gorod\s+/i, '');
                console.log('Lotin "gorod" aniqlandi:', cityName);
                const result = cityName.charAt(0).toUpperCase() + cityName.slice(1) + ' city';
                console.log('Shahar tarjimasi:', result);
                return result;
            }

            if (name.match(/^g\.\s*(.+)$/i)) {
                const cityName = name.replace(/^g\.\s*/i, '');
                console.log('Lotin "g." aniqlandi:', cityName);
                const result = cityName.charAt(0).toUpperCase() + cityName.slice(1) + ' city';
                console.log('Shahar tarjimasi:', result);
                return result;
            }

            // Kirill harflaridagi "город" va "г." so'zlarini aniqlash
            if (name.match(/^[гГ][оО][рР][оО][дД]\s+(.+)$/)) {
                const cityName = name.replace(/^[гГ][оО][рР][оО][дД]\s+/, '');
                console.log('Kirill "город" aniqlandi:', cityName);
                const transliteratedName = transliterate(cityName);
                const result = transliteratedName.charAt(0).toUpperCase() + transliteratedName.slice(1) + ' city';
                console.log('Shahar tarjimasi:', result);
                return result;
            }

            if (name.match(/^[гГ]\.\s*(.+)$/)) {
                const cityName = name.replace(/^[гГ]\.\s*/, '');
                console.log('Kirill "г." aniqlandi:', cityName);
                const transliteratedName = transliterate(cityName);
                const result = transliteratedName.charAt(0).toUpperCase() + transliteratedName.slice(1) + ' city';
                console.log('Shahar tarjimasi:', result);
                return result;
            }

            // Tumanlar uchun tarjimalar - kengaytirilgan regex
            // Ruscha tuman nomlari ko'p xil variantda bo'ladi

            // Maxsus tuman nomlarini tekshirish (masalan Dangara → Dangarinsky)
            const cleanDistrictName = cleanDistrictSuffixes(name);
            if (cleanDistrictName) {
                console.log('Maxsus tuman nomi aniqlandi:', name, '→', cleanDistrictName);
                const transliteratedName = transliterate(cleanDistrictName);
                const result = transliteratedName.charAt(0).toUpperCase() + transliteratedName.slice(1) + ' district';
                console.log('Tuman tarjimasi:', result);
                return result;
            }

            // Agar hech qanday qoidaga mos kelmasa, transliteratsiya qilib qaytaramiz
            console.log('Oddiy transliteratsiya:', name);
            return transliterate(name);
        }

        // Tuman nomini suffix (qo'shimcha)laridan tozalash
        function cleanDistrictSuffixes(name) {
            // Ko'p uchraydigan ruscha tuman qo'shimchalari
            const suffixes = [
                'ский район', 'ской район', 'ский', 'ской',
                'инский район', 'инской район', 'инский', 'инской',
                'нский район', 'нской район', 'нский', 'нской',
                'кий район', 'кой район', 'кий', 'кой',
                'ий район', 'ой район', 'ий', 'ой',
                ' район'
            ];

            // Ayrim maxsus tumanlar uchun tuzatishlar - aniq, to'g'ri nomlar
            const specialDistricts = {
                // Dastlabki nomlar
                'Дангаринский': 'Дангара',
                'Дангаринский район': 'Дангара',
                'Алтынсаринский': 'Алтынсары',
                'Алтынсаринский район': 'Алтынсары',
                'Бостанлыкский': 'Бостанлык',
                'Бостанлыкский район': 'Бостанлык',
                'Чиназский': 'Чиназ',
                'Чиназский район': 'Чиназ',
                'Зангиатинский': 'Зангиата',
                'Зангиатинский район': 'Зангиата',
                'Янгиюльский': 'Янгиюль',
                'Янгиюльский район': 'Янгиюль',
                'Пешкунский': 'Пешку',
                'Пешкунский район': 'Пешку',
                'Нарпайский': 'Нарпай',
                'Нарпайский район': 'Нарпай',

                // -инский bilan tugaydigan tumanlar - aniq nomlar
                'Асакинский': 'Асака',
                'Асакинский район': 'Асака',
                'Балыкчинский': 'Балыкчи',
                'Балыкчинский район': 'Балыкчи',
                'Булакбашинский': 'Булакбаши',
                'Булакбашинский район': 'Булакбаши',
                'Кургантепинский': 'Кургантепа',
                'Кургантепинский район': 'Кургантепа',
                'Алтынкульский': 'Алтынкуль',
                'Алтынкульский район': 'Алтынкуль',

                // Andijon viloyati tumanlari
                'Андижанский': 'Андижан',
                'Андижанский район': 'Андижан',
                'Джалакудукский': 'Джалакудук',
                'Джалакудукский район': 'Джалакудук',
                'Избасканский': 'Избаскан',
                'Избасканский район': 'Избаскан',
                'Мархаматский': 'Мархамат',
                'Мархаматский район': 'Мархамат',
                'Пахтаабадский': 'Пахтаабад',
                'Пахтаабадский район': 'Пахтаабад',
                'Улугнорский': 'Улугнор',
                'Улугнорский район': 'Улугнор',
                'Ходжаабадский': 'Ходжаабад',
                'Ходжаабадский район': 'Ходжаабад',
                'Шахриханский': 'Шахрихан',
                'Шахриханский район': 'Шахрихан',
                'Бустанский': 'Бустан',
                'Бустанский район': 'Бустан',

                // Farg'ona viloyati tumanlari
                'Алтыарыкский': 'Алтыарык',
                'Алтыарыкский район': 'Алтыарык',
                'Багдадский': 'Багдад',
                'Багдадский район': 'Багдад',
                'Бешарыкский': 'Бешарык',
                'Бешарыкский район': 'Бешарык',
                'Бувайдинский': 'Бувайда',
                'Бувайдинский район': 'Бувайда',
                'Дангаринский': 'Дангара',
                'Дангаринский район': 'Дангара',
                'Кувинский': 'Кува',
                'Кувинский район': 'Кува',
                'Куштепинский': 'Куштепа',
                'Куштепинский район': 'Куштепа',
                'Риштанский': 'Риштан',
                'Риштанский район': 'Риштан',
                'Сохский': 'Сох',
                'Сохский район': 'Сох',
                'Ташлакский': 'Ташлак',
                'Ташлакский район': 'Ташлак',
                'Узбекистанский': 'Узбекистан',
                'Узбекистанский район': 'Узбекистан',
                'Ферганский': 'Фергана',
                'Ферганский район': 'Фергана',
                'Фуркатский': 'Фуркат',
                'Фуркатский район': 'Фуркат',
                'Язъяванский': 'Язъяван',
                'Язъяванский район': 'Язъяван',

                // Namangan viloyati tumanlari
                'Касансайский': 'Касансай',
                'Касансайский район': 'Касансай',
                'Мингбулакский': 'Мингбулак',
                'Мингбулакский район': 'Мингбулак',
                'Наманганский': 'Наманган',
                'Наманганский район': 'Наманган',
                'Нарынский': 'Нарын',
                'Нарынский район': 'Нарын',
                'Папский': 'Пап',
                'Папский район': 'Пап',
                'Туракурганский': 'Туракурган',
                'Туракурганский район': 'Туракурган',
                'Уйчинский': 'Уйчи',
                'Уйчинский район': 'Уйчи',
                'Учкурганский': 'Учкурган',
                'Учкурганский район': 'Учкурган',
                'Чартакский': 'Чартак',
                'Чартакский район': 'Чартак',
                'Чустский': 'Чуст',
                'Чустский район': 'Чуст',
                'Янгикурганский': 'Янгикурган',
                'Янгикурганский район': 'Янгикурган'
            };

            // Maxsus tumanlar uchun tekshirish
            if (specialDistricts[name]) {
                return specialDistricts[name];
            }

            // -инский bilan tugaydigan tumanlar uchun maxsus yondashuv
            const inSkyRegex = /^(.+?)инский(\s+район)?$/i;
            const inSkoyRegex = /^(.+?)инской(\s+район)?$/i;

            if (name.match(inSkyRegex)) {
                const baseName = name.replace(inSkyRegex, '$1');
                console.log('-инский bilan tugagan tuman:', name, '→', baseName);
                // Bu nomlarni specialDistricts massivida tekshirish ma'qul
                return baseName + 'a'; // Ko'p tumanlarda -a bilan tugaydi (Asaka, Dangara, kabi)
            }

            if (name.match(inSkoyRegex)) {
                const baseName = name.replace(inSkoyRegex, '$1');
                console.log('-инской bilan tugagan tuman:', name, '→', baseName);
                return baseName + 'a'; // Ko'p tumanlarda -a bilan tugaydi
            }

            // Ko'p uchraydigan qo'shimchalarni tozalash
            for (const suffix of suffixes) {
                if (name.endsWith(suffix)) {
                    return name.substring(0, name.length - suffix.length);
                }
            }

            // Oxirgi imkoniyat: murakkab regex orqali barcha holatlarda ishlashga harakat
            const regexPatterns = [
                /^(.+?)инский(\s+район)?$/i,  // -инский/-инский район
                /^(.+?)ский(\s+район)?$/i,    // -ский/-ский район
                /^(.+?)ской(\s+район)?$/i,    // -ской/-ской район
                /^(.+?)кий(\s+район)?$/i,     // -кий/-кий район
                /^(.+?)ий(\s+район)?$/i,      // -ий/-ий район
                /^(.+?)\s+район$/i           // просто район
            ];

            for (const regex of regexPatterns) {
                const match = name.match(regex);
                if (match) {
                    console.log('Tuman suffiksi topildi:', regex, name, '→', match[1]);
                    return match[1];
                }
            }

            // Agar tuman suffiksini aniqlay olmasak, asosiy nomni qaytaramiz
            return name;
        }

        // Kirill harflaridan lotin harflariga o'tkazish uchun funksiya
        function transliterate(text) {
            const translitMap = {
                'а': 'a', 'б': 'b', 'в': 'v', 'г': 'g', 'д': 'd', 'е': 'e', 'ё': 'yo',
                'ж': 'zh', 'з': 'z', 'и': 'i', 'й': 'y', 'к': 'k', 'л': 'l', 'м': 'm',
                'н': 'n', 'о': 'o', 'п': 'p', 'р': 'r', 'с': 's', 'т': 't', 'у': 'u',
                'ф': 'f', 'х': 'kh', 'ц': 'ts', 'ч': 'ch', 'ш': 'sh', 'щ': 'sch', 'ъ': '',
                'ы': 'y', 'ь': '', 'э': 'e', 'ю': 'yu', 'я': 'ya',
                'А': 'A', 'Б': 'B', 'В': 'V', 'Г': 'G', 'Д': 'D', 'Е': 'E', 'Ё': 'Yo',
                'Ж': 'Zh', 'З': 'Z', 'И': 'I', 'Й': 'Y', 'К': 'K', 'Л': 'L', 'М': 'M',
                'Н': 'N', 'О': 'O', 'П': 'P', 'Р': 'R', 'С': 'S', 'Т': 'T', 'У': 'U',
                'Ф': 'F', 'Х': 'Kh', 'Ц': 'Ts', 'Ч': 'Ch', 'Ш': 'Sh', 'Щ': 'Sch', 'Ъ': '',
                'Ы': 'Y', 'Ь': '', 'Э': 'E', 'Ю': 'Yu', 'Я': 'Ya'
            };

            return text.split('').map(char => translitMap[char] || char).join('');
        }

        // Саҳифа юкланганда фильтр қийматларини тиклаш
        document.addEventListener('DOMContentLoaded', function () {
            // Til tugmalariga hodisalar qo'shish
            document.getElementById('lang-ru').addEventListener('click', function () {
                changeLanguage('ru');
            });

            document.getElementById('lang-en').addEventListener('click', function () {
                changeLanguage('en');
            });

            // Dastlabki tilni o'rnatish
            changeLanguage(currentLanguage);

            // Qidiruv elementlarini yangilash
            updateSearchPlaceholder();

            // Вилоятлар статистикасини тайёрлаш
            prepareRegionsStats();

            // Восстанавливаем фильтры из localStorage
            restoreFilterState();

            // Добавляем обработчик события для кнопки сброса фильтров
            document.getElementById('reset-filters').addEventListener('click', resetAllFilters);

            // Обработчик события для поля поиска БС
            document.getElementById('bs-search').addEventListener('input', function () {
                const searchValue = this.value.trim();
                const regionId = parseInt(document.getElementById('region').value) || null;
                const areaId = parseInt(document.getElementById('area').value) || null;
                const statusValue = document.getElementById('status').value;

                // Сохраняем состояние фильтров
                saveFilterState();

                // Удаляем все маркеры с карты
                pointLayers.forEach(marker => mymap.removeLayer(marker));
                pointLayers.length = 0;

                // Выбираем источник данных для фильтрации
                let dataToFilter;
                if (regionId) {
                    if (areaId) {
                        // Если выбран район, загружаем данные района
                        fetch(`/api/base-stations/area/${areaId}/`)
                            .then(response => response.json())
                            .then(stations => {
                                // Синхронизируем статусы с глобальными данными
                                const stationStatusMap = {};
                                points.forEach(point => {
                                    stationStatusMap[point.bsName || point.name] = point.status;
                                });

                                stations = stations.map(station => {
                                    const stationName = station.bsName || station.name;
                                    const statusFromGlobal = stationStatusMap[stationName];
                                    return {
                                        ...station,
                                        status: statusFromGlobal !== undefined ? statusFromGlobal : (station.status === 'offline')
                                    };
                                });

                                // Применяем фильтр по имени/номеру БС
                                if (searchValue) {
                                    stations = filterByBsName(stations, searchValue);
                                }

                                // Применяем фильтр по статусу
                                applyStatusFilter(stations, statusValue);
                            });
                    } else {
                        // Если выбран только регион, загружаем данные региона
                        fetch(`/api/base-stations/region/${regionId}/`)
                            .then(response => response.json())
                            .then(stations => {
                                // Синхронизируем статусы с глобальными данными
                                const stationStatusMap = {};
                                points.forEach(point => {
                                    stationStatusMap[point.bsName || point.name] = point.status;
                                });

                                stations = stations.map(station => {
                                    const stationName = station.bsName || station.name;
                                    const statusFromGlobal = stationStatusMap[stationName];
                                    return {
                                        ...station,
                                        status: statusFromGlobal !== undefined ? statusFromGlobal : (station.status === 'offline')
                                    };
                                });

                                // Применяем фильтр по имени/номеру БС
                                if (searchValue) {
                                    stations = filterByBsName(stations, searchValue);
                                }

                                // Применяем фильтр по статусу
                                applyStatusFilter(stations, statusValue);
                            });
                    }
                } else {
                    // Если не выбран ни регион, ни район, фильтруем все точки
                    let filteredPoints = [...points];

                    // Применяем фильтр по имени/номеру БС
                    if (searchValue) {
                        filteredPoints = filterByBsName(filteredPoints, searchValue);
                    }

                    // Применяем фильтр по статусу
                    applyStatusFilter(filteredPoints, statusValue);
                }
            });
        });

        // Обработчик изменения статуса
        document.getElementById('status').addEventListener('change', function () {
            const statusValue = this.value;
            const regionId = parseInt(document.getElementById('region').value) || null;
            const areaId = parseInt(document.getElementById('area').value) || null;
            const bsSearch = document.getElementById('bs-search').value.trim();

            saveFilterState();

            // Удаляем все маркеры с карты
            pointLayers.forEach(marker => mymap.removeLayer(marker));
            pointLayers.length = 0;

            // Применяем фильтры в зависимости от выбранных значений
            if (regionId) {
                // Если выбран регион
                if (areaId) {
                    // Если выбран район
                    fetch(`/api/base-stations/area/${areaId}/`)
                        .then(response => response.json())
                        .then(stations => {
                            // Синхронизируем статусы с глобальными данными
                            const stationStatusMap = {};
                            points.forEach(point => {
                                stationStatusMap[point.bsName || point.name] = point.status;
                            });

                            stations = stations.map(station => {
                                const stationName = station.bsName || station.name;
                                const statusFromGlobal = stationStatusMap[stationName];
                                return {
                                    ...station,
                                    status: statusFromGlobal !== undefined ? statusFromGlobal : (station.status === 'offline')
                                };
                            });

                            // Применяем фильтр по имени/номеру БС
                            if (bsSearch) {
                                stations = filterByBsName(stations, bsSearch);
                            }

                            // Применяем фильтр по статусу
                            applyStatusFilter(stations, statusValue);
                        });
                } else {
                    // Если выбран только регион
                    fetch(`/api/base-stations/region/${regionId}/`)
                        .then(response => response.json())
                        .then(stations => {
                            // Синхронизируем статусы с глобальными данными
                            const stationStatusMap = {};
                            points.forEach(point => {
                                stationStatusMap[point.bsName || point.name] = point.status;
                            });

                            stations = stations.map(station => {
                                const stationName = station.bsName || station.name;
                                const statusFromGlobal = stationStatusMap[stationName];
                                return {
                                    ...station,
                                    status: statusFromGlobal !== undefined ? statusFromGlobal : (station.status === 'offline')
                                };
                            });

                            // Применяем фильтр по имени/номеру БС
                            if (bsSearch) {
                                stations = filterByBsName(stations, bsSearch);
                            }

                            // Применяем фильтр по статусу
                            applyStatusFilter(stations, statusValue);
                        });
                }
            } else {
                // Если не выбран ни регион, ни район
                let filteredPoints = [...points];

                // Применяем фильтр по имени/номеру БС
                if (bsSearch) {
                    filteredPoints = filterByBsName(filteredPoints, bsSearch);
                }

                // Применяем фильтр по статусу ко всем точкам
                applyStatusFilter(filteredPoints, statusValue);
            }
        });

        // Обработчики кликов на кнопки переключения типа и источника карты
        const mapButton = document.getElementById('map-button');
        const satelliteButton = document.getElementById('satellite-button');
        const osmSourceButton = document.getElementById('osm-source-button');
        const yandexSourceButton = document.getElementById('yandex-source-button');

        // Функция для обновления слоя карты в зависимости от выбранного режима и источника
        function updateMapLayer() {
            // Удаляем текущий слой
            if (mymap.hasLayer(currentMapLayer)) {
                mymap.removeLayer(currentMapLayer);
            }
            if (mymap.hasLayer(currentSatelliteLayer)) {
                mymap.removeLayer(currentSatelliteLayer);
            }

            // Добавляем нужный слой в зависимости от режима
            if (isMapMode) {
                currentMapLayer.addTo(mymap);
            } else {
                currentSatelliteLayer.addTo(mymap);
            }
        }

        // Обработчики для режима карты
        mapButton.addEventListener('click', function () {
            isMapMode = true;
            mapButton.classList.add('active');
            satelliteButton.classList.remove('active');
            updateMapLayer();
        });

        satelliteButton.addEventListener('click', function () {
            isMapMode = false;
            satelliteButton.classList.add('active');
            mapButton.classList.remove('active');
            updateMapLayer();
        });

        // Обработчики для источника карты
        osmSourceButton.addEventListener('click', function () {
            currentMapLayer = osmMapLayer;
            currentSatelliteLayer = osmSatelliteLayer;
            osmSourceButton.classList.add('active');
            yandexSourceButton.classList.remove('active');
            updateMapLayer();
        });

        yandexSourceButton.addEventListener('click', function () {
            currentMapLayer = yandexMapLayer;
            currentSatelliteLayer = yandexSatelliteLayer;
            yandexSourceButton.classList.add('active');
            osmSourceButton.classList.remove('active');
            updateMapLayer();
        });

        // Слушаем событие 'zoomend'
        mymap.on('zoomend', updateCircleSizes);

        // Ўзбекистон чегаралари
        const uzbekistanBounds = [
            [37.1, 55.9], // Жанубий-ғарбий нуқта
            [45.6, 73.1]  // Шимолий-шарқий нуқта
        ];
        // Картани Ўзбекистон билан чегаралаш
        mymap.setMaxBounds(uzbekistanBounds);
    </script>

</body>

{% endblock content %}