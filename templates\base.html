<!DOCTYPE html>
{% load static %}

{% block extrahead %}
<script>window.CKEDITOR_BASEPATH = '/static/ckeditor/ckeditor/';</script>
{{ block.super }}
{% endblock %}

<html lang="en">
<head>
    <meta charset="UTF-8">
    <title> {% block title %}Beeline-BS Uzbekistan {% endblock title %} </title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- Google Fonts (Agar hali qo'shmagan bo'lsangiz) -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
    <!-- Bootstrap CSS -->
    <link href="{% static 'css/bootstrap.min.css' %}" rel="stylesheet">
    <link type="image/x-icon" href="{% static 'img/signal.ico' %}" rel="shortcut icon">
    <!-- Yangilangan CSS stillari -->
    <style>
        /* YUQORIDAGI YANGILANGAN CSS QOIDALARINI SHU YERGA QO'YING */
        body {
            font-family: 'Roboto', sans-serif; /* Yangi shrift */
            background-color: #f8f9fa;
        }

        .navbar {
            background-color: #343a40;
            padding-top: 0.5rem;
            padding-bottom: 0.5rem;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15); /* Soya qo'shildi */
        }

        .navbar-brand img {
            max-height: 30px;
            margin-right: 10px; /* Masofa biroz oshirildi */
        }

        .navbar-brand b {
            color: #ffc107;
            font-weight: 500; /* Shrift qalinligi (Roboto uchun) */
        }

        .navbar-nav .nav-link {
            color: rgba(255, 255, 255, 0.85); /* Matn rangi biroz xiraroq oq */
            padding: 0.5rem 1rem;
            margin: 0 0.4rem; /* Linklar orasidagi gorizontal masofa biroz oshirildi */
            border-radius: 0.3rem; /* Yumaloqlik biroz o'zgartirildi */
            transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out;
            text-align: center;
            font-weight: 500; /* Shrift qalinligi (Roboto uchun) */
        }

        .navbar-nav .nav-link:hover,
        .navbar-nav .nav-link:focus {
            color: #fff; /* Hoverda to'liq oq */
            background-color: rgba(255, 193, 7, 0.2); /* Hoverda yarim shaffof sariq fon */
        }

        /* Aktiv link uchun */
        .navbar-nav .nav-link.active {
            color: #343a40; /* Aktiv matn rangi */
            background-color: #ffc107; /* Aktiv fon rangi (asosiy sariq) */
            font-weight: 700; /* Aktiv link qalinroq */
        }

        .dropdown-menu {
            background-color: #343a40;
            border: 1px solid rgba(255, 255, 255, 0.1); /* Chegara chizig'i qo'shildi */
            margin-top: 0.5rem !important; /* Navbar va dropdown orasidagi masofa biroz oshirildi */
            border-radius: 0.3rem;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* Dropdownga ham soya */
        }

        .dropdown-menu .dropdown-item {
            color: rgba(255, 255, 255, 0.85); /* Dropdown matn rangi */
            padding: 0.6rem 1.2rem; /* Padding biroz oshirildi */
            transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out;
            font-weight: 400; /* Shrift qalinligi */
        }

        .dropdown-menu .dropdown-item:hover,
        .dropdown-menu .dropdown-item:focus {
            background-color: #ffc107; /* Hoverda sariq fon */
            color: #343a40; /* Hoverda to'q matn */
        }

         /* User menyusi (o'ng taraf) */
        .navbar-nav.ms-auto .nav-link {
            /* User nomi uchun alohida stil kerak bo'lsa */
        }

        /* User dropdown ochuvchi link (ismi) */
        #userDropdown {
             font-weight: 500; /* Foydalanuvchi nomi qalinroq */
        }


        /* Toggler (kichik ekranlar uchun) */
        .navbar-toggler {
            border-color: rgba(255, 193, 7, 0.5); /* Toggler ramkasi sariqroq */
            border-radius: 0.3rem;
        }
        .navbar-toggler:focus {
            box-shadow: 0 0 0 0.25rem rgba(255, 193, 7, 0.5); /* Focus effekti */
        }
        .navbar-toggler-icon {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 193, 7, 1%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
        }

        /* Dropdown ajratuvchi chizig'i */
        .dropdown-divider {
             border-color: rgba(255, 193, 7, 0.25) !important; /* Chiziq rangi */
        }


    </style>
</head>

<body>
<!-- QOLGAN HTML KODI O'ZGARMASDAN QOLADI -->
<nav class="navbar navbar-expand-lg navbar-dark">
    <div class="container-fluid">
        <a class="navbar-brand d-flex align-items-center" href="{% url 'bsview' %}">
            <img src="{% static 'img/logo2.png' %}" alt="Logo">
            <b>Beeline-BS</b>
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNavDropdown"
                aria-controls="navbarNavDropdown" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNavDropdown">
            {% if user.is_authenticated %}
            <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                <li class="nav-item">
                    <a class="nav-link {% if request.resolver_match.url_name == 'bsview' %}active{% endif %}"
                       aria-current="page" href="{% url 'bsview' %}">Список БС</a>
                </li>
                {% if user.privilege_id == 1 or user.privilege_id == 2 %}
                <li class="nav-item">
                    <a class="nav-link {% if request.resolver_match.url_name == 'addbs' %}active{% endif %}"
                       href="{% url 'addbs' %}">Добавить БС</a>
                </li>
                {% endif %}

                {% if user.privilege_id == 1 or user.privilege_id == 2 %}
                <li class="nav-item">
                    <a class="nav-link {% if request.resolver_match.url_name == 'createreport' %}active{% endif %}"
                       href="{% url 'createreport' %}">Разные Отчеты</a>
                </li>

                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="alarmsDropdown1" role="button"
                       data-bs-toggle="dropdown" aria-expanded="false">
                        Алармы
                    </a>
                    <ul class="dropdown-menu" aria-labelledby="alarmsDropdown1">
                        <li><a class="dropdown-item" href="{% url 'getalarms' %}">Актив Алармы</a></li>
                        <li><a class="dropdown-item" href="{% url 'alarmsall' %}">Все Актив Алармы</a></li>
                        {% if user.privilege_id == 1 %}
                        <li><a class="dropdown-item" href="{% url 'idlebts' %}">Аварии по областям</a></li>
                        {% endif %}
                        <li><a class="dropdown-item" href="{% url 'alarmstat' %}">Аларм истории</a></li>
                        <li><a class="dropdown-item" href="{% url 'alarmview' %}">Выгрузка истории</a></li>
                        <li><a class="dropdown-item" href="{% url 'alarmanalyze' %}">Анализ Аварий по БС</a></li>
                        <li><a class="dropdown-item" href="{% url 'alarmanalyzeday' %}">Анализ Аварий по Дате</a></li>
                    </ul>
                </li>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="btsDropdown" role="button"
                       data-bs-toggle="dropdown" aria-expanded="false">
                        БТС файлы
                    </a>
                    <ul class="dropdown-menu" aria-labelledby="btsDropdown">
                        <li><a class="dropdown-item" href="{% url 'createbts' %}">Делаем файл BTS для Nemo</a></li>
                        <li><a class="dropdown-item" href="{% url 'createkmz' %}">Делаем KMZ файл для Google Earth</a>
                        </li>
                        <li><a class="dropdown-item" href="{% url 'createGnet' %}">Cell файл для G-NetTrack</a></li>
                    </ul>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if request.resolver_match.url_name == 'conffiles' %}active{% endif %}"
                       href="{% url 'conffiles' %}">Conf. файлы</a> <!-- fw-bold o'chirildi, CSSda beriladi -->
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if request.resolver_match.url_name == 'importdata' %}active{% endif %}"
                       href="{% url 'importdata' %}">Импорт данных</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if request.resolver_match.url_name == 'nriview' %}active{% endif %}"
                       href="{% url 'nriview' %}">Данные с NRI</a>
                </li>
                {% endif %}
                {% if user.privilege_id == 1 %}
                <li class="nav-item">
                    <a class="nav-link {% if request.resolver_match.url_name == 'users' %}active{% endif %}"
                       href="{% url 'users' %}">Пользователи</a>
                </li>
                {% endif %}

                <li class="nav-item">
                    <a class="nav-link {% if request.resolver_match.url_name == 'mapview2' %}active{% endif %}"
                       href="{% url 'mapview2' %}">Map View</a>
                </li>
            </ul>


            <ul class="navbar-nav ms-auto">
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button"
                       data-bs-toggle="dropdown"
                       aria-expanded="false">
                        {{user.first_name}} {{user.last_name}}
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                        {% if user.privilege_id == 1 %}
                        <li><a class="dropdown-item" href="{% url 'signup' %}">Добавить пользователя</a></li>
                        {% endif %}
                        <li><a class="dropdown-item" href="{% url 'changepassword' %}">Смена пароля</a></li>
                        <li>
                            <hr class="dropdown-divider">
                        </li> <!-- Style CSS da beriladi -->
                        <li><a class="dropdown-item" href="{% url 'logout' %}">Выход</a></li>
                    </ul>
                </li>
            </ul>
            {% else %}
            <ul class="navbar-nav ms-auto">
                <li class="nav-item">
                    <a class="btn btn-outline-warning" href="{% url 'login' %}">Вход</a>
                </li>
            </ul>
            {% endif %}
        </div>
    </div>
</nav>

<main>
    <div class="container-xl mt-4">
        {% block content %}
        {% endblock content %}
    </div>
</main>

<script src="{% static 'js/bootstrap.bundle.min.js' %}"></script>

</body>
</html>