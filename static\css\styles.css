/* Global styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body,
html {
  height: 100%;
  font-family: "Roboto", Arial, sans-serif;
}

#app {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.app-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.map-wrapper {
  flex: 1;
  position: relative;
  height: 100%;
}

/* Header styles */
.app-header {
  background-color: #2c3e50;
  color: white;
  padding: 10px 20px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.header-title h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 500;
}

.header-subtitle {
  margin: 5px 0 0;
  font-size: 14px;
  opacity: 0.8;
}

.header-info {
  display: flex;
  align-items: center;
}

.status-indicator {
  display: flex;
  align-items: center;
  margin-left: 20px;
  font-size: 14px;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 5px;
}

.status-dot.online {
  background-color: #4caf50;
}

.status-dot.offline {
  background-color: #f44336;
}

/* Sidebar styles */
.sidebar {
  width: 300px;
  height: 100%;
  background-color: white;
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.sidebar-header {
  padding: 15px;
  background-color: #2c3e50;
  color: white;
}

.sidebar-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.sidebar-content {
  padding: 15px;
  overflow-y: auto;
  flex: 1;
}

.stats-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  background-color: #f5f5f5;
  border-radius: 5px;
  padding: 10px;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

.stat-item.online .stat-value {
  color: #4caf50;
}

.stat-item.offline .stat-value {
  color: #f44336;
}

.filter-group {
  margin-bottom: 15px;
}

.filter-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  font-size: 14px;
  color: #333;
}

.filter-group input,
.filter-group select {
  width: 100%;
  padding: 8px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.filter-group input:focus,
.filter-group select:focus {
  outline: none;
  border-color: #2c3e50;
}

.filter-group select:disabled {
  background-color: #f5f5f5;
  cursor: not-allowed;
}

.reset-button {
  width: 100%;
  padding: 10px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin-top: 10px;
  transition: background-color 0.2s;
}

.reset-button:hover {
  background-color: #e0e0e0;
}

.reset-button:active {
  background-color: #d0d0d0;
}

/* Map styles */
.map-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.custom-marker div {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.8);
  box-shadow: none; /* Box shadow olib tashlash */
}

.custom-marker.online div {
  background-color: rgba(76, 175, 80, 0.9); /* Yanada ko'rinarli */
}

.custom-marker.offline div {
  background-color: rgba(244, 67, 54, 0.9); /* Yanada ko'rinarli */
}

.station-label {
  font-weight: bold;
  white-space: nowrap;
  text-align: center;
  pointer-events: none;
}

.bs-name {
  padding: 0px 2px 0px 0;
  border-radius: 2px;
  background-color: rgba(255, 255, 255, 0.5); /* Yanada shaffofroq fon */
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1;
  border: none;
  font-size: 8px; /* Yanada kichikroq shrift */
  box-shadow: none; /* Box shadow olib tashlash */
  text-shadow: 0 0 1px rgba(255, 255, 255, 0.7);
  white-space: nowrap;
}

.bs-name.online {
  color: #1b5e20;
  border-left: 2px solid rgba(76, 175, 80, 0.9); /* Ingichkaroq chiziq */
  font-weight: bold;
  padding-left: 2px;
}

.bs-name.offline {
  color: #b71c1c;
  border-left: 2px solid rgba(244, 67, 54, 0.9); /* Ingichkaroq chiziq */
  font-weight: bold;
  padding-left: 2px;
}

.station-popup-container {
  min-width: 200px;
  max-width: 300px;
}

.station-popup {
  min-width: 200px;
  max-width: 280px;
  font-size: 12px;
}

.station-popup h3 {
  margin-top: 0;
  margin-bottom: 8px;
  font-size: 14px;
  border-bottom: 1px solid #ddd;
  padding-bottom: 5px;
}

.station-popup p {
  margin: 3px 0;
  font-size: 12px;
  line-height: 1.2;
}

.station-popup .popup-info div {
  margin: 3px 0;
  line-height: 1.2;
}

.station-popup .status {
  font-weight: bold;
}

.station-popup .status.online {
  color: #4caf50;
}

.station-popup .status.offline {
  color: #f44336;
}

.station-popup .alarm-info {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #ddd;
}

.station-popup .alarm-info h4 {
  margin-top: 0;
  margin-bottom: 5px;
  font-size: 13px;
  color: #f44336;
}

.station-popup .alarm-item {
  margin-bottom: 5px;
  padding: 5px;
  background-color: rgba(244, 67, 54, 0.05);
  border-radius: 3px;
}

.station-popup .alarm-item div {
  margin: 2px 0;
  line-height: 1.2;
}

.station-popup .more-alarms {
  font-size: 11px;
  font-style: italic;
  color: #888;
  margin-top: 5px;
  text-align: center;
}

.map-type-control {
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.4);
  padding: 5px;
  display: flex;
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1000;
}

.map-type-control button {
  background-color: white;
  border: 1px solid #ccc;
  padding: 6px 10px;
  cursor: pointer;
  font-size: 12px;
  outline: none;
}

.map-type-control button:first-child {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
  border-right: none;
}

.map-type-control button:last-child {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}

.map-type-control button.active {
  background-color: #f0f0f0;
  font-weight: bold;
}

/* Responsive styles */
@media (max-width: 768px) {
  .app-content {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: auto;
    max-height: 300px;
  }

  .map-wrapper {
    height: 70%;
  }

  .header-content {
    flex-direction: column;
    align-items: flex-start;
  }

  .header-info {
    margin-top: 10px;
  }

  .status-indicator {
    margin-left: 0;
    margin-right: 20px;
  }
}
