import pandas as pd
from openpyxl.reader.excel import load_workbook
from django.contrib.auth.decorators import login_required
from django.shortcuts import render, redirect
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import ListView, DetailView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.core.paginator import Paginator
from django.db import connection
from datetime import timedelta
from datetime import datetime
import bsview.models
from django.http import HttpResponse
import datetime as dt
import os
import zipfile
from io import BytesIO
from transliterate import translit
from django.shortcuts import render
from django.db.models import Count

# from functions
from bsview.forms import *
from bsview.views.functions.kmz import *
from bsview.views.functions.bts2 import *
from bsview.views.functions.bts3 import *
from bsview.views.functions.bts4 import *
from bsview.views.functions.gnet import *
from bsview.views.functions.reports import *
from bsview.views.functions.nrireports import *
from bsview.views.functions.myfunctions import *
from bsview.views.functions.serializers import *



