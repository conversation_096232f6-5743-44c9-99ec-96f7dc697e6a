﻿{% extends 'base.html' %}
{% load static %}

{% block content %}

<div class="jumbotron">
    <p>
</div>
<body>
<form action="{% url 'getbts' %}" method="post" id="getbtsform" enctype="multipart/form-data">
    {% csrf_token %}

    <div class="container card ">
        <div class="row ">
            <div class="col col-3">
                <p>
                    <b>Выберите область:</b>
                <p>
                <div class="card-header w-100 p-3">
                    <select class="form-select" multiple aria-label="multiple select example" name="regiono" id="reg" style="height: 200px;">
                        {% for reg in region %}
                        {% if reg.id == user.region_id %}
                        <option selected value="{{ reg.id }}">{{reg.name}}</option>
                        {% else %}
                        {% if user.privilege_id == 1 %}
                        <option value="{{ reg.id }}">{{reg.name}}</option>
                        {% else %}
                        <option value="{{ reg.id }}" disabled>{{reg.name}}</option>
                        {% endif %}

                        {% endif %}
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="col ">

                <p><b>Выберите диапазон:</b></p>
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="inlineRadioOptions" id="inlineRadio1"
                           value="GSM" onclick="change()" checked>
                    <label class="form-check-label" for="inlineRadio1">GSM</label>
                </div>
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="inlineRadioOptions" id="inlineRadio2"
                           value="UMTS" onclick="change2()">
                    <label class="form-check-label" for="inlineRadio2">UMTS</label>
                </div>
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="inlineRadioOptions" id="inlineRadio3"
                           value="LTE" onclick="change3()">
                    <label class="form-check-label" for="inlineRadio3">LTE</label>
                </div>
                <br>
                <br>
                <p>
                    <b id="vibor">Выбор конфигурационного файла 2G:</b>
                    <br>

                <p>
                <div class="input-group mb-3">
                    <label class="input-group-text" for="Filevibor" id="vid">Конф. файл 2G</label>
                    <input type="file" class="form-control" id="Filevibor" name="File2G" required>
                    <label class="input-group-text" for="Filevibor">Загрузка</label>
                </div>

                <div id="gsmOptions">
                    <label for="gsmSelect">Выберите Band:</label>
                    <select id="gsmSelect" class="form-select">
                        <option value="all">Все</option>
                        <option value="gsm 900">gsm 900</option>
                        <option value="gsm 1800">gsm 1800</option>
                    </select>
                </div>

                <div id="lteOptions" style="display: none;">
                    <label for="lteSelect">Выберите Band:</label>
                    <select id="lteSelect" class="form-select">
					    <option value="all">Все</option>
                        <option value="1">Band 1</option>
						<option value="5/18">Band 5/18</option>
                        <option value="7">Band 7</option>
                        <option value="38/41">Band 38/41</option>
                        <option value="40">Band 40</option>
                    </select>
                </div>

                <p>
            </div>
        </div>
        <br>
    </div>
    <br>
    <button class="btn btn-primary" type="submit" id="knopka">Создать BTS файл для 2G</button>
    <a href="{% url 'bsview' %}" class="btn btn-primary">Отмена</a>
</form>
<br>
{% if allError %}
<table class="table table-striped">
    <tr>
        <td width="10%">№</td>
        <td width="50%">Ошибка</td>
        <td width="20%">CELL_ID</td>
        <td width="20%">Изменить</td>
    </tr>

    {% for errbsname, errbs_dp, errbs_pk in allError %}

    <tr>
    <td >{{ forloop.counter }}</td>
        <td>Базе данных не указаны сектора для диапазона {{ errbs_dp }} </td>
        <td>{{ errbsname }}</td>
        <td><a href="{% url 'viewdetail' errbs_pk %}">Просмотр</a></td>

    </tr>
    {% endfor %}
</table>
{% endif %}

<script>
    const vid = document.getElementById("vid");
    const vibor = document.getElementById("vibor");
    const knopka = document.getElementById("knopka");
    const filevibor = document.getElementById("Filevibor");
    const gsmOptions = document.getElementById("gsmOptions");
    const lteOptions = document.getElementById("lteOptions");

    // Show GSM options on page load if GSM is selected
    window.onload = () => {
        if (document.getElementById("inlineRadio1").checked) {
            gsmOptions.style.display = "block";
        }
    };

    const change = () => {
          vid.innerText = "Конф. Файл 2G"
          vibor.innerText = "Выбор конфигурационного файла 2G"
          knopka.innerText = "Создать BTS файл для 2G"
          filevibor.setAttribute("name", "File2G");
          gsmOptions.style.display = "block";
          lteOptions.style.display = "none";
    };
    const change2 = () => {
         vid.innerText = "Конф. Файл 3G"
         vibor.innerText = "Выбор конфигурационного файла 3G"
         knopka.innerText = "Создать BTS файл для 3G"
         filevibor.setAttribute("name", "File3G");
         gsmOptions.style.display = "none";
         lteOptions.style.display = "none";
    };
    const change3 = () => {
         vid.innerText = "Конф. Файл 4G"
         vibor.innerText = "Выбор конфигурационного файла 4G"
         knopka.innerText = "Создать BTS файл для 4G"
         filevibor.setAttribute("name", "File4G");
         gsmOptions.style.display = "none";
         lteOptions.style.display = "block";
    };
</script>

</body>
{% endblock %}