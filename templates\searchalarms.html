{% extends 'base.html' %}
{% load static %}

{% block content %}
<script type="text/javascript" src="{% static 'js/jquery.min.js' %}"></script>
<script type="text/javascript" src="{% static 'js/moment.min.js' %}"></script>
<script type="text/javascript" src="{% static 'js/daterangepicker.js' %}"></script>
<link rel="stylesheet" type="text/css" href="{% static 'css/bootstrap.css' %}"/>
<link rel="stylesheet" type="text/css" href="{% static 'css/daterangepicker.css' %}"/>

<div class="jumbotron">
    <p>
</div>
<body>
<!--<H3>Страница находится на стадии разработки !!!</H3>-->
<!--H3>Базовая станция: {{ bsnumber }} | Количество аварии: {{ kolAlm }} | Продолжительность: {{ sumTime }} </H3>-->
<b><table class="table table-hover" ><TR>
    <td> Базовая станция: </td>
    <td> {{ bsnumber }} </td>
    <td> Количество аварий: </td>
    <td> {{ kolAlm }} </td>
    <td> Продолжительность: </td>
    <td>  {{ sumTime }} </td>

</TR></table></b>


<form action="{% url 'searchalarms' %}" method="post">
    {% csrf_token %}

    <div class="container card ">
        <div class="row ">
            <table>
                <TR>
                    <td width="30%">
                        <label class="form-control bg-light" aria-label="Выбор диапазона даты">
                            <select name="select_id" class="form-select" aria-label="Default select example">
                                {% for alm in alms %}
                                <option value={{ alm.alarm_id }} {% if alm.alarm_id == select_id %}selected{% endif %} >{{ alm.alarm_name }}</option>
                                {% endfor %}
                            </select>
                        </label>
                    </td>
                    <td width="30%">
                        <label class="form-control bg-light" aria-label="Выбор диапазона даты">
                            <input class="form-control bg-light" type="text" name="daterange" value={{ dat }}/>
                        </label>
                    </td>
                    <Td width="20%">
                        <label class="form-control bg-light" aria-label="Выбор БС">
                            <input type="text" class="form-control" placeholder="Номер БС" value={{ bsnumber }}
                                   aria-label="Номер БС" aria-describedby="button-addon2" name="bsnumber">
                        </label>
                    </td>
                    <TD width="20%" align="center">
                        <label class="form-control bg-light" aria-label="Нажать на кнопку">
                            <div class="d-grid gap-2">
                                <button class="btn btn-primary" type="submit" id="knopka">Показать</button>
                            </div>
                        </label>
                    </TD>
                </TR>
            </table>
        </div>
    </div>
</form>
<table class="table table-hover">
    <tr><b>
        <!--    <tr bgcolor="#ffcc00">-->
        <td width="3"> №</td>
        <td width="15%">Наименование БС</td>
        <td width="8%">Номер БС</td>
        <td width="8%">Cell Name</td>
        <td width="13%">Авария</td>
        <td width="10%">BSC/RNC</td>
        <td width="13%">Время Аварии</Td>
        <td width="13%">Время устранения</Td>
        <td width="13%">Время простоя</Td>
        <!--        <td><img src="{% static 'img/trash1.png' %}"></Td>-->
    </b></TR>

    {% for item in page_obj %}
    <tr>
        <td>{{ forloop.counter0|add:page_obj.start_index }}</td>
        <td>{{ item.bsname }}</td>
        <td>{{ item.bsnumber }}</td>
        <td>{{ item.cellname }}</td>
        <td>{{ item.alarmname }}</td>
        <td>{{ item.bscrnc }}</td>
        <td>{{ item.appeartime|date:"Y-m-d H:i:s" }}</td>
        <td>{{ item.cleartime|date:"Y-m-d H:i:s" }}</td>
        <td>{{ item.downtime }}</td>
    </tr>
    {% endfor %}
</table>

<script type="text/javascript">

var today = new Date("{{ dateval1 }}");
var endDate = new Date("{{ dateval2 }}");

$(function() {
    $('input[name="daterange"]').daterangepicker({
    startDate: today,
    endDate: endDate,
    showWeekNumbers: true,
    "locale": {
        "format": "DD/MM/YYYY",
        "separator": " - ",
        "applyLabel": "Сохранить",
        "cancelLabel": "Назад",
        "daysOfWeek": [
            "Вс",
            "Пн",
            "Вт",
            "Ср",
            "Чт",
            "Пт",
            "Сб"
        ],
        "monthNames": [
            "Январь",
            "Февраль",
            "Март",
            "Апрель",
            "Май",
            "Июнь",
            "Июль",
            "Август",
            "Сентябрь",
            "Октябрь",
            "Ноябрь",
            "Декабрь"
        ],
        "firstDay": 1
    }}
    );
});

</script>
</body>

<div class="col-12">
{% include "pagination.html" %}
</div>


{% endblock %}

