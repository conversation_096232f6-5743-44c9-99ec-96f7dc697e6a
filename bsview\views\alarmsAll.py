from django.http import HttpResponseRedirect, HttpResponseNotAllowed, JsonResponse
from pyexpat.errors import messages

from bsview.views.imports_file import *


class BsAlarmView(LoginRequiredMixin, ListView):
    model = Current_Alarms
    template_name = 'alarms.html'
    paginate_by = 50

    # print(str(bsview.models.Log_Alarms.objects.all().query))
    def get_queryset(self):
        # print(self.request.user.region)
        regionUzb = RegionUzb.objects.get(name=self.request.user.region)
        bscrnc = (regionUzb.bsc + ',' + regionUzb.rnc).split(",")
        # if self.request.user.privilege_id != 1:
        return Current_Alarms.objects.filter(bscrnc__in=bscrnc)

        # else:
        #    return bsview.models.Current_Alarms.objects.all()

class BsAlarmViewAll(LoginRequiredMixin, ListView):
    model = Log_Alarms_full
    template_name = 'alarmsall.html'
    paginate_by = 50

    def get_queryset(self):
        return Log_Alarms_full.objects.filter(
            bsnumber__in=getBsfromRegion(self.request.user.region)).order_by("alarmname", "appeartime")


class BsAlarmViewAllSelect(LoginRequiredMixin, ListView):
    model = Log_Alarms_full
    template_name = 'alarmsall.html'
    paginate_by = 50

    def get_queryset(self):
        if self.kwargs['alarms']:
            print(self.kwargs['alarms'])
            return Log_Alarms_full.objects.filter(idalarm=self.kwargs['alarms'],
                                                                bsnumber__in=getBsfromRegion(
                                                                    self.request.user.region)).order_by("appeartime")


class AlarmRegionViewAll(LoginRequiredMixin, ListView):
    model = Log_Alarms_full
    template_name = 'alarmregionviewall.html'
    paginate_by = 50

    def get_queryset(self):
        if self.kwargs['region']:
            return Log_Alarms_full.objects.filter(
                bsnumber__in=getBsfromRegion(self.kwargs['region'])).order_by("alarmname", "appeartime")


class AlarmRegionView(LoginRequiredMixin, ListView):
    model = Current_Alarms
    template_name = 'alarmregionview.html'
    paginate_by = 50

    def get_queryset(self):
        if self.kwargs['region']:
            regionUzb = RegionUzb.objects.get(id=self.kwargs['region'])
            bscrnc = (regionUzb.bsc + ',' + regionUzb.rnc).split(",")
            # print(bscrnc)
            return Current_Alarms.objects.filter(bscrnc__in=bscrnc)


class AlarmDeleteView(LoginRequiredMixin, DeleteView):
    model = Current_Alarms
    template_name = 'alm_delete.html'
    success_url = reverse_lazy('getalarms')
    # success_url = reverse_lazy('alarms')


class AlarmDeleteViewAll(LoginRequiredMixin, DeleteView):
    model = Log_Alarms_full
    template_name = 'almall_delete.html'
    success_url = reverse_lazy('alarmsall')

@login_required
def idlebts(request):

    regions = RegionUzb.objects.annotate(
        bs_count=Count('bsbeeline', distinct=False)
    )

    # Создаем словарь для хранения количества alarm_count и unique_bsnumber для каждого региона
    alarm_count_dict = {region.id: 0 for region in regions}
    unique_bsnumber_dict = {region.id: set() for region in regions}

    # Получаем количество записей от Current_Alarms, которые соответствуют regionUzb.bsc или regionUzb.rnc
    current_alarms = Current_Alarms.objects.all()
    for alarm in current_alarms:
        for region in regions:
            bsc_list = region.bsc.split(',')
            rnc_list = region.rnc.split(',')
            if alarm.bscrnc in bsc_list or alarm.bscrnc in rnc_list:
                alarm_count_dict[region.id] += 1
                unique_bsnumber_dict[region.id].add(alarm.bsnumber)

    total_bs_count = 0
    total_alarm_count = 0
    total_unique_bsnumber = 0

    for region in regions:
        region.alarm_count = alarm_count_dict[region.id]
        region.unique_bsnumber = len(unique_bsnumber_dict[region.id])
        region.bs_percentage = round((region.unique_bsnumber / region.bs_count) * 100, 2) if region.bs_count > 0 else 0
        total_bs_count += region.bs_count
        total_alarm_count += region.alarm_count
        total_unique_bsnumber += region.unique_bsnumber

    return render(request, 'allidlebts.html', {'region': regions,
                                               'total_bs_count' : total_bs_count,
                                               'total_alarm_count' : total_alarm_count,
                                               'total_unique_bsnumber' : total_unique_bsnumber,
                                               'total_bs_percentage': round(
                                                   (total_unique_bsnumber / total_bs_count) * 100,
                                                   2) if total_bs_count > 0 else 0,
                                               })


@login_required
def alarmview(request):
    region = RegionUzb.objects.all()
    return render(request, 'alarmview.html', {'region': region})


@login_required
def getalarm(request):
    file_stream = BytesIO()
    if request.method == "POST":
        region = request.POST.get('regiono')
        alarm_type = request.POST.get('inlineRadioOptions')
        dates = request.POST.get('daterange')

        # print(dates, region, alarm_type)
        dat1 = datetime.datetime(year=int(dates[6:10]), month=int(dates[3:5]), day=int(dates[0:2]), second=1)
        dat2 = datetime.datetime(year=int(dates[19:23]), month=int(dates[16:18]), day=int(dates[13:15]), hour=23,
                                 minute=59, second=59)
        # print(dat1)
        # print(dat2)
        # print(dat2-dat1)
        regionUzb = RegionUzb.objects.get(id=region)
        bscrnc = (regionUzb.bsc + ',' + regionUzb.rnc).split(",")

        if alarm_type == "oml":
            alarms = Log_Alarms.objects.filter(bscrnc__in=bscrnc, alarmname="OML Fault",
                                                             appeartime__gte=dat1, appeartime__lte=dat2)

            book = reportAlarmOml(alarms, len(alarms), getRegion(region_id=region).name, dat1, dat2, alarm_type)

        elif alarm_type == "nodeb":
            alarms = Log_Alarms.objects.filter(bscrnc__in=bscrnc, alarmname="NodeB Unavailable",
                                                             appeartime__gte=dat1, appeartime__lte=dat2)
            book = reportAlarmOml(alarms, len(alarms), getRegion(region_id=region).name, dat1, dat2, alarm_type)

        elif alarm_type == "full":
            alarms = Log_Alarms_arxiv.objects.filter(bscrnc__in=bscrnc,
                                                                   appeartime__gte=dat1, appeartime__lte=dat2)
            book = reportAlarmFull(alarms, len(alarms), getRegion(region_id=region).name, dat1, dat2)
        book.save(file_stream)
        response = HttpResponse(content=file_stream.getvalue(), content_type='application/ms-excel')
        response[
            'Content-Disposition'] = f'attachment; filename= ReportData-{dt.datetime.now().strftime("%d-%m-%Y-%H-%M-%S")}.xlsx'
        return response

@login_required
def alarmstat(request):
    region = RegionUzb.objects.all()
    alarms = Alarms.objects.all()
    return render(request, 'alarmstat.html', {'region': region, 'alarms': alarms})


@login_required
def searchAlarm(request):
    alms = Alarms.objects.all()
    if request.method == "POST":
        region = request.user.region
        alarm_type = request.POST.get('select_id')
        dates = request.POST.get('daterange')
        dat1 = datetime.datetime(year=int(dates[6:10]), month=int(dates[3:5]), day=int(dates[0:2]), second=1)
        dat2 = datetime.datetime(year=int(dates[19:23]), month=int(dates[16:18]), day=int(dates[13:15]), hour=23,
                                 minute=59, second=59)
        dateval1 = dates[3:5] + "/" + dates[0:2] + "/" + dates[6:10]
        dateval2 = dates[16:18] + "/" + dates[13:15] + "/" + dates[19:23]
        bscrnc = (region.bsc + ',' + region.rnc).split(",")
        bsnomer = request.POST.get('bsnumber')
        day = dat2 - dat1
        if int(day.days) + 1 > 31:
            return HttpResponse("<h1>Days are more than 31 </h1> <a href='alarmstat.html'' alt='Назад'>")
        else:
            if alarm_type == "21825":
                if request.user.privilege_id != 1:
                    alarms = Log_Alarms.objects.filter(bscrnc__in=bscrnc, alarmname="OML Fault",
                                                                     bsnumber=bsnomer, appeartime__gte=dat1,
                                                                     appeartime__lte=dat2)
                else:
                    alarms = Log_Alarms.objects.filter(alarmname="OML Fault",
                                                                     bsnumber=bsnomer, appeartime__gte=dat1,
                                                                     appeartime__lte=dat2)
            elif alarm_type == "22214":
                if request.user.privilege_id != 1:
                    alarms = Log_Alarms.objects.filter(bscrnc__in=bscrnc, alarmname="NodeB Unavailable",
                                                                     bsnumber=bsnomer, appeartime__gte=dat1,
                                                                     appeartime__lte=dat2)
                else:
                    alarms = Log_Alarms.objects.filter(alarmname="NodeB Unavailable",
                                                                     bsnumber=bsnomer, appeartime__gte=dat1,
                                                                     appeartime__lte=dat2)

            elif alarm_type == "00000":
                if request.user.privilege_id != 1:
                    alarms = Log_Alarms_arxiv.objects.filter(bscrnc__in=bscrnc,
                                                                           bsnumber=bsnomer, appeartime__gte=dat1,
                                                                           appeartime__lte=dat2)
                else:
                    alarms = Log_Alarms_arxiv.objects.filter(bsnumber=bsnomer, appeartime__gte=dat1,
                                                                           appeartime__lte=dat2)

            else:
                if request.user.privilege_id != 1:
                    alarms = Log_Alarms_arxiv.objects.filter(bscrnc__in=bscrnc, idalarm=alarm_type,
                                                                           bsnumber=bsnomer, appeartime__gte=dat1,
                                                                           appeartime__lte=dat2)
                else:
                    alarms = Log_Alarms_arxiv.objects.filter(idalarm=alarm_type,
                                                                           bsnumber=bsnomer, appeartime__gte=dat1,
                                                                           appeartime__lte=dat2)

            kolAlm = len(alarms)
            sumTime = datetime.timedelta(seconds=0)
            if alarm_type == "21825" or alarm_type == "22214" \
                    or alarm_type == "25622"\
                    or alarm_type == "65033" or alarm_type == "29201" \
                    or alarm_type == "26529" or alarm_type == "26594" \
                    or alarm_type == "26234" and len(alarms) > 0:
                for al in alarms:
                    sumTime = sumTime + al.downtime

            paginator = Paginator(alarms, 100)
            page_number = request.GET.get('page', 1)
            posts = paginator.page(page_number)

            return render(request, "searchalarms.html", {"page_obj": posts, "bsnumber": bsnomer,
                                                         'select_id': alarm_type, "dat": dates, 'alms': alms,
                                                         'kolAlm': kolAlm, 'sumTime': sumTime,
                                                         'dateval1': dateval1, 'dateval2': dateval2})

@login_required
def analyze_alarm(request):
    region = RegionUzb.objects.all()
    return render(request, 'alarmanalyze.html', {'region': region})


@login_required
def alarm_analyze_view(request):
    alms = Alarms.objects.all()
    if request.method == "POST":
        region = request.user.region
        bsnomer = request.POST.get('bsnumber')
        dates = request.POST.get('daterange')
        start_date = datetime.datetime.strptime(f"{dates[6:10]}-{dates[3:5]}-{dates[0:2]}", "%Y-%m-%d")
        end_date = datetime.datetime.strptime(f"{dates[19:23]}-{dates[16:18]}-{dates[13:15]}", "%Y-%m-%d")
        D = 'D'
        date_list = pd.date_range(start_date, end_date, freq=D)
        print(start_date, end_date)
        dateval1 = dates[3:5] + "/" + dates[0:2] + "/" + dates[6:10]
        dateval2 = dates[16:18] + "/" + dates[13:15] + "/" + dates[19:23]
        # bscrnc = (region.bsc + ',' + region.rnc).split(",")
        # bsnomer = request.POST.get('bsnumber')
        # day = dat2 - dat1
        hours = []
        for i in range(24):
            hours.append(format(str(i).zfill(2)))

    return render(request, "alarmanalyzeview.html",
                  {'date_list': date_list, 'start_date': dateval1, 'end_date': dateval2, 'hours': hours,
                   'bsnomer': bsnomer})


@login_required
def analyze_alarm_day(request):
    region = RegionUzb.objects.all()
    return render(request, 'alarmanalyzeday.html', {'region': region})


@login_required
def alarm_analyze_view_day(request):
    region = RegionUzb.objects.all()
    if request.method == "POST":
        # reg = BsBeeline.objects.get(region='1')
        regionUzb = RegionUzb.objects.get(id=request.POST.get('regiono'))
        dates = request.POST.get('datepicker')
        start_time = datetime.datetime.strptime(f"{dates[6:10]}-{dates[3:5]}-{dates[0:2]}", "%Y-%m-%d")
        end_time = start_time + timedelta(hours=23, minutes=59)
        # print(start_time, " ", end_time, " ", bscrnc)
        # alarms = bsview.models.Log_Alarms_arxiv.objects.filter(bscrnc__in=bscrnc, idalarm="65033")
        req_region = request.POST.get('regiono')
        bscrnc = (region.bsc + ',' + region.rnc).split(",")
        print(req_region)
        alarms = Log_Alarms_arxiv.objects.filter(appeartime__gte=start_time, appeartime__lte=end_time, \
                                                               bsnumber__in=BsBeeline.objects.filter(
                                                                   region=req_region).values('bsnum'), \
                                                               idalarm="65033")
        dat = dates[3:5] + "/" + dates[0:2] + "/" + dates[6:10]
        for alarm in alarms:
            print(alarm.bsname, alarm.alarmname, alarm.appeartime, alarm.cleartime)

    return render(request, "alarmanalyzeviewday.html", {'region': region, "dat": dat, 'alarms': alarms})

datetime

def log_alarms_filtered(request):
    # region = RegionUzb.objects.all()
    if request.method == "POST":
        region = request.user.region
        dates = request.POST.get('datepicker')
        start_time = datetime.datetime.strptime(f"{dates[6:10]}-{dates[3:5]}-{dates[0:2]}", "%Y-%m-%d")
        end_time = start_time + timedelta(hours=23, minutes=59)
        # req_region = request.POST.get('regiono')
        bscrnc = (region.bsc + ',' + region.rnc).split(",")
        # daynow = datetime.strptime(request.GET.get('daynow'), '%Y-%m-%d')
        logs = Log_Alarms_arxiv.objects.filter(appeartime__gte=start_time, appeartime__lte=end_time, bscrnc__in=bscrnc)
        data = {}
        unique_bsname = logs.values_list('bsnumber', flat=True).distinct()
        for bs in unique_bsname:
            data[bs] = []
            for hour in range(24):
                hour_logs = logs.filter(bsnumber=bs, appeartime__hour=hour)
                if hour_logs.exists():
                    log = hour_logs.first()
                    if log.idalarm in ['21825', '22214']:
                        color = 'red'
                    else:
                        color = 'yellow'
                    data[bs].append((log.appeartime, log.cleartime, color, log.alarmname))
                else:
                    data[bs].append((None, None, 'green', ''))


    return render(request, 'log_alarms_filtered.html', {'data': data, 'unique_bsname': unique_bsname, 'hours':list(range(24))})

    # return render(request, 'log_alarms_filtered.html', {'logs': logs, 'unique_bsname': unique_bsname, 'daynow': daynow, 'hours':list(range(24))})


def alarms_view(request):
    # Получаем дату из фильтра запроса
    region = RegionUzb.objects.all()
    if request.method == "POST":
        # region = request.user.region
        dates = request.POST.get('datepicker')
        # start_time = datetime.datetime.strptime(f"{dates[6:10]}-{dates[3:5]}-{dates[0:2]}", "%Y-%m-%d")
        # end_time = start_time + timedelta(hours=23, minutes=59)
        req_region = RegionUzb.objects.get(id=request.POST.get('regiono'))
        bscrnc = (req_region.bsc + ',' + req_region.rnc).split(",")
        daynow = datetime.datetime.strptime(dates, '%d/%m/%Y')
        # logs = Log_Alarms_arxiv.objects.filter(appeartime__gte=start_time, appeartime__lte=end_time, bscrnc__in=bscrnc)
        # data = {}
        # unique_bsname = logs.values_list('bsnumber', flat=True).distinct()



    # Получаем записи за выбранную дату
    alarms = Log_Alarms_arxiv.objects.filter(
        appeartime__date=daynow,
        bscrnc__in = bscrnc
    )

    # Создаем словарь для хранения данных по часам
    data = {}
    for alarm in alarms:
        if alarm.bsname not in data:
            data[alarm.bsname] = {'hours': [None] * 24}
        start_hour = alarm.appeartime.hour
        end_hour = alarm.cleartime.hour
        for hour in range(start_hour, end_hour + 1):
            if alarm.idalarm in ['22214', '21825']:
                color = 'red'
            else:
                color = 'yellow'
            data[alarm.bsname]['hours'][hour] = {'start_time': alarm.appeartime.strftime("%H:%M"), 'end_time': alarm.cleartime.strftime("%H:%M"),
                                                 'color': color}

    for bsname in data:
        for hour in range(24):
            if data[bsname]['hours'][hour] is None:
                data[bsname]['hours'][hour] = {'color': 'green'}
    h24=list(range(24))
    context = {
        'data': data,
        'hours':[f"{hour:02d}:00" for hour in h24],
        'region':region
    }

    return render(request, 'log_alarms_filtered.html',context)

def delete_selected(request):
    """
    Обрабатывает POST-запрос на удаление выбранных элементов.
    """
    if request.method == 'POST':
        # Получаем список pk из запроса.  Важно проверить, что это список.
        selected_ids = request.POST.getlist('selected_alarms')  # 'selected_alarms' - это имя атрибута name у ваших чекбоксов
        if not selected_ids:
            #  Возвращаем JSON-ответ с указанием ошибки
            return JsonResponse({'status': 'error', 'message': 'Не выбраны элементы для удаления.'}, status=400)

        try:
            # Преобразуем pk в целые числа и удаляем элементы.  Безопаснее делать это явно.
            pks_to_delete = [int(pk) for pk in selected_ids]
            # Используйте filter и delete для удаления нескольких объектов за один запрос.
            deleted_count, _ = Log_Alarms_full.objects.filter(pk__in=pks_to_delete).delete()  # pk__in
            #  Возвращаем JSON-ответ об успешном удалении
            # return JsonResponse({'status': 'success', 'message': f'Успешно удалено {deleted_count} элементов.'})
            return HttpResponseRedirect(reverse('alarmsall'))
        except ValueError:
            #  Возвращаем JSON-ответ с указанием ошибки
            return JsonResponse({'status': 'error', 'message': 'Некорректные данные: ожидались целые числа.'}, status=400)
        except Exception as e:
            #  Логируем ошибку для отладки
            print(f"Ошибка при удалении: {e}")
            #  Возвращаем JSON-ответ с указанием ошибки
            return JsonResponse({'status': 'error', 'message': f'Произошла ошибка при удалении: {e}'}, status=500)
    else:
        # Если это не POST-запрос, возвращаем ошибку 405 (Method Not Allowed).
        return HttpResponseNotAllowed(['POST'])

def access_denied(request):
    """
    Отображает страницу отказа в доступе.
    """
    return render(request, 'access_denied.html')  # Создайте шаблон access_denied.html



