{% extends 'base.html' %}
{% load static %}
{% block content %}
<script type="text/javascript" src="{% static 'js/jquery.min.js' %}"></script>
<script type="text/javascript" src="{% static 'js/moment.min.js' %}"></script>
<script type="text/javascript" src="{% static 'js/bootstrap.min.js' %}"></script>
<link rel="stylesheet" type="text/css" href="{% static 'css/bootstrap.css' %}"/>
<link rel="stylesheet" type="text/css" href="{% static 'css/datepicker.css' %}"/>
<link rel="stylesheet" type="text/css" href="{% static 'css/bootstrap.min.css' %}"/>
<script type="text/javascript" src="{% static 'js/bootstrap-datepicker.min.js' %}"></script>
<script type="text/javascript" src="{% static 'js/bootstrap-datepicker.ru.min.js' %}"></script>
<body>
<h1>На стадии разработки!!!</h1>
<!--&lt;!&ndash;<H3>Функция работает !!!</H3>&ndash;&gt;-->
<form action="{% url 'alarmanalyzeviewday' %}" method="post">
    {% csrf_token %}
    <div class="container card ">
        <table>
            <TR>
                <TD>
                    <div class="col col-7 ml-auto">
                        <b>Выберите область:</b>
                            <select class="form-select" multiple aria-label="multiple select example" name="regiono"
                                    id="reg">
                                {% for reg in region %}
                                {% if reg.id == user.region_id %}
                                <option selected value="{{ reg.id }}">{{reg.name}}</option>
                                {% else %}
                                {% if user.privilege_id == 1 or user.privilege_id == 4 %}
                                <option value="{{ reg.id }}">{{reg.name}}</option>
                                {% else %}
                                <option value="{{ reg.id }}" disabled>{{reg.name}}</option>
                                {% endif %}
                                {% endif %}
                                {% endfor %}
                            </select>
                    </div>
                    <br>
                </TD>
                <TD>
                    <div class="col-md-6 bg-light ">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <button class="btn btn-outline-secondary" type="button" id="decrease-day">-
                                </button>
                            </div>
                            <input type="text" class="form-control datepicker bg-light" id="datepicker" name="datepicker"
                                   readonly>
                            <div class="input-group-append">
                                <button class="btn btn-outline-secondary" type="button" id="increase-day">+
                                </button>
                            </div>
                        </div>
                     </div>

                </TD>
                <TD width="20%" align="center">
                    <label class="form-control bg-light" aria-label="Нажать на кнопку">
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary" type="submit" id="knopka">Показать</button>
                        </div>
                    </label>
                </TD>
            </TR>
        </table>
        </TR>
    </div>
    <br>
</form>

<style>
        .red { background-color: red; }
        .yellow { background-color: yellow; }
        .green { background-color: green; }
    </style>
</head>

    <table class="table-bordered">
        <thead>
            <tr>
                <th>#</th>
                <th>Наименование БС</th>
                {% for hour in hours %}
                    <th>{{ hour }}</th>
                {% endfor %}
            </tr>
        </thead>
        <tbody>
            {% for bsname, details in data.items %}
                <tr>
                    <td style="padding: 5px;"> {{ forloop.counter }} </td>
                    <td>{{ bsname }}</td>
                    {% for hour_data in details.hours %}
                        <td class="{{ hour_data.color }}">
                            {% if hour_data.start_time and hour_data.end_time %}
                                {{ hour_data.start_time }}
                            {% endif %}
                        </td>
                    {% endfor %}
                </tr>
            {% endfor %}
        </tbody>
    </table>
</body>





</body>


<style>
     body { background-color: #f8f9fa; font-family: 'Roboto', sans-serif; } .container { margin-top: 50px; } h1 { text-align: center; margin-bottom: 30px; color: #343a40; } .datepicker-dropdown { background-color: #ffffff; border-radius: 10px; border: 1px solid #dee2e6; } .datepicker table { width: 100%; margin: 0; border-collapse: collapse; } .datepicker table tr td, .datepicker table tr th { text-align: center; width: 2.4em; height: 2.4em; border-radius: 5px; transition: background-color 0.2s ease-in-out; } .datepicker table tr td.day:hover, .datepicker table tr td.day.active { background-color: #007bff; color: #ffffff; } .datepicker table tr td.old, .datepicker table tr td.new { color: #adb5bd; }

</style>
<script>
        $(document).ready(function(){
            $('.datepicker').datepicker({
                format: 'dd/mm/yyyy',
                language: 'ru',
                todayBtn: true
            }).datepicker("setDate",'yesterday');
            $('#increase-day').on('click', function(){
                var currentDate = $('#datepicker').datepicker('getDate');
                currentDate.setDate(currentDate.getDate() + 1);
                $('#datepicker').datepicker('setDate', currentDate);
            });

            $('#decrease-day').on('click', function(){
                var currentDate = $('#datepicker').datepicker('getDate');
                currentDate.setDate(currentDate.getDate() - 1);
                $('#datepicker').datepicker('setDate', currentDate);
            });
        });
</script>


</body>
{% endblock content%}