{% extends 'base.html' %}
{% load static %}

{% block content %}
<script type="text/javascript" src="{% static 'js/jquery.min.js' %}"></script>
<script type="text/javascript" src="{% static 'js/moment.min.js' %}"></script>
<script type="text/javascript" src="{% static 'js/daterangepicker.js' %}"></script>
<link rel="stylesheet" type="text/css" href="{% static 'css/bootstrap.css' %}"/>
<link rel="stylesheet" type="text/css" href="{% static 'css/daterangepicker.css' %}"/>

<div class="jumbotron">
    <p>
</div>
<body>
<form action="{% url 'getalarm' %}" method="post" id="getalarmform" enctype="multipart/form-data">
    {% csrf_token %}

    <div class="container card ">
        <div class="row ">
            <div class="col col-3">
                <p>
                    <b>Выберите область:</b>
                <p>
                <div class="card-header w-100 p-3">
                    <select class="form-select" multiple aria-label="multiple select example" name="regiono" id="reg">
                        {% for reg in region %}
                        {% if reg.id == user.region_id %}
                        <option selected value="{{ reg.id }}">{{reg.name}}</option>
                        {% else %}
                        {% if user.privilege_id == 1 %}
                        <option value="{{ reg.id }}">{{reg.name}}</option>
                        {% else %}
                        <option value="{{ reg.id }}" disabled>{{reg.name}}</option>
                        {% endif %}

                        {% endif %}
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="col ">

                <p><b>Выбрать отчет:</b></p>
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="inlineRadioOptions" id="oml"
                           value="oml" onclick="change()" checked>
                    <label class="form-check-label" for="oml">OML Fault</label>
                </div>
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="inlineRadioOptions" id="nodeb"
                           value="nodeb" onclick="change2()">
                    <label class="form-check-label" for="nodeb">NodeB Unavailable</label>
                </div>
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="inlineRadioOptions" id="full"
                           value="full" onclick="change3()">
                    <label class="form-check-label" for="full">Другие алармы</label>
                </div>
                <br>
                <br>
                <p>
                    <b id="vibor">Выбор даты:</b>
                    <br>

                <p>

                  <div class="col">
                        <label class="form-control bg-light"  aria-label="Выбор диапазона даты">
                            <input class="form-control bg-light" type="text" name="daterange" value="Выбор даты" />
<!--                           <input type="text" class="form-control bg-light" name="datefilter" value="Выбор даты" />-->
                        </label>
            </div>

                <p>
            </div>
        </div>
        <br>
    </div>
    <br>
    <button class="btn btn-primary" type="submit" id="knopka">Создать отчет</button>
    <!--    <input type="submit" value="Upload" name="pmUpload" id="pmUpload" class="button">-->
    <a href="{% url 'bsview' %}" class="btn btn-primary">Отмена</a>
</form>
<br>



<script type="text/javascript">
$(function() {
    $('input[name="daterange"]').daterangepicker({
        showWeekNumbers: true,
        "locale": {
        "format": "DD/MM/YYYY",
        "separator": " - ",
        "applyLabel": "Сохранить",
        "cancelLabel": "Назад",
        "daysOfWeek": [
            "Вс",
            "Пн",
            "Вт",
            "Ср",
            "Чт",
            "Пт",
            "Сб"
        ],
        "monthNames": [
            "Январь",
            "Февраль",
            "Март",
            "Апрель",
            "Май",
            "Июнь",
            "Июль",
            "Август",
            "Сентябрь",
            "Октябрь",
            "Ноябрь",
            "Декабрь"
        ],
        "firstDay": 1
    }}
    );
});
</script>

</body>


{% endblock %}

