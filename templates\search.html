{% extends 'base.html' %}
{% load static %}

{% block content %}

<div class="jumbotron">
    <p>
</div>
<body>
<table class="table table-striped">
    <tr>
        <!--    <tr bgcolor="#ffcc00">-->
        <td width="4"> №</td>
        <td width="20%">
            <form action="{% url 'search_page1' %}" method="post">
                {% csrf_token %}
                <div class="input-group">
                    <input type="text" class="form-control" placeholder="Наименование БС"
                           aria-label="Наименование БС" aria-describedby="button-addon1" name="bsname">
                    <button class="btn btn-outline-secondary" type="submit" id="button-addon1">Поиск</button>
                </div>
            </form>
        </td>
        <!--        <td>Наименование БС</td>-->
        <td width="13%">
            <form action="{% url 'search_page2' %}" method="post">
                {% csrf_token %}
                <div class="input-group">
                    <input type="text" class="form-control" placeholder="Номер БС"
                           aria-label="Номер БС" aria-describedby="button-addon2" name="bsnumber">
                    <button class="btn btn-outline-secondary" type="submit" id="button-addon2">Поиск</button>
                </div>
            </form>
        </td>
        <!--        <td>Номер БС</td>-->
        <td width="10%">Широта</td>
        <td width="10%">Долгота</td>
        <td width="20%">Область</td>
        <td>Район</TD>
        <td><img src="{% static 'img/trash1.png' %}"></Td>
        <td><img src="{% static 'img/file.png' %}"></Td>
    </TR>

    {% for item in baza %}

    <tr>
        <!--        <td>{{ forloop.counter }}</td>-->
         <td>{{ forloop.counter }}</td>
        <td><a href="{% url 'viewdetail' item.pk %}">{{ item.bsname }}</a></td>
        <td>{{ item.bsnum }}</td>
        <td>{{ item.lat }}</td>
        <td>{{ item.lon }}</td>
        <td>{{ item.region }}</td>
        <td>{{ item.area }}</td>
        {% if user.privilege_id != 3 %}
        <td><a href="{% url 'bsdelete' item.pk %}"><img src="{% static 'img/trash1.png' %}" alt="Удалить"></a></td>
        <td><a href="{% url 'bsedit' item.pk %}"><img src="{% static 'img/file.png' %}" alt="Редактировать"></a></td>
        {% else %}
        <td><a href="{% url 'accessdeny' %}"><img src="{% static 'img/trash1.png' %}" alt="Удалить"></a></td>
        <td><a href="{% url 'accessdeny' %}"><img src="{% static 'img/file.png' %}" alt="Редактировать"></a></td>
        {% endif %}

    </tr>
    {% endfor %}
</table>
<div class="pagination">
    <span class="step-links">
        {% if page_obj.has_previous %}
            <a href="?page=1">&laquo; first</a>
            <a href="?page={{ page_obj.previous_page_number }}">previous</a>
        {% endif %}

        <span class="current">
            Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}.
        </span>

        {% if page_obj.has_next %}
            <a href="?page={{ page_obj.next_page_number }}">next</a>
            <a href="?page={{ page_obj.paginator.num_pages }}">last &raquo;</a>
        {% endif %}
    </span>
</div>
</body>
{% endblock content %}

