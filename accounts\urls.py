from django.urls import path
from .views import *
from django.contrib.auth import views

urlpatterns = [
    # path('signup/', SignUpView.as_view(),name='signup'),
    path('signup/', CustomLoginView.as_view(),name='signup'),
    path('users/', UsersView.as_view(),name='users'),
    path('<int:pk>/userchange/', UsersUpdate.as_view(),name='userschange'),
    path('userchange/done/', UsersUpdateDone,name='userupdatedone'),
    path('<int:pk>/userdelete/', UsersDelete.as_view(),name='usersdelete'),
    path('password/', PasswordChange.as_view(),name='changepassword'),
    path('password/changepassdone/', ChangePasswordDone,name='changepassdone'),
    path('<int:pk>/delete/', UsersDelete.as_view(), name='userdelete'),
]
