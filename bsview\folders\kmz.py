import datetime as dt


def startKml(region):
    kml = [f'<?xml version="1.0" encoding="UTF-8"?>' '\n',
           f'<kml xmlns="http://earth.google.com/kml/2.0">\n',
           f'<Folder><name>{region} - BS</name>\n',
           ]
    return kml
def createKml(region,bss,color,iconname, position, sectorname, sect):
    kml = [f'<Document><name>{position}_{dt.date.today()}</name>\n',
          f'<Folder><name>{position}</name>\n',
          f'<Style id="BS"><IconStyle id="BSIconstyle"><Icon><href>bs.png</href></Icon><heading>0</heading><color>FFCCCCCC</color><scale>2</scale></IconStyle></Style>\n'
          ]
    for bs in bss:
        if sect == 'gsm900' and bs.gsm900: kml.append(f'<Placemark><name>{bs.bsname}</name><Point><coordinates>{bs.lon.replace(",",".")},{bs.lat.replace(",",".")},0.00000000</coordinates></Point><styleUrl>#BS</styleUrl></Placemark>\n')
        if sect == 'gsm1800' and bs.gsm1800: kml.append(f'<Placemark><name>{bs.bsname}</name><Point><coordinates>{bs.lon.replace(",",".")},{bs.lat.replace(",",".")},0.00000000</coordinates></Point><styleUrl>#BS</styleUrl></Placemark>\n')
        if sect == 'umts900' and bs.umts900: kml.append(f'<Placemark><name>{bs.bsname}</name><Point><coordinates>{bs.lon.replace(",",".")},{bs.lat.replace(",",".")},0.00000000</coordinates></Point><styleUrl>#BS</styleUrl></Placemark>\n')
        if sect == 'umts2100' and bs.umts2100: kml.append(f'<Placemark><name>{bs.bsname}</name><Point><coordinates>{bs.lon.replace(",",".")},{bs.lat.replace(",",".")},0.00000000</coordinates></Point><styleUrl>#BS</styleUrl></Placemark>\n')
        if sect == 'besector' and bs.umtsbesec: kml.append(f'<Placemark><name>{bs.bsname}</name><Point><coordinates>{bs.lon.replace(",",".")},{bs.lat.replace(",",".")},0.00000000</coordinates></Point><styleUrl>#BS</styleUrl></Placemark>\n')
        if sect == 'lte800' and bs.lte800: kml.append(f'<Placemark><name>{bs.bsname}</name><Point><coordinates>{bs.lon.replace(",",".")},{bs.lat.replace(",",".")},0.00000000</coordinates></Point><styleUrl>#BS</styleUrl></Placemark>\n')
        if sect == 'lte1800' and bs.lte1800: kml.append(f'<Placemark><name>{bs.bsname}</name><Point><coordinates>{bs.lon.replace(",",".")},{bs.lat.replace(",",".")},0.00000000</coordinates></Point><styleUrl>#BS</styleUrl></Placemark>\n')
        if sect == 'lts2600' and bs.lte2600: kml.append(f'<Placemark><name>{bs.bsname}</name><Point><coordinates>{bs.lon.replace(",",".")},{bs.lat.replace(",",".")},0.00000000</coordinates></Point><styleUrl>#BS</styleUrl></Placemark>\n')
        if sect == 'lte2300' and bs.lte2300: kml.append(f'<Placemark><name>{bs.bsname}</name><Point><coordinates>{bs.lon.replace(",",".")},{bs.lat.replace(",",".")},0.00000000</coordinates></Point><styleUrl>#BS</styleUrl></Placemark>\n')
        if sect == 'lte2100' and bs.lte2100: kml.append(f'<Placemark><name>{bs.bsname}</name><Point><coordinates>{bs.lon.replace(",",".")},{bs.lat.replace(",",".")},0.00000000</coordinates></Point><styleUrl>#BS</styleUrl></Placemark>\n')

    kml.append('</Folder>\n')
    kml.append(f'<Folder><name>{sectorname}</name><Style id="A0"><IconStyle id="A0Iconstyle"><Icon><href>{iconname}</href></Icon><heading>0</heading><color>{color}</color><scale>2</scale></IconStyle><LabelStyle id="A0Labelstyle"><color>0000000</color></LabelStyle></Style>\n')
    for bs in bss:
        if sect=='gsm900': sector = bs.gsm900
        if sect=='gsm1800': sector = bs.gsm1800
        if sect=='umts900': sector = bs.umts900
        if sect=='umts2100': sector = bs.umts2100
        if sect=='besector': sector = bs.umtsbesec
        if sect=='lte800': sector = bs.lte800
        if sect=='lte1800': sector = bs.lte1800
        if sect=='lte2600': sector = bs.lte2600
        if sect=='lte2300': sector = bs.lte2300
        if sect=='lte2100': sector = bs.lte2100

        if sector:
            #print(len(sector))
            for i in range(len(sector.split('-'))):
                kml.append((f'<Style id="A{sector.split("-")[i]}"><IconStyle id="A{sector.split("-")[i]}Iconstyle"><Icon><href>{iconname}</href></Icon><heading>{sector.split("-")[i]}</heading><color>{color}</color><scale>2</scale></IconStyle><LabelStyle id="A{sector.split("-")[i]}Labelstyle"><color>0000000</color></LabelStyle></Style>\n'))
                kml.append(f'<Placemark><name>{bs.bsnum}{i+1}</name><Point><coordinates>{bs.lon.replace(",",".")},{bs.lat.replace(",",".")},0.00000000</coordinates></Point><styleUrl>#A{sector.split("-")[i]}</styleUrl></Placemark>\n')
    kml.append('</Folder></Document>\n')
    return kml

def endKml():
    kml='</Folder></kml>\n'

    return kml
