import datetime
from openpyxl.styles import Border, Side, Font, PatternFill
from openpyxl.utils import get_column_letter
from io import BytesIO
from openpyxl import Workbook

def reportNri(all_bs_id, all_bs_name, all_bs_number, all_lat, all_lon, app_id, antenna_id, height, sectors, diapasons, horizontal,vertical, anten_id, anten_name, band):
    book = Workbook()
    sheet = book.active
    # sheet.append(['№', 'APP_id', 'BS_id','BSName', 'BSNumber', 'Lat', 'Lon',  'Antenna_ID', 'Anten_ID','Antenna Model',
    #           'Height', 'Sectors','Diapazon','Horizontal Diagram', 'Vertical Diagram', 'Select_Diap','VerticalBeam'])
    sheet.append(['№', 'BSName', 'BSNumber', 'Lat', 'Lon', 'Antenna Model', 'Height', 'Sectors', 'Select_Diap', 'VerticalBeam'])

    qator=0
    for i in range(0, len(all_bs_id)):
        for r in range(0, len(app_id)):
            if all_bs_id[i] == app_id[r]:
                qator +=1
                # diapason_list=[]
                # beam_list=[]
                vertical_beam = ""
                AntennaName =""
                Antenn_Id =""
                AntenDiapazon=""
                Antenhorizontal=""
                AntenVertical=""
                for w in range(0,len(anten_id)):
                    if antenna_id[r] == anten_id[w]:
                        AntennaName = anten_name[w]
                        Antenn_Id = anten_id[w]
                        AntenDiapazon = diapasons[w].replace(" ", "")
                        Antenhorizontal = horizontal[w].replace(" ", "")
                        AntenVertical = vertical[w].replace(" ", "")


                if band == "1800":
                    if "L1" in sectors[r] or "R1" in sectors[r]:
                        # diapason_list= diapasons[r].split(" | ")
                        # beam_list = vertical[r].split(" | ")
                        diapason_list= AntenDiapazon.split("|")
                        beam_list = AntenVertical.split("|")

                        if band in diapason_list:
                            index = diapason_list.index(band)  # Находим индекс c1 в списке a1
                            vertical_beam = beam_list[index]  # Получаем значение из b1_list по найденному индексу
                            # 'print(f"Если c1 = {c1}, то x1 = {x1}")
                        # sheet.append([qator, app_id[r], all_bs_id[i], all_bs_name[i], all_bs_number[i], all_lat[i], all_lon[i], antenna_id[r], Antenn_Id , AntennaName, height[r], sectors[r], diapasons[r], horizontal[r], vertical[r], band, vertical_beam])

                        # sheet.append([qator, app_id[r], all_bs_id[i], all_bs_name[i], all_bs_number[i], all_lat[i],
                        #               all_lon[i], antenna_id[r], Antenn_Id , AntennaName, height[r], sectors[r],
                        #               AntenDiapazon, Antenhorizontal, AntenVertical, band, vertical_beam])

                        sheet.append([qator, all_bs_name[i], all_bs_number[i], all_lat[i],
                                      all_lon[i], AntennaName, height[r], sectors[r], band, okrug06(vertical_beam)])


                elif band == "2600":
                    if "A1" in sectors[r] or "F1" in sectors[r] or "T1" in sectors[r] or "K1" in sectors[r]:
                        # diapason_list= diapasons[r].split(" | ")
                        # beam_list = vertical[r].split(" | ")
                        diapason_list= AntenDiapazon.split("|")
                        beam_list = AntenVertical.split("|")

                        if band in diapason_list:
                            index = diapason_list.index(band)  # Находим индекс c1 в списке a1
                            vertical_beam = beam_list[index]  # Получаем значение из b1_list по найденному индексу

                            # 'print(f"Если c1 = {c1}, то x1 = {x1}")
                        # sheet.append([qator, app_id[r], all_bs_id[i], all_bs_name[i], all_bs_number[i], all_lat[i], all_lon[i], antenna_id[r], Antenn_Id , AntennaName, height[r], sectors[r], diapasons[r], horizontal[r], vertical[r], band, vertical_beam])
                        # sheet.append([qator, app_id[r], all_bs_id[i], all_bs_name[i], all_bs_number[i], all_lat[i],
                        #               all_lon[i], antenna_id[r], Antenn_Id , AntennaName, height[r], sectors[r],
                        #               AntenDiapazon, Antenhorizontal, AntenVertical, band, vertical_beam])
                        sheet.append([qator, all_bs_name[i], all_bs_number[i], all_lat[i],
                                      all_lon[i], AntennaName, height[r], sectors[r], band, okrug06(vertical_beam)])


    return book

def okrug06(number_str):
    """Округляет число, если дробная часть >= 0.6, с обработкой ошибок."""
    if not number_str:  # Проверка на пустую строку
        return "Строка пуста."
    try:
        number_float = float(number_str)
        if number_float - int(number_float) >= 0.6:
            return int(number_float) + 1
        else:
            return int(number_float)
    except ValueError:
        return ""