{% extends 'base.html' %}
{% load static %}

{% block content %}

<div class="jumbotron" xmlns="http://www.w3.org/1999/html">
    <p><p>
    <h5>Архив конфигурационных файлов</h5>
    <p><p>
</div>
<body>

<table class="table table-striped">
    <tr>
        <td width="16%"> Дата ( {{ files.count }} )</td>
        <td width="28%"> GSM </td>
        <td width="28%"> UMTS </td>
        <td width="28%"> LTE </td>
    </tr>
    {% for item in files %}
    <tr>
        <td>{{ item.dat }}</td>
        <td><a href="/media/2G/{{ item.file2g }}"> {{ item.file2g }} </a></td>
        <td><a href="/media/3G/{{ item.file3g }}"> {{ item.file3g }} </a></td>
        <td><a href="/media/4G/{{ item.file4g }}"> {{ item.file4g }} </a></td>
<!--        <td>{{ item.file3g }}</td>-->
<!--        <td>{{ item.file4g }}</td>-->
    </tr>
    {% endfor %}
</table>

</body>
{% endblock content %}

