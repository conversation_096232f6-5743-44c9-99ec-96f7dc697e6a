{% extends 'base.html' %}
{% load static %}

{% block content %}

<div class="jumbotron">
    <p></p>
    <h5>Архив конфигурационных файлов</h5>
    <p></p>
</div>
<body>

<div style="max-height: 550px; overflow-y: auto;"> 
    <table class="table table-hover table-striped">
        <thead style="position: sticky; top: 0; background-color: white; z-index: 1;">
            <tr>
                <th width="16%">Дата ( {{ files.count }} )</th>
                <th width="28%">GSM</th>
                <th width="28%">UMTS</th>
                <th width="28%">LTE</th>
            </tr>
        </thead>
        <tbody>
            {% for item in files %}
            <tr>
                <td>{{ item.dat }}</td>
                <td><a href="/media/2G/{{ item.file2g }}" class="text-decoration-none text-dark hover-effect"> {{ item.file2g }} </a></td>
                <td><a href="/media/3G/{{ item.file3g }}" class="text-decoration-none text-dark hover-effect"> {{ item.file3g }} </a></td>
                <td><a href="/media/4G/{{ item.file4g }}" class="text-decoration-none text-dark hover-effect"> {{ item.file4g }} </a></td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<style>
    .hover-effect {
        transition: all 0.3s ease;
    }
    .hover-effect:hover {
        background-color: #007bff;
        color: #fff;
        transform: scale(1.05);
    }
</style>

</body>
{% endblock content %}