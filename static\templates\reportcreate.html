{% extends 'base.html' %}
{% load static %}


{% block content %}

<div class="jumbotron">
    <p>
</div>
<body>
<form action="{% url 'getreport' %}" method="post" id="getreportform" enctype="multipart/form-data">
    {% csrf_token %}

    <div class="container card ">
        <div class="row ">
            <div class="col col-3">
                <br>
                {% if user.privilege_id == 1 %}
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="inlineRadioOptions" id="inlineRadio1"
                           value="alluzb" onclick="disablereg()">
                    <label class="form-check-label" for="inlineRadio1">По Узбекистану</label>
                </div>
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="inlineRadioOptions" id="inlineRadio2"
                           value="region" onclick="enablereg()" checked>
                    <label class="form-check-label" for="inlineRadio2" >По областям</label>
                </div>
                <p><br>
                    {% endif %}
                    <b>Выберите область:</b>
                <p>
                <div class="card-header w-100 p-3">
                    <select class="form-select" multiple aria-label="multiple select example" name="regiono" id="reg">
                        {% for reg in region %}
                        {% if reg.id == user.region_id %}
                        <option selected value="{{ reg.id }}">{{reg.name}}</option>
                        {% else %}
                        {% if user.privilege_id == 1 or user.privilege_id == 4 %}
                        <option value="{{ reg.id }}">{{reg.name}}</option>
                        {% else %}
                        <option value="{{ reg.id }}" disabled>{{reg.name}}</option>
                        {% endif %}

                        {% endif %}
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="col ">

                <br>
                <br>
                <br>
                <p><b>Выберите отчет:</b></p>


                <div class="form-check">
                    <input class="form-check-input" type="radio" name="reportRadioOptions" id="reportRadio1"
                           value="reportRadio1" onclick="change1()" checked>
                    <label class="form-check-label" for="reportRadio1">БС файл с Азимутами</label>
                </div>
                 <div class="form-check">
                    <input class="form-check-input" type="radio" name="reportRadioOptions" id="reportRadio2"
                           value="reportRadio2" onclick="change2()">
                    <label class="form-check-label" for="reportRadio2">Отчет по кабинетам (по районам) </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="radio" name="reportRadioOptions" id="reportRadio3"
                           value="reportRadio3" onclick="change3()">
                    <label class="form-check-label" for="reportRadio2">Еще какойто отчет </label>
                </div>
                <p>
            </div>
        </div>
        <br>
    </div>
    <br>
    <button class="btn btn-primary" type="submit" id="knopka">Сделать отчет</button>
    <!--    <input type="submit" value="Upload" name="pmUpload" id="pmUpload" class="button">-->
    <a href="{% url 'bsview' %}" class="btn btn-primary">Отмена</a>
</form>
<!--<a href="{% url 'getkmz' %}" class="btn btn-primary">Создать KMZ</a>-->
<!--<input class="btn btn-primary" type="button" value="Отмена" onclick="javascript:history.go(-1);">-->
<br>

<script>
    const opt = document.getElementById("reg");
    const disablereg = () => {
     opt.disabled = true;
    };
    const enablereg = () => {
      opt.disabled = false;
    };








</script>


</body>
{% endblock %}

