from django.db import models
from django.contrib.auth.models import AbstractUser
from bsview.models import RegionUzb


# Create your models here.

class Privilege(models.Model):
    privilege = models.CharField(max_length=15)

    def __str__(self):
        return self.privilege

class CustomUser(AbstractUser):
    telegram_id = models.CharField(null=True, blank=True, max_length=15)
    #administrator = models.BooleanField(default=False)
    # region = models.CharField(null=True, blank=True, max_length=15)
    region = models.ForeignKey(RegionUzb, on_delete=models.SET_NULL, null=True)
    privilege=models.ForeignKey(Privilege,on_delete=models.SET_NULL, null=True)
    def __str__(self):
        return self.username

