import datetime
from django.db import models
from django.urls import reverse

class RegionUzb(models.Model):
    name = models.Char<PERSON>ield(max_length=30, db_index=True)
    bsc = models.CharField(max_length=50)
    rnc = models.CharField(max_length=50)
    def __str__(self):
        return self.name

    def get_absolute_url(self):
        return reverse('regionuzb', kwargs={'cat_id': self.pk})


class AreaUzb(models.Model):
    region = models.ForeignKey(RegionUzb, on_delete=models.CASCADE)
    name = models.Char<PERSON>ield(max_length=30, db_index=True)

    def __str__(self):
        return self.name

# Create your models here.
class Bs<PERSON>eeline(models.Model):
    bsname = models.Char<PERSON>ield(max_length=30, verbose_name="Наименование БС", null=False,blank=False)
    bsnum = models.CharField(max_length=6, verbose_name="Номер БС", null=False,blank=False)
    besecnum = models.Char<PERSON>ield(max_length=6, verbose_name="Номер Бисектор", null=True, blank=True)
    lat = models.CharField(max_length=9, verbose_name="Долгота", null=False,blank=False)
    lon = models.CharField(max_length=9, verbose_name="Широта", null=False,blank=False)
    modem = models.CharField(max_length=30, verbose_name="Номер модема(АСКУЭ)", null=True, blank=True)
    address = models.CharField(max_length=100, verbose_name="Адрес", null=False,blank=False)
    #area = models.CharField(max_length=30, verbose_name="Район")
    # region = models.CharField(max_length=30, verbose_name="Область")
    area = models.ForeignKey(AreaUzb, on_delete=models.SET_NULL, null=True)
    region = models.ForeignKey(RegionUzb, on_delete=models.SET_NULL, null=True)
    gsm900 = models.CharField(max_length=15, verbose_name="GSM-900", null=True, blank=True)
    gsm1800 = models.CharField(max_length=15, verbose_name="GSM-1800", null=True, blank=True)
    gsmbi1800 = models.CharField(max_length=15, verbose_name="GSMBi-1800", null=True, blank=True)
    umts900 = models.CharField(max_length=15, verbose_name="UMTS-900", null=True, blank=True)
    umts2100 = models.CharField(max_length=15, verbose_name="UMTS-2100", null=True, blank=True)
    umtsbesec = models.CharField(max_length=15, verbose_name="UMTS-Бисектор", null=True, blank=True)
    lte800 = models.CharField(max_length=15, verbose_name="LTE-800", null=True, blank=True)
    lte1800 = models.CharField(max_length=15, verbose_name="LTE-1800", null=True, blank=True)
    ltebi1800 = models.CharField(max_length=15, verbose_name="LTEBi-1800", null=True, blank=True)
    lte2600 = models.CharField(max_length=30, verbose_name="LTE-2600", null=True, blank=True)
    lte2300 = models.CharField(max_length=15, verbose_name="LTE-2300", null=True, blank=True)
    lte2100 = models.CharField(max_length=15, verbose_name="LTE-2100", null=True, blank=True)
    changedate = models.DateTimeField(auto_now_add=True, null=True)
    author = models.ForeignKey('accounts.Customuser',on_delete=models.SET_NULL, null=True)

    class Meta:
        ordering = ['bsnum']

    def __str__(self):
        return self.bsname

    def get_absolute_url(self):
        return reverse('viewdetail', args=[str(self.id)])
        # return reverse('bs_detail', args=[str(self.id)])


class Current_Alarms(models.Model):
    alarmid = models.CharField(max_length=20, verbose_name="#", null=False,blank=False)
    bscrnc = models.CharField(max_length=30, verbose_name="BSC / RNC", null=False,blank=False)
    bsname = models.CharField(max_length=30, verbose_name="Наименование БС", null=False,blank=False)
    appeartime = models.DateTimeField(verbose_name="Время появления")

    def __str__(self):
        return self.bsname

    @property
    def calctime(self):
        aptime = datetime.datetime(self.appeartime.year,self.appeartime.month, self.appeartime.day,
                                   self.appeartime.hour,  self.appeartime.minute,  self.appeartime.second)
        return str(datetime.datetime.now()-aptime).split('.')[0]

    @property
    def bsnumber(self):
        return self.bsname[self.bsname.rfind("_") + 1:]

    @property
    def alarmname(self):
        if self.bscrnc[0:3] == "BSC":
            return "OML Fault"
        elif self.bscrnc[0:3] == "RNC":
            return "NodeB Unavailable"

class Log_Alarms(models.Model):
    alarmid = models.CharField(max_length=20, verbose_name="#", null=False,blank=False)
    bscrnc = models.CharField(max_length=30, verbose_name="BSC / RNC", null=False,blank=False)
    bsname = models.CharField(max_length=30, verbose_name="Наименование БС", null=False,blank=False)
    bsnumber = models.CharField(max_length=10, verbose_name="Номер БС", null=False,blank=False)
    alarmname = models.CharField(max_length=30, verbose_name="Авария", null=False,blank=False)
    appeartime = models.DateTimeField(verbose_name="Время появления")
    cleartime = models.DateTimeField(verbose_name="Время устранения")
    #downtime =models.DateTimeField(verbose_name="Время простоя")
    def __str__(self):
        return self.bsname
    @property
    def downtime(self):
        if self.cleartime != None:
        #if len(str(self.cleartime))==0:
            dtime = self.cleartime - self.appeartime
        else:
            dtime = datetime.timedelta(seconds=10)
        return dtime

class Log_Alarms_arxiv(models.Model):
    alarmid = models.CharField(max_length=20, verbose_name="#", null=False,blank=False)
    idalarm = models.CharField(max_length=20, verbose_name="#", null=False,blank=False)
    bscrnc = models.CharField(max_length=30, verbose_name="BSC / RNC", null=False,blank=False)
    bsname = models.CharField(max_length=30, verbose_name="Наименование БС", null=False,blank=False)
    bsnumber = models.CharField(max_length=10, verbose_name="Номер БС", null=False,blank=False)
    cellname = models.CharField(max_length=10, verbose_name="Номер БС", null=False,blank=False)
    alarmname = models.CharField(max_length=30, verbose_name="Авария", null=False,blank=False)
    appeartime = models.DateTimeField(verbose_name="Время появления")
    cleartime = models.DateTimeField(verbose_name="Время устранения")
    #downtime =models.DateTimeField(verbose_name="Время простоя")
    def __str__(self):
        return self.bsname
    @property
    def downtime(self):
        if self.cleartime != None:
        #if len(str(self.cleartime))==0:
            dtime = self.cleartime - self.appeartime
        else:
            dtime = datetime.timedelta(seconds=10)
        return dtime
    @property
    def get_region(self):
        try:
            bss = BsBeeline.objects.get(bsnum=self.bsnumber)
            return bss.region
        except BsBeeline.DoesNotExist:
            return None

class Log_Alarms_full(models.Model):
    alarmid = models.CharField(max_length=20, verbose_name="#", null=False,blank=False)
    idalarm = models.CharField(max_length=20, verbose_name="#", null=False,blank=False)
    bscrnc = models.CharField(max_length=30, verbose_name="BSC / RNC", null=False,blank=False)
    bsname = models.CharField(max_length=30, verbose_name="Наименование БС", null=False,blank=False)
    bsnumber = models.CharField(max_length=10, verbose_name="Номер БС", null=False,blank=False)
    cellname = models.CharField(max_length=10, verbose_name="Номер БС", null=False,blank=False)
    alarmname = models.CharField(max_length=30, verbose_name="Авария", null=False,blank=False)
    appeartime = models.DateTimeField(verbose_name="Время появления")
    cleartime = models.DateTimeField(verbose_name="Время устранения")
    #downtime =models.DateTimeField(verbose_name="Время простоя")
    def __str__(self):
        return self.bsname
    @property
    def calctime(self):
        aptime = datetime.datetime(self.appeartime.year,self.appeartime.month, self.appeartime.day,
                                   self.appeartime.hour,  self.appeartime.minute,  self.appeartime.second)
        return str(datetime.datetime.now()-aptime).split('.')[0]


class Alarms(models.Model):
    alarm_id = models.CharField(max_length=10, verbose_name="ID Alarm", null=False,blank=False)
    alarm_name = models.CharField(max_length=50, verbose_name="Name Alarm", null=False,blank=False)

    def __str__(self):
        return self.alarm_name

class ConfFile(models.Model):
    dat = models.DateField(verbose_name="Date", null=False,blank=False)
    file2g = models.CharField(max_length=50, verbose_name="file2g", null=False,blank=False)
    file3g = models.CharField(max_length=50, verbose_name="file3g", null=False,blank=False)
    file4g = models.CharField(max_length=50, verbose_name="file4g", null=False,blank=False)

    def __str__(self):
        return self.dat

