{% extends 'base.html' %}
{% load crispy_forms_tags %}
{% block title %} Map View BS Beeline {% endblock title %}

{% block content %}
    <title>Карта Узбекистана по областям</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css"
          integrity="sha512-xodZBNTC5n17Xt2atTPuE1HxjVMSvLVW9ocqUKLsCC5CXdbqCmblAshOMAS6/keqq/sMZMZ19scR4PsZChSR7A=="
          crossorigin=""/>
    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"
            integrity="sha512-XQoYMMe3lbHZqvPTTnFFzjGmVFqVAJI0iNW1c/maCGxqGMEKN1DPQlGVXZbwnh5mId8lnZzxePadRq9QLYrogg=="
            crossorigin=""></script>
    <style>
        #mapid { height: 500px; }
    </style>

<body>
    <h1>Карта Узбекистана по областям</h1>
    <div id="mapid"></div>

    <script>
        var map = L.map('mapid').setView([41.3775, 64.5853], 6); // Центр Узбекистана и начальный зум

        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/">OpenStreetMap</a> contributors'
        }).addTo(map);

        var regionsData = JSON.parse('{{ regions_data|escapejs }}');
        var geojsonLayer; // Объявим слой GeoJSON глобально

        function getColor(regionName) {
            // Здесь вы можете определить логику присвоения цвета каждой области
            if (regionName.includes('Ташкент')) {
                return '#ff0000'; // Красный для Ташкента
            } else if (regionName.includes('Самарканд')) {
                return '#00ff00'; // Зеленый для Самарканда
            } else if (regionName.includes('Бухара')) {
                return '#0000ff'; // Синий для Бухары
            } else {
                return '#808080'; // Серый для остальных
            }
        }

        function style(feature) {
            return {
                fillColor: getColor(feature.properties.name_ru), // Предполагается, что в properties есть поле name_ru
                weight: 1,
                opacity: 1,
                color: 'black',
                fillOpacity: 0.7
            };
        }

        function highlightFeature(e) {
            var layer = e.target;

            layer.setStyle({
                weight: 3,
                color: '#666',
                fillOpacity: 0.9
            });

            if (!L.Browser.ie && !L.Browser.opera && !L.Browser.edge) {
                layer.bringToFront();
            }
        }

        function resetHighlight(e) {
            geojsonLayer.resetStyle(e.target);
        }

        function onEachFeature(feature, layer) {
            layer.bindPopup(feature.properties.name_ru);
            layer.on({
                mouseover: highlightFeature,
                mouseout: resetHighlight
            });
        }

        geojsonLayer = L.geoJSON(regionsData, {
            style: style,
            onEachFeature: onEachFeature
        }).addTo(map);
    </script>
</body>

{% endblock content %}