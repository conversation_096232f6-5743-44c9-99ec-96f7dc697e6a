{% extends 'base.html' %}
{% load crispy_forms_tags %}
{% block title %} Map View BS Beeline {% endblock title %}

{% block content %}

<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
    integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin="" />
<link rel="stylesheet" href="/static/css/styles.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />

<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
    integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>

<style>
    /* Body stillarini o'rnatamiz */
    body {
        margin: 0;
        padding: 0;
        overflow: hidden;
    }

    /* Loader stillarini o'rnatamiz */
    .loader-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.85);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        transition: opacity 0.5s, visibility 0.5s;
    }

    .loader {
        width: 120px;
        height: 120px;
        border: 5px solid transparent;
        border-top-color: #F9A825;
        border-radius: 50%;
        animation: spin 1.5s linear infinite;
        position: relative;
    }

    .loader:before, .loader:after {
        content: "";
        position: absolute;
        border: 5px solid transparent;
        border-radius: 50%;
    }

    .loader:before {
        top: 5px;
        left: 5px;
        right: 5px;
        bottom: 5px;
        border-top-color: #E53935;
        animation: spin 2s linear infinite;
    }

    .loader:after {
        top: 15px;
        left: 15px;
        right: 15px;
        bottom: 15px;
        border-top-color: #64B5F6;
        animation: spin 1s linear infinite;
    }

    .loader-text {
        margin-top: 30px;
        font-family: 'Arial', sans-serif;
        font-size: 28px;
        color: white;
        letter-spacing: 3px;
        position: relative;
    }

    .loader-text span {
        display: inline-block;
        opacity: 0;
        animation: fadeIn 1s ease-in-out infinite;
    }

    .loader-text span:nth-child(1) { animation-delay: 0.05s; }
    .loader-text span:nth-child(2) { animation-delay: 0.1s; }
    .loader-text span:nth-child(3) { animation-delay: 0.15s; }
    .loader-text span:nth-child(4) { animation-delay: 0.2s; }
    .loader-text span:nth-child(5) { animation-delay: 0.25s; }
    .loader-text span:nth-child(6) { animation-delay: 0.3s; }
    .loader-text span:nth-child(7) { animation-delay: 0.35s; }
    .loader-text span:nth-child(8) { animation-delay: 0.4s; }
    .loader-text span:nth-child(9) { animation-delay: 0.45s; }
    .loader-text span:nth-child(10) { animation-delay: 0.5s; }
    .loader-text span:nth-child(11) { animation-delay: 0.55s; }
    .loader-text span:nth-child(12) { animation-delay: 0.6s; }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    @keyframes fadeIn {
        0%, 100% { opacity: 0; transform: translateY(5px); }
        50% { opacity: 1; transform: translateY(0); }
    }

    /* Navbar stillarini o'zgartiramiz */
    .navbar {
        margin-bottom: 0 !important;
        z-index: 2000;
        position: relative;
    }

    /* Карта контейнери учун стиллар */
    #mapid {
        height: 100vh;
        width: calc(100% - 300px);
        margin: 0;
        padding: 0;
        position: fixed;
        top: 0;
        left: 300px;
        right: 0;
        bottom: 0;
        z-index: 95;
    }

    /* Zoom tugmalarini to'g'rilash */
    .leaflet-control-container .leaflet-top {
        top: 80px;
    }

    .leaflet-control-container .leaflet-right {
        right: 10px;
    }

    .leaflet-control-zoom {
        margin-right: 10px;
    }

    /* Bo'sh joyni yo'qotish uchun */
    .container-fluid {
        padding: 0;
        margin: 0;
        position: relative;
    }

    /* Стиль для сайдбара */
    .sidebar {
        width: 300px;
        max-width: 300px;
        height: calc(100vh - 70px);
        background-color: white;
        box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
        display: flex;
        flex-direction: column;
        overflow: hidden;
        position: fixed;
        top: 70px;
        left: 0;
        z-index: 1000;
        border-radius: 0;
    }

    .sidebar-header {
        padding: 15px;
        background-color: #2c3e50;
        color: white;
    }

    .sidebar-header h2 {
        margin: 0;
        font-size: 18px;
        font-weight: 500;
    }

    .sidebar-content {
        padding: 0 8px 8px;
        overflow-y: auto;
        flex: 1;
        position: relative;
    }

    .stats-container {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;
        background-color: #f5f5f5;
        border-radius: 5px;
        padding: 10px;
    }

    .stat-item {
        text-align: center;
        flex: 1;
    }

    .stat-value {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 5px;
    }

    .stat-label {
        font-size: 12px;
        color: #666;
    }

    .stat-item.online .stat-value {
        color: #4caf50;
    }

    .stat-item.offline .stat-value {
        color: #f44336;
    }

    .filter-group {
        margin-bottom: 10px;
    }

    .filter-group label {
        display: block;
        margin-bottom: 3px;
        font-weight: 500;
        font-size: 12px;
        color: #555;
    }

    .filter-group select, .filter-group input {
        width: 100%;
        padding: 6px 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 13px;
    }

    .filter-group select:focus {
        outline: none;
        border-color: #2c3e50;
    }

    .filter-group select:disabled {
        background-color: #f5f5f5;
        cursor: not-allowed;
    }

    .map-type-control {
        background-color: white;
        border-radius: 4px;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
        padding: 3px;
        display: flex;
        position: absolute;
        top: 80px;
        /* Под навбаром */
        right: 10px;
        z-index: 1000;
    }

    .map-type-control button {
        background-color: white;
        border: none;
        padding: 8px 8px;
        cursor: pointer;
        font-size: 14px;
        outline: none;
        transition: all 0.2s ease;
        color: #555;
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .map-type-control button:first-child {
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;
    }

    .map-type-control button:last-child {
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
    }

    .map-type-control button.active {
        background-color: #4285F4;
        color: white;
        font-weight: 500;
    }

    .map-type-control button:hover:not(.active) {
        background-color: #f4f4f4;
    }

    /* Вилоятлар статистикаси учун стиллар */
    .regions-stats {
        margin-top: 0;
        border-top: none;
        padding-top: 0;
    }

    .region-item {
        margin-bottom: 5px;
        padding: 8px;
        background-color: #f9f9f9;
        border-radius: 5px;
        cursor: pointer;
        transition: background-color 0.2s;
    }

    .region-item:hover {
        background-color: #f0f0f0;
    }

    .region-name {
        font-weight: bold;
        color: #2c3e50;
    }

    .region-stats {
        display: flex;
        justify-content: space-between;
    }

    .region-stat {
        flex: 1;
        text-align: center;
    }

    .region-stat-value {
        font-weight: bold;
    }

    .region-stat-label {
        font-size: 12px;
        color: #666;
    }

    .total-stat {
        color: #333;
    }

    .online-stat {
        color: #4caf50;
    }

    .offline-stat {
        color: #f44336;
    }

    .region-item.active {
        background-color: #e3f2fd;
        border-left: 3px solid #2196f3;
    }
    
    /* Вилоятлар статистикаси жадвал кўриниши */
    .regions-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        margin-top: 0;
        font-size: 13px;
    }
    
    .regions-table thead {
        position: sticky;
        top: 0;
        z-index: 10;
        background-color: white;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .regions-table th {
        background-color: #f3f3f3;
        padding: 8px 5px;
        text-align: center;
        font-weight: 600;
        border-bottom: 2px solid #ddd;
    }
    
    .regions-table td {
        padding: 6px 5px;
        text-align: center;
        border-bottom: 1px solid #eee;
    }
    
    .regions-table tr:hover {
        background-color: #f5f5f5;
        cursor: pointer;
    }
    
    .regions-table .region-name-cell {
        text-align: left;
        font-weight: 500;
    }
    
    .regions-table tr.active {
        background-color: #e3f2fd;
    }

    /* БС номи стилини яхшилаш */
    .bs-name-tooltip {
        background: transparent;
        border: none;
        box-shadow: none;
        color: #000000;
        font-weight: bold;
        font-size: 10px;
        padding: 0;
        text-shadow: 1px 1px 1px rgba(255, 255, 255, 0.7);
        white-space: nowrap;
        border-radius: 0;
        transition: font-size 0.2s;
    }

    /* Турли карта масштаблари учун БС номлари ўлчамлари */
    .zoom-level-low .bs-name-tooltip {
        font-size: 8px;
    }

    .zoom-level-medium .bs-name-tooltip {
        font-size: 10px;
    }

    .zoom-level-high .bs-name-tooltip {
        font-size: 12px;
    }
    
    /* БС номи стилини яхшилаш учун Leaflet стиллари орқали ўзгартириш */
    .bs-name-tooltip:before {
        display: none;
    }
    
    /* БС номи стилини яхшилаш */
    .leaflet-tooltip.bs-name-tooltip {
        background: transparent;
        border: none;
        box-shadow: none;
    }
</style>
</head>

<body>
    <!-- Loader -->
    <div class="loader-container" id="loader">
        <div class="loader"></div>
        <div class="loader-text">
            <span>Q</span>
            <span>u</span>
            <span>a</span>
            <span>l</span>
            <span>i</span>
            <span>t</span>
            <span>y</span>
            <span>&nbsp;</span>
            <span>t</span>
            <span>e</span>
            <span>a</span>
            <span>m</span>
        </div>
    </div>

    <div class="container-fluid">
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>Статистика</h2>
            </div>
            <div class="sidebar-content">
                <!-- Вилоятлар статистикаси -->
                <div class="regions-stats">
                    <table class="regions-table">
                        <thead>
                            <tr>
                                <th>Регион</th>
                                <th>All</th>
                                <th>On</th>
                                <th>Off</th>
                            </tr>
                        </thead>
                        <tbody id="regions-stats-container">
                            <!-- Вилоятлар статистикаси динамик равишда қўшилади -->
                        </tbody>
                    </table>
                </div>

                <!-- Ажратиш чизиғи -->
                <div style="border-top: 1px solid #ddd; margin: 10px 0;"></div>

                <!-- Қолган фильтрлар -->
                <div class="filter-group">
                    <label for="area">Район:</label>
                    <select id="area" disabled>
                        <option value="">Все районы</option>
                        <!-- Районы будут добавлены динамически -->
                    </select>
                </div>

                <!-- Поиск по имени или номеру БС -->
                <div class="filter-group">
                    <label for="bs-search">Поиск БС:</label>
                    <input type="text" id="bs-search" class="form-control" placeholder="Введите название или номер БС">
                </div>

                <div class="filter-group" style="margin-bottom: 5px;">
                    <button id="reset-filters" class="btn btn-danger btn-sm w-100 mb-2" style="font-size: 12px; padding: 6px 0;">Сбросить фильтры</button>
                </div>

                <!-- Яширин селектлар (логика учун) -->
                <div style="display: none;">
                    <select id="region">
                        <option value="">Все регионы</option>
                        <!-- Регионы будут добавлены динамически -->
                    </select>
                    <select id="status">
                        <option value="all">Все</option>
                        <option value="online">Онлайн</option>
                        <option value="offline">Оффлайн</option>
                    </select>
                    <!-- Яширин статистика элементлари -->
                    <div id="total-bs-count">0</div>
                    <div id="active-bs-count">0</div>
                    <div id="inactive-bs-count">0</div>
                </div>

                <!-- Карта режими тугмалари сайдбардан олиб ташланди -->
            </div>
        </div>
    </div>

    <div id="mapid"></div>

    <!-- Карта режими бошқаруви -->
    <div class="map-type-control">
        <button id="map-button" class="active" title="Карта"><i class="fas fa-map"></i></button>
        <button id="satellite-button" title="Спутник"><i class="fas fa-satellite"></i></button>
    </div>

    {{ points_data_json|json_script:"points-data" }}

    <script>
        // Loader учун скрипт
        document.addEventListener('DOMContentLoaded', function() {
            // Барча ҳарфлар дастлабда кўриниши учун
            setTimeout(function() {
                const loaderTextSpans = document.querySelectorAll('.loader-text span');
                loaderTextSpans.forEach(span => {
                    span.style.opacity = '1';
                    span.style.animation = 'none';
                });
            }, 800);
            
            // Барча манбалар юкланишини кутиш
            window.addEventListener('load', function() {
                setTimeout(function() {
                    const loader = document.getElementById('loader');
                    loader.style.opacity = '0';
                    loader.style.visibility = 'hidden';
                }, 800); // 0.8 секунддан сўнг лоадерни яшириш
            });
        });
        
        // Фильтр қийматларини сақлаш учун функция
        function saveFilterState() {
            const filterState = {
                region: document.getElementById('region').value,
                area: document.getElementById('area').value,
                bsSearch: document.getElementById('bs-search').value,
                status: document.getElementById('status').value
            };
            localStorage.setItem('filterState', JSON.stringify(filterState));
        }

        // Фильтр қийматларини тиклаш учун функция
        function restoreFilterState() {
            const savedState = localStorage.getItem('filterState');
            if (savedState) {
                const filterState = JSON.parse(savedState);
                const regionId = filterState.region;
                const areaId = filterState.area;
                const bsSearch = filterState.bsSearch || '';
                const statusValue = filterState.status;
                
                // Устанавливаем значения в селектах и полях
                document.getElementById('region').value = regionId;
                document.getElementById('bs-search').value = bsSearch;
                document.getElementById('status').value = statusValue;
                
                if (regionId) {
                    // Загружаем районы для выбранного региона
                    fetch(`/api/areas/region/${regionId}/`)
                        .then(response => response.json())
                        .then(areas => {
                            const areaSelect = document.getElementById('area');
                            areaSelect.innerHTML = '<option value="">Все районы</option>';
                            areas.forEach(area => {
                                const option = document.createElement('option');
                                option.value = area.id;
                                option.textContent = area.name;
                                areaSelect.appendChild(option);
                            });
                            areaSelect.disabled = false;
                            areaSelect.value = areaId;
                            
                            // Определяем источник данных в зависимости от фильтров
                            let dataPromise;
                            
                            if (areaId) {
                                // Если выбран район
                                dataPromise = fetch(`/api/base-stations/area/${areaId}/`)
                                    .then(response => response.json());
                            } else {
                                // Если выбран только регион
                                dataPromise = fetch(`/api/base-stations/region/${regionId}/`)
                                    .then(response => response.json());
                            }
                            
                            dataPromise.then(stations => {
                                if (stations.length > 0) {
                                    // Умумий БС маълумотларидан статус қийматларини олиш
                                    const stationStatusMap = {};
                                    points.forEach(point => {
                                        // БС номи бўйича статус қийматини сақлаш
                                        stationStatusMap[point.bsName || point.name] = point.status;
                                    });
                                    
                                    stations = stations.map(station => {
                                        // БС номи бўйича умумий маълумотлардан статусни олиш
                                        const stationName = station.bsName || station.name;
                                        const statusFromGlobal = stationStatusMap[stationName];
                                        
                                        return {
                                            ...station,
                                            // Агар умумий маълумотларда статус топилса, шуни ишлатиш, акс ҳолда API дан қайтган статусни ишлатиш
                                            status: statusFromGlobal !== undefined ? statusFromGlobal : (station.status === 'offline')
                                        };
                                    });
                                    
                                    // Если задан поиск по имени БС, применяем фильтр
                                    if (bsSearch) {
                                        stations = filterByBsName(stations, bsSearch);
                                    }
                                    
                                    // Если есть отфильтрованные станции
                                    if (stations.length > 0) {
                                        // Вычисляем центр
                                        let totalLat = 0;
                                        let totalLon = 0;
                                        stations.forEach(station => {
                                            totalLat += parseFloat(station.lat);
                                            totalLon += parseFloat(station.lon);
                                        });
                                        const centerLat = totalLat / stations.length;
                                        const centerLon = totalLon / stations.length;
                                        
                                        // Центрируем карту
                                        const zoomLevel = areaId ? 11 : 10;
                                        mymap.setView([centerLat, centerLon], zoomLevel);
                                        
                                        // Применяем фильтр по статусу
                                        applyStatusFilter(stations, statusValue);
                                    } else {
                                        // Если нет станций после фильтрации по имени
                                        pointLayers.forEach(marker => mymap.removeLayer(marker));
                                        pointLayers.length = 0;
                                        
                                        // Обновляем статистику
                                        document.getElementById('total-bs-count').textContent = '0';
                                        document.getElementById('active-bs-count').textContent = '0';
                                        document.getElementById('inactive-bs-count').textContent = '0';
                                    }
                                }
                            });
                        });
                } else {
                    // Если регион не выбран, применяем фильтры к общим данным
                    let filteredPoints = [...points];
                    
                    // Если задан поиск по имени БС, применяем фильтр
                    if (bsSearch) {
                        filteredPoints = filterByBsName(filteredPoints, bsSearch);
                    }
                    
                    // Применяем фильтр по статусу
                    applyStatusFilter(filteredPoints, statusValue);
                }
            } else {
                // Если нет сохраненных фильтров, показываем все точки
                applyStatusFilter(points, 'all');
            }
        }

        // Умумий статистика қийматлари
        let globalTotalBsCount = 0;
        let globalActiveBsCount = 0;
        let globalInactiveBsCount = 0;
        
        // Ҳар бир вилоят учун статистика тайёрлаш
        let regionsStatsData = {};

        // Вилоят статистикасини янгилаш функцияси
        function updateRegionsStats() {
            const container = document.getElementById('regions-stats-container');
            container.innerHTML = '';
            
            // Фаол вилоят id-сини олиш
            const activeRegionId = document.getElementById('region').value;
            
            // Республика Ўзбекистон сатрини жадвалнинг бошига қўшиш
            const uzbekistanRow = document.createElement('tr');
            uzbekistanRow.className = activeRegionId === '' ? 'active' : '';
            uzbekistanRow.dataset.regionId = '';
            
            // Ўзбекистон номи
            const uzNameCell = document.createElement('td');
            uzNameCell.className = 'region-name-cell';
            uzNameCell.textContent = 'Все регионы';
            uzNameCell.dataset.statType = 'all';
            uzbekistanRow.appendChild(uzNameCell);
            
            // Умумий БС сони
            const uzTotalCell = document.createElement('td');
            uzTotalCell.className = 'total-stat';
            uzTotalCell.textContent = globalTotalBsCount;
            uzTotalCell.dataset.statType = 'all';
            uzbekistanRow.appendChild(uzTotalCell);
            
            // Онлайн БС сони
            const uzOnlineCell = document.createElement('td');
            uzOnlineCell.className = 'online-stat';
            uzOnlineCell.textContent = globalActiveBsCount;
            uzOnlineCell.dataset.statType = 'online';
            uzbekistanRow.appendChild(uzOnlineCell);
            
            // Оффлайн БС сони
            const uzOfflineCell = document.createElement('td');
            uzOfflineCell.className = 'offline-stat';
            uzOfflineCell.textContent = globalInactiveBsCount;
            uzOfflineCell.dataset.statType = 'offline';
            uzbekistanRow.appendChild(uzOfflineCell);
            
            container.appendChild(uzbekistanRow);
            
            // Ўзбекистон сатрига клик қўшиш
            uzbekistanRow.addEventListener('click', function(e) {
                const statType = e.target.dataset.statType || 'all';
                
                // Умумий фильтр танлаш ва тозалаш
                document.getElementById('region').value = '';
                document.getElementById('area').value = '';
                document.getElementById('area').disabled = true;
                document.getElementById('status').value = statType;
                
                // Картани умумий кўринишга қайтариш
                mymap.setView([41.3, 69.3], 6);
                
                // Умумий БС лар фильтрини қўллаш
                let filteredPoints = [...points];
                applyStatusFilter(filteredPoints, statType);
                
                // Фильтр ҳолатини сақлаш
                saveFilterState();
                
                // Вилоятлар статистикасини янгилаш
                updateRegionsStats();
            });
            
            // Ҳар бир вилоят учун элементлар яратиш
            Object.keys(regionsStatsData).forEach(regionId => {
                const region = regionsStatsData[regionId];
                const isActive = activeRegionId === regionId;
                
                const row = document.createElement('tr');
                row.className = isActive ? 'active' : '';
                row.dataset.regionId = regionId;
                
                // Вилоят номи
                const nameCell = document.createElement('td');
                nameCell.className = 'region-name-cell';
                nameCell.textContent = region.name;
                nameCell.dataset.statType = 'all';
                row.appendChild(nameCell);
                
                // Умумий БС сони
                const totalCell = document.createElement('td');
                totalCell.className = 'total-stat';
                totalCell.textContent = region.total;
                totalCell.dataset.statType = 'all';
                row.appendChild(totalCell);
                
                // Онлайн БС сони
                const onlineCell = document.createElement('td');
                onlineCell.className = 'online-stat';
                onlineCell.textContent = region.online;
                onlineCell.dataset.statType = 'online';
                row.appendChild(onlineCell);
                
                // Оффлайн БС сони
                const offlineCell = document.createElement('td');
                offlineCell.className = 'offline-stat';
                offlineCell.textContent = region.offline;
                offlineCell.dataset.statType = 'offline';
                row.appendChild(offlineCell);
                
                container.appendChild(row);
                
                // Вилоят саторига клик event listener қўшиш
                row.addEventListener('click', function(e) {
                    const regionId = this.dataset.regionId;
                    const statType = e.target.dataset.statType || 'all';
                    
                    // Вилоят танлаш
                document.getElementById('region').value = regionId;
                    
                    // Районлар селектини тозалаш
                    const areaSelect = document.getElementById('area');
                    areaSelect.innerHTML = '<option value="">Все районы</option>';
                    
                    // Статус фильтрини ўрнатиш
                    document.getElementById('status').value = statType;
                    
                    // Районларни юклаш
                    fetch(`/api/areas/region/${regionId}/`)
                        .then(response => response.json())
                        .then(areas => {
                            areas.forEach(area => {
                                const option = document.createElement('option');
                                option.value = area.id;
                                option.textContent = area.name;
                                areaSelect.appendChild(option);
                            });
                            areaSelect.disabled = false;
                        });
                    
                    // БС ларни юклаш ва кўрсатиш
                    fetch(`/api/base-stations/region/${regionId}/`)
                        .then(response => response.json())
                        .then(stations => {
                                if (stations.length > 0) {
                                // Статуслар билан синхронлаш
                                    const stationStatusMap = {};
                                    points.forEach(point => {
                                        stationStatusMap[point.bsName || point.name] = point.status;
                                    });

                                    stations = stations.map(station => {
                                        const stationName = station.bsName || station.name;
                                        const statusFromGlobal = stationStatusMap[stationName];
                                        return {
                                            ...station,
                                            status: statusFromGlobal !== undefined ? statusFromGlobal : (station.status === 'offline')
                                        };
                                    });

                                // Координаталар марказини ҳисоблаш
                                        let totalLat = 0;
                                        let totalLon = 0;
                                        stations.forEach(station => {
                                            totalLat += parseFloat(station.lat);
                                            totalLon += parseFloat(station.lon);
                                        });
                                        const centerLat = totalLat / stations.length;
                                        const centerLon = totalLon / stations.length;

                                // Картани марказлаштириш
                                mymap.setView([centerLat, centerLon], 10);
                                
                                // Статус бўйича фильтрлаш ва кўрсатиш
                                applyStatusFilter(stations, statType);
                                
                                // Барча вилоятлар статистикасини янгилаш
                                updateRegionsStats();
                                
                                // Фильтр ҳолатини сақлаш
                                saveFilterState();
                            }
                        });
                });
            });
        }
        
        // Вилоятлар статистикасини тайёрлаш функцияси
        function prepareRegionsStats() {
            // Регионлар номларини олиш
            fetch('/api/regions/')
                .then(response => response.json())
                .then(regions => {
                    // Рес. Узбекистан регионини фильтрлаб ташлаш
                    regions = regions.filter(region => region.name !== 'Рес. Узбекистан');
                    
                    // Ҳар бир регион учун БС ларни олиш
                    const promises = regions.map(region => {
                        return fetch(`/api/base-stations/region/${region.id}/`)
                            .then(response => response.json())
                            .then(stations => {
                                // Умумий БС маълумотларидан статус қийматларини олиш
                                const stationStatusMap = {};
                                points.forEach(point => {
                                    stationStatusMap[point.bsName || point.name] = point.status;
                                });
                                
                                stations = stations.map(station => {
                                    const stationName = station.bsName || station.name;
                                    const statusFromGlobal = stationStatusMap[stationName];
                                    return {
                                        ...station,
                                        status: statusFromGlobal !== undefined ? statusFromGlobal : (station.status === 'offline')
                                    };
                                });
                                
                                const total = stations.length;
                                const offline = stations.filter(station => station.status === true).length;
                                const online = total - offline;
                                
                                regionsStatsData[region.id] = {
                                    name: region.name,
                                    total: total,
                                    online: online,
                                    offline: offline
                                };
                            });
                    });
                    
                    // Барча сўровлар тугагандан сўнг статистика элементларини яратиш
                    Promise.all(promises).then(() => {
                        updateRegionsStats();
                    });
                });
        }

        // Маълумотларни янгилаш функцияси
        function refreshData() {
            // Сохраняем текущее состояние фильтров перед обновлением
            const currentRegion = document.getElementById('region').value;
            const currentArea = document.getElementById('area').value;
            const currentBsSearch = document.getElementById('bs-search').value;
            const currentStatus = document.getElementById('status').value;

            // Сохраняем состояние в localStorage
            saveFilterState();

            fetch(window.location.href)
                .then(response => response.text())
                .then(html => {
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');
                    const newPointsData = JSON.parse(doc.getElementById('points-data').textContent);

                    // Сохраняем старые данные для сравнения
                    const oldPoints = [...points];

                    // Получаем все точки для дальнейшей фильтрации
                    points = newPointsData;

                    // Обновляем общую статистику (умумий статистика)
                    totalBsCount = points.length;
                    inactiveBsCount = points.filter(point => point.status === true).length;
                    activeBsCount = totalBsCount - inactiveBsCount;
                    
                    // Глобал статистика қийматларини янгилаш
                    globalTotalBsCount = totalBsCount;
                    globalActiveBsCount = activeBsCount;
                    globalInactiveBsCount = inactiveBsCount;
                    
                    // Вилоятлар статистикасини янгилаш
                    prepareRegionsStats();

                    // Восстанавливаем состояние фильтров
                    document.getElementById('region').value = currentRegion;
                    document.getElementById('area').value = currentArea;
                    document.getElementById('bs-search').value = currentBsSearch;
                    document.getElementById('status').value = currentStatus;

                    // Применяем фильтры в зависимости от текущего состояния
                    const regionId = currentRegion ? parseInt(currentRegion) : null;
                    const areaId = currentArea ? parseInt(currentArea) : null;

                    // Применяем фильтры в зависимости от выбранных значений
                    if (regionId) {
                        // Если выбран регион
                        if (areaId) {
                            // Если выбран район
                            fetch(`/api/base-stations/area/${areaId}/`)
                                .then(response => response.json())
                                .then(stations => {
                                    // Синхронизируем статусы с глобальными данными
                                    const stationStatusMap = {};
                                    points.forEach(point => {
                                        stationStatusMap[point.bsName || point.name] = point.status;
                                    });

                                    stations = stations.map(station => {
                                        const stationName = station.bsName || station.name;
                                        const statusFromGlobal = stationStatusMap[stationName];
                                        return {
                                            ...station,
                                            status: statusFromGlobal !== undefined ? statusFromGlobal : (station.status === 'offline')
                                        };
                                    });

                                    // Применяем фильтр по имени/номеру БС
                                    if (currentBsSearch) {
                                        stations = filterByBsName(stations, currentBsSearch);
                                    }

                                    // Применяем фильтр по статусу
                                    applyStatusFilter(stations, currentStatus);
                                });
                        } else {
                            // Если выбран только регион
                            fetch(`/api/base-stations/region/${regionId}/`)
                                .then(response => response.json())
                                .then(stations => {
                                    // Синхронизируем статусы с глобальными данными
                                    const stationStatusMap = {};
                                    points.forEach(point => {
                                        stationStatusMap[point.bsName || point.name] = point.status;
                                    });

                                    stations = stations.map(station => {
                                        const stationName = station.bsName || station.name;
                                        const statusFromGlobal = stationStatusMap[stationName];
                                        return {
                                            ...station,
                                            status: statusFromGlobal !== undefined ? statusFromGlobal : (station.status === 'offline')
                                        };
                                    });

                                    // Применяем фильтр по имени/номеру БС
                                    if (currentBsSearch) {
                                        stations = filterByBsName(stations, currentBsSearch);
                                    }

                                    // Применяем фильтр по статусу
                                    applyStatusFilter(stations, currentStatus);
                                });
                        }
                    } else {
                        // Если не выбран ни регион, ни район
                        // Фильтруем по имени/номеру БС если задано
                        let filteredPoints = [...points];
                        if (currentBsSearch) {
                            filteredPoints = filterByBsName(filteredPoints, currentBsSearch);
                            // Если есть результаты поиска и они есть на карте, обновляем только изменившиеся
                            if (filteredPoints.length > 0) {
                                updateChangedMarkers(oldPoints, filteredPoints, currentStatus);
                            } else {
                                // Очищаем карту если нет результатов
                                pointLayers.forEach(marker => mymap.removeLayer(marker));
                                pointLayers.length = 0;

                                // Обновляем статистику
                                document.getElementById('total-bs-count').textContent = '0';
                                document.getElementById('active-bs-count').textContent = '0';
                                document.getElementById('inactive-bs-count').textContent = '0';
                            }
                        } else {
                            // Обновляем только маркеры, у которых изменился статус
                            updateChangedMarkers(oldPoints, points, currentStatus);
                        }
                    }
                })
                .catch(error => {
                    console.error('Маълумотларни янгилашда хато:', error);
                    // Хато бўлганда ҳам эски маркерларни сақлаб қолиш учун
                    if (points && points.length > 0) {
                        const statusValue = document.getElementById('status').value;
                        applyStatusFilter(points, statusValue, true);
                    }
                });
        }

        // Функция для фильтрации БС по имени или номеру
        function filterByBsName(stations, searchText) {
            searchText = searchText.toLowerCase();
            return stations.filter(station => {
                const bsName = (station.bsName || station.name || '').toLowerCase();
                const bsNumber = (station.bsNumber || station.number || '').toString().toLowerCase();
                return bsName.includes(searchText) || bsNumber.includes(searchText);
            });
        }

        // Функция для обновления только изменившихся маркеров (для режима все регионы)
        function updateChangedMarkers(oldPoints, newPoints, statusValue) {
            // Создаем карту для быстрого поиска точек по имени
            const oldPointsMap = {};
            oldPoints.forEach(point => {
                const pointName = point.bsName || point.name;
                oldPointsMap[pointName] = point;
            });

            // Фильтруем точки в соответствии с выбранным статусом
            let filteredNewPoints = [...newPoints];
            if (statusValue === 'online') {
                filteredNewPoints = newPoints.filter(point => point.status === false);
            } else if (statusValue === 'offline') {
                filteredNewPoints = newPoints.filter(point => point.status === true);
            }

            // Обновляем статистику
            const totalDisplayed = filteredNewPoints.length;
            const inactiveDisplayed = filteredNewPoints.filter(point => point.status === true).length;
            const activeDisplayed = totalDisplayed - inactiveDisplayed;

            document.getElementById('total-bs-count').textContent = totalDisplayed;
            document.getElementById('active-bs-count').textContent = activeDisplayed;
            document.getElementById('inactive-bs-count').textContent = inactiveDisplayed;

            // Создаем карту точек на карте по имени для быстрого доступа
            const markersMap = {};
            pointLayers.forEach(marker => {
                const pointName = marker.options.pointName;
                markersMap[pointName] = marker;
            });

            // Проверяем и обновляем существующие маркеры
            filteredNewPoints.forEach(point => {
                const pointName = point.bsName || point.name;

                // Если этот маркер уже есть на карте - обновляем его
                if (markersMap[pointName]) {
                    const marker = markersMap[pointName];
                    const oldPoint = oldPointsMap[pointName];

                    // Проверяем, изменился ли статус
                    if (oldPoint && oldPoint.status !== point.status) {
                        // Изменяем цвет маркера
                        const newColor = point.status === true ? 'red' : 'green';
                        marker.setStyle({ fillColor: newColor });

                        // Определяем, нужно ли показывать постоянную подпись (только для аварийных БС)
                        const permanentTooltip = statusValue === 'offline' && point.status === true;
                        
                        // Удаляем старую подсказку
                        marker.unbindTooltip();
                        
                        // Добавляем новую подсказку с улучшенным содержимым
                        let tooltipContent = "";
                        if (permanentTooltip) {
                            // Для постоянных подсказок только имя БС
                            tooltipContent = pointName;
                        } else {
                            // Для остальных - имя БС и статус
                            let statusText = point.status === true ? 'БС не работает' : 'БС активен';
                            tooltipContent = "<b>" + pointName + "</b><br>" + statusText;
                        }
                        
                        marker.bindTooltip(tooltipContent, {
                            permanent: permanentTooltip,
                            direction: permanentTooltip ? 'bottom' : 'top',
                            offset: permanentTooltip ? [0, 3] : [0, -10],
                            className: permanentTooltip ? 'bs-name-tooltip' : ''
                        });

                        // Выделяем изменившиеся маркеры анимацией
                        marker.setStyle({ fillOpacity: 1 });
                        setTimeout(() => {
                            marker.setStyle({ fillOpacity: 0.8 });
                        }, 500);
                    }
                    // Помечаем маркер как обработанный
                    markersMap[pointName] = null;
                }
                // Если такого маркера нет, создаем новый
                else {
                    // Координаталар тўғри эканини текшириш
                    if (!point.lat || !point.lon || isNaN(parseFloat(point.lat)) || isNaN(parseFloat(point.lon))) {
                        console.warn('Нотўғри координаталар:', point);
                        return; // Бу нуқтани ўтказиб юбориш
                    }

                    let pointColor = point.status === true ? 'red' : 'green';
                    
                    // Определяем, нужно ли показывать постоянную подпись (только для аварийных БС)
                    const permanentTooltip = statusValue === 'offline' && point.status === true;

                    const circleMarker = L.circleMarker([point.lat, point.lon], {
                        radius: getCircleRadius(mymap.getZoom()),
                        fillColor: pointColor,
                        color: "#000",
                        weight: 1,
                        opacity: 1,
                        fillOpacity: 0.8,
                        pointName: pointName // Сохраняем имя точки в опциях маркера
                    });
                    
                    // Добавляем всплывающую подсказку с улучшенным содержимым
                    let tooltipContent = "";
                    if (permanentTooltip) {
                        // Для постоянных подсказок только имя БС
                        tooltipContent = pointName;
                    } else {
                        // Для остальных - имя БС и статус
                        let statusText = point.status === true ? 'БС не работает' : 'БС активен';
                        tooltipContent = "<b>" + pointName + "</b><br>" + statusText;
                    }
                    
                    circleMarker.bindTooltip(tooltipContent, {
                        permanent: permanentTooltip,
                        direction: permanentTooltip ? 'bottom' : 'top',
                        offset: permanentTooltip ? [0, 3] : [0, -10],
                        className: permanentTooltip ? 'bs-name-tooltip' : ''
                    });
                    
                    circleMarker.addTo(mymap);
                    pointLayers.push(circleMarker);
                }
            });

            // Удаляем маркеры, которых больше нет в новых данных
            const newPointNames = filteredNewPoints.map(p => p.bsName || p.name);

            for (let i = pointLayers.length - 1; i >= 0; i--) {
                const marker = pointLayers[i];
                const pointName = marker.options.pointName;

                if (!newPointNames.includes(pointName)) {
                    mymap.removeLayer(marker);
                    pointLayers.splice(i, 1);
                }
            }
        }

        // Функция для применения фильтра по статусу и отображения точек
        function applyStatusFilter(pointsToFilter, statusValue, isRefresh = false) {
            // Если это не рефреш или выбран регион/район, удаляем все маркеры и создаем заново
            if (!isRefresh || document.getElementById('region').value) {
                // Удаляем все существующие маркеры с карты
                pointLayers.forEach(marker => mymap.removeLayer(marker));
                pointLayers.length = 0;
            }

            console.log('Фильтрга келган маълумотлар:', pointsToFilter);
            let displayPoints = [...pointsToFilter];

            // Фильтр по статусу - это второй уровень фильтрации после региона/района
            if (statusValue === 'online') {
                displayPoints = pointsToFilter.filter(point => point.status === false);
                console.log('Онлайн фильтр натижаси:', displayPoints);
            } else if (statusValue === 'offline') {
                displayPoints = pointsToFilter.filter(point => point.status === true);
                console.log('Оффлайн фильтр натижаси:', displayPoints);
            }

            // Обновляем только локальную статистику для текущего выбора
            const totalDisplayed = displayPoints.length;
            const inactiveDisplayed = displayPoints.filter(point => point.status === true).length;
            const activeDisplayed = totalDisplayed - inactiveDisplayed;
            console.log('Фильтр натижаси - Жами:', totalDisplayed, 'Онлайн:', activeDisplayed, 'Оффлайн:', inactiveDisplayed);

            // Обновляем статистику в сайдбаре
            document.getElementById('total-bs-count').textContent = totalDisplayed;
            document.getElementById('active-bs-count').textContent = activeDisplayed;
            document.getElementById('inactive-bs-count').textContent = inactiveDisplayed;

            // Если это рефреш и не выбран регион/район, обновление маркеров выполняет updateChangedMarkers
            if (isRefresh && !document.getElementById('region').value) {
                return;
            }

            // Отображаем отфильтрованные точки на карте
            if (displayPoints && displayPoints.length > 0) {
                displayPoints.forEach(function (point) {
                    // Координаталар тўғри эканини текшириш
                    if (!point.lat || !point.lon || isNaN(parseFloat(point.lat)) || isNaN(parseFloat(point.lon))) {
                        console.warn('Нотўғри координаталар:', point);
                        return; // Бу нуқтани ўтказиб юбориш
                    }

                    let pointColor = point.status === true ? 'red' : 'green';
                    const pointName = point.bsName || point.name;
                    
                    // Определяем, нужно ли показывать постоянную подпись (только для аварийных БС)
                    const permanentTooltip = statusValue === 'offline' && point.status === true;

                    const circleMarker = L.circleMarker([point.lat, point.lon], {
                        radius: getCircleRadius(mymap.getZoom()),
                        fillColor: pointColor,
                        color: "#000",
                        weight: 1,
                        opacity: 1,
                        fillOpacity: 0.8,
                        pointName: pointName // Сохраняем имя точки в опциях маркера
                    });
                    
                    // Добавляем всплывающую подсказку с улучшенным содержимым
                    let tooltipContent = "";
                    if (permanentTooltip) {
                        // Для постоянных подсказок только имя БС
                        tooltipContent = pointName;
                    } else {
                        // Для остальных - имя БС и статус
                        let statusText = point.status === true ? 'БС не работает' : 'БС активен';
                        tooltipContent = "<b>" + pointName + "</b><br>" + statusText;
                    }
                    
                    circleMarker.bindTooltip(tooltipContent, {
                        permanent: permanentTooltip,
                        direction: permanentTooltip ? 'bottom' : 'top',
                        offset: permanentTooltip ? [0, 3] : [0, -10],
                        className: permanentTooltip ? 'bs-name-tooltip' : ''
                    });
                    
                    circleMarker.addTo(mymap);
                    pointLayers.push(circleMarker);
                });
                console.log('Картага қўшилган нуқталар сони:', pointLayers.length);
            } else {
                console.warn('Кўрсатиш учун нуқталар мавжуд эмас!');
            }
        }

        // Ҳар 10 секундда маълумотларни янгилаш
        setInterval(refreshData, 10000);

        // 1. Инициализация карты
        var mymap = L.map('mapid').setView([41.3, 69.3], 6) // Марказ Ўзбекистон - умумий кўриниш

        // 2. Добавление базового слоя карты (по умолчанию)
        var osmLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(mymap);

        // 3. Добавление спутникового слоя
        var satelliteLayer = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
            attribution: '&copy; Esri &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community',
            maxZoom: 20
        });

        // 4. Получение данных о точках
        const pointsDataElement = document.getElementById('points-data');
        var points = JSON.parse(pointsDataElement.textContent);
        console.log('Умумий БС маълумотлари:', points);

        // Подсчет статистики БС
        var totalBsCount = points.length;
        var inactiveBsCount = points.filter(point => point.status === true).length;
        var activeBsCount = totalBsCount - inactiveBsCount;
        console.log('Умумий БС сони:', totalBsCount);
        console.log('Оффлайн БС сони:', inactiveBsCount);
        console.log('Онлайн БС сони:', activeBsCount);
        
        // Глобал статистика қийматларини сақлаш
        globalTotalBsCount = totalBsCount;
        globalActiveBsCount = activeBsCount;
        globalInactiveBsCount = inactiveBsCount;

        // Отображение статистики
        document.getElementById('total-bs-count').textContent = totalBsCount;
        document.getElementById('active-bs-count').textContent = activeBsCount;
        document.getElementById('inactive-bs-count').textContent = inactiveBsCount;

        // Функция для определения размера круга в зависимости от уровня зума
        function getCircleRadius(zoom) {
            if (zoom < 6) {
                return 2;
            } else if (zoom < 10) {
                return 4;
            } else {
                return 10;
            }
        }

        // Функция для обновления размера кругов и текста на карте
        function updateCircleSizes() {
            const currentZoom = mymap.getZoom();
            const newRadius = getCircleRadius(currentZoom);
            
            // Определяем класс для масштаба текста
            let zoomClass = '';
            if (currentZoom < 8) {
                zoomClass = 'zoom-level-low';
            } else if (currentZoom < 12) {
                zoomClass = 'zoom-level-medium';
            } else {
                zoomClass = 'zoom-level-high';
            }
            
            // Сначала удаляем все классы масштаба
            document.body.classList.remove('zoom-level-low', 'zoom-level-medium', 'zoom-level-high');
            // Добавляем нужный класс
            document.body.classList.add(zoomClass);
            
            // Обновляем радиус всех маркеров
            mymap.eachLayer(function (layer) {
                if (layer instanceof L.CircleMarker) {
                    layer.setRadius(newRadius);
                    
                    // Если это аварийный БС с постоянной подсказкой, обновляем offset подсказки
                    if (layer.getTooltip() && layer.getTooltip().options.permanent) {
                        // Обновляем offset в зависимости от зума
                        let offsetValue = 3;
                        if (currentZoom < 8) {
                            offsetValue = 2;
                        } else if (currentZoom >= 12) {
                            offsetValue = 4;
                        }
                        
                        // Получаем текущий контент подсказки
                        const tooltipContent = layer.getTooltip().getContent();
                        
                        // Удаляем старую подсказку
                        layer.unbindTooltip();
                        
                        // Создаем новую подсказку с обновленным offset
                        layer.bindTooltip(tooltipContent, {
                            permanent: true,
                            direction: 'bottom',
                            offset: [0, offsetValue],
                            className: 'bs-name-tooltip'
                        });
                    }
                }
            });
        }

        // Создаем и добавляем маркеры на карту
        const pointLayers = []; // Массив для хранения слоев маркеров
        const pointsByRegion = {}; // Объект для хранения точек по регионам
        const pointsByArea = {}; // Объект для хранения точек по районам

        // Координаты центров регионов
        const regionCenters = {
            1: [41.311081, 69.240562], // Тошкент шаҳри
            2: [40.783555, 72.350891], // Андижон
            3: [40.384240, 71.785690], // Фарғона
            4: [41.001071, 71.672278], // Наманган
            5: [39.768083, 64.421710], // Бухоро
            6: [40.121462, 67.842194], // Жиззах
            7: [38.839802, 65.781462], // Қашқадарё
            8: [40.103922, 65.374260], // Навоий
            9: [41.248990, 69.333240], // Тошкент вилояти
            10: [40.837641, 68.661338], // Сирдарё
            11: [37.940552, 67.510929], // Сурхондарё
            12: [41.552080, 60.631622], // Хоразм
            13: [43.804363, 59.018464], // Қорақалпоғистон
            14: [39.654388, 66.975824]  // Самарқанд
        };

        // Загрузка регионов
        fetch('/api/regions/')
            .then(response => response.json())
            .then(regions => {
                const regionSelect = document.getElementById('region');
                regions.forEach(region => {
                    const option = document.createElement('option');
                    option.value = region.id;
                    option.textContent = region.name;
                    regionSelect.appendChild(option);
                });
                
                // Вилоятлар статистикасини тайёрлаш
                prepareRegionsStats();
            });

        // Обработчик изменения региона
        document.getElementById('region').addEventListener('change', function () {
            const regionId = parseInt(this.value);
            const statusValue = document.getElementById('status').value;
            const bsSearch = document.getElementById('bs-search').value.trim();
            saveFilterState();
            const areaSelect = document.getElementById('area');

            // Очистка списка районов
            areaSelect.innerHTML = '<option value="">Все районы</option>';

            // Если выбрано "Все регионы" или значение пустое
            if (!regionId) {
                // Отключаем выбор района
                areaSelect.disabled = true;
                // Возвращаем карту к общему виду Узбекистана
                mymap.setView([41.3, 69.3], 6); // Марказ Ўзбекистон - умумий кўриниш

                // Применяем фильтр ко всем точкам
                let filteredPoints = [...points];

                // Применяем фильтр по имени/номеру БС если задан
                if (bsSearch) {
                    filteredPoints = filterByBsName(filteredPoints, bsSearch);
                }

                // Применяем фильтр по статусу
                applyStatusFilter(filteredPoints, statusValue);
                return;
            }

            if (regionId) {
                // Загрузка районов для выбранного региона
                fetch(`/api/areas/region/${regionId}/`)
                    .then(response => response.json())
                    .then(areas => {
                        areas.forEach(area => {
                            const option = document.createElement('option');
                            option.value = area.id;
                            option.textContent = area.name;
                            areaSelect.appendChild(option);
                        });
                        areaSelect.disabled = false;
                    });

                // Загрузка базовых станций для выбранного региона и центрирование карты
                fetch(`/api/base-stations/region/${regionId}/`)
                    .then(response => response.json())
                    .then(stations => {
                        if (stations.length > 0) {
                            // Синхронизируем статусы с глобальными данными
                            const stationStatusMap = {};
                            points.forEach(point => {
                                stationStatusMap[point.bsName || point.name] = point.status;
                            });

                            stations = stations.map(station => {
                                const stationName = station.bsName || station.name;
                                const statusFromGlobal = stationStatusMap[stationName];
                                return {
                                    ...station,
                                    status: statusFromGlobal !== undefined ? statusFromGlobal : (station.status === 'offline')
                                };
                            });

                            // Фильтрация по имени/номеру БС если задано
                            let filteredStations = stations;
                            if (bsSearch) {
                                filteredStations = filterByBsName(stations, bsSearch);
                            }

                            // Если есть станции после фильтрации
                            if (filteredStations.length > 0) {
                                // Вычисляем центр региона на основе координат станций
                                let totalLat = 0;
                                let totalLon = 0;
                                filteredStations.forEach(station => {
                                    totalLat += parseFloat(station.lat);
                                    totalLon += parseFloat(station.lon);
                                });
                                const centerLat = totalLat / filteredStations.length;
                                const centerLon = totalLon / filteredStations.length;

                                // Центрируем карту на вычисленном центре региона
                                mymap.setView([centerLat, centerLon], 10);

                                // Применяем фильтр по статусу к станциям региона
                                applyStatusFilter(filteredStations, statusValue);
                            } else {
                                // Если нет станций после фильтрации
                                pointLayers.forEach(marker => mymap.removeLayer(marker));
                                pointLayers.length = 0;

                                // Обновляем статистику
                                document.getElementById('total-bs-count').textContent = '0';
                                document.getElementById('active-bs-count').textContent = '0';
                                document.getElementById('inactive-bs-count').textContent = '0';

                                // Используем центр региона из заранее определенных координат
                                if (regionCenters[regionId]) {
                                    mymap.setView(regionCenters[regionId], 10);
                                }
                            }
                        }
                    });
            } else {
                // Если регион не выбран, отключаем выбор района
                areaSelect.disabled = true;

                // Возвращаем карту к общему виду
                mymap.setView([41.3, 69.3], 6);

                // Применяем фильтр ко всем точкам
                let filteredPoints = [...points];

                // Применяем фильтр по имени/номеру БС если задан
                if (bsSearch) {
                    filteredPoints = filterByBsName(filteredPoints, bsSearch);
                }

                // Применяем фильтр по статусу
                applyStatusFilter(filteredPoints, statusValue);
            }
        });

        // Обработчик изменения района
        document.getElementById('area').addEventListener('change', function () {
            const areaId = parseInt(this.value);
            const statusValue = document.getElementById('status').value;
            const bsSearch = document.getElementById('bs-search').value.trim();
            saveFilterState();

            if (areaId) {
                // Загрузка базовых станций для выбранного района
                fetch(`/api/base-stations/area/${areaId}/`)
                    .then(response => response.json())
                    .then(stations => {
                        if (stations.length > 0) {
                            // Синхронизируем статусы с глобальными данными
                            const stationStatusMap = {};
                            points.forEach(point => {
                                stationStatusMap[point.bsName || point.name] = point.status;
                            });

                            stations = stations.map(station => {
                                const stationName = station.bsName || station.name;
                                const statusFromGlobal = stationStatusMap[stationName];
                                return {
                                    ...station,
                                    status: statusFromGlobal !== undefined ? statusFromGlobal : (station.status === 'offline')
                                };
                            });

                            // Фильтрация по имени/номеру БС если задано
                            let filteredStations = stations;
                            if (bsSearch) {
                                filteredStations = filterByBsName(stations, bsSearch);
                            }

                            // Если есть станции после фильтрации
                            if (filteredStations.length > 0) {
                                // Вычисляем центр района на основе координат станций
                                let totalLat = 0;
                                let totalLon = 0;
                                filteredStations.forEach(station => {
                                    totalLat += parseFloat(station.lat);
                                    totalLon += parseFloat(station.lon);
                                });
                                const centerLat = totalLat / filteredStations.length;
                                const centerLon = totalLon / filteredStations.length;

                                // Центрируем карту на вычисленном центре района
                                mymap.setView([centerLat, centerLon], 11);

                                // Применяем фильтр по статусу к станциям района
                                applyStatusFilter(filteredStations, statusValue);
                            } else {
                                // Если нет станций после фильтрации
                                pointLayers.forEach(marker => mymap.removeLayer(marker));
                                pointLayers.length = 0;

                                // Обновляем статистику
                                document.getElementById('total-bs-count').textContent = '0';
                                document.getElementById('active-bs-count').textContent = '0';
                                document.getElementById('inactive-bs-count').textContent = '0';
                            }
                        }
                    });
            } else {
                // Если район не выбран, возвращаемся к виду региона
                const regionId = parseInt(document.getElementById('region').value);
                if (regionId) {
                    // Загружаем станции для выбранного региона
                    fetch(`/api/base-stations/region/${regionId}/`)
                        .then(response => response.json())
                        .then(stations => {
                            if (stations.length > 0) {
                                // Синхронизируем статусы с глобальными данными
                                const stationStatusMap = {};
                                points.forEach(point => {
                                    stationStatusMap[point.bsName || point.name] = point.status;
                                });

                                stations = stations.map(station => {
                                    const stationName = station.bsName || station.name;
                                    const statusFromGlobal = stationStatusMap[stationName];
                                    return {
                                        ...station,
                                        status: statusFromGlobal !== undefined ? statusFromGlobal : (station.status === 'offline')
                                    };
                                });

                                // Фильтрация по имени/номеру БС если задано
                                let filteredStations = stations;
                                if (bsSearch) {
                                    filteredStations = filterByBsName(stations, bsSearch);
                                }

                                // Если есть станции после фильтрации
                                if (filteredStations.length > 0) {
                                    if (regionCenters[regionId]) {
                                        mymap.setView(regionCenters[regionId], 8);
                                    } else {
                                        // Вычисляем центр региона на основе координат станций
                                        let totalLat = 0;
                                        let totalLon = 0;
                                        filteredStations.forEach(station => {
                                            totalLat += parseFloat(station.lat);
                                            totalLon += parseFloat(station.lon);
                                        });
                                        const centerLat = totalLat / filteredStations.length;
                                        const centerLon = totalLon / filteredStations.length;
                                        mymap.setView([centerLat, centerLon], 8);
                                    }

                                    // Применяем фильтр по статусу к станциям региона
                                    applyStatusFilter(filteredStations, statusValue);
                                } else {
                                    // Если нет станций после фильтрации
                                    pointLayers.forEach(marker => mymap.removeLayer(marker));
                                    pointLayers.length = 0;

                                    // Обновляем статистику
                                    document.getElementById('total-bs-count').textContent = '0';
                                    document.getElementById('active-bs-count').textContent = '0';
                                    document.getElementById('inactive-bs-count').textContent = '0';

                                    // Используем центр региона из заранее определенных координат
                                    if (regionCenters[regionId]) {
                                        mymap.setView(regionCenters[regionId], 8);
                                    }
                                }
                            }
                        });
                } else {
                    // Если ни регион, ни район не выбраны
                    mymap.setView([41.3, 69.3], 6); // Марказ Ўзбекистон - умумий кўриниш

                    // Применяем фильтр ко всем точкам
                    let filteredPoints = [...points];

                    // Применяем фильтр по имени/номеру БС если задан
                    if (bsSearch) {
                        filteredPoints = filterByBsName(filteredPoints, bsSearch);
                    }

                    // Применяем фильтр по статусу
                    applyStatusFilter(filteredPoints, statusValue);
                }
            }
        });

        // Статистика теперь обновляется в функции applyStatusFilter

        // Функция для сброса всех фильтров
        function resetAllFilters() {
            // Сбрасываем значения в селектах
            document.getElementById('region').value = '';
            document.getElementById('area').value = '';
            document.getElementById('bs-search').value = '';
            document.getElementById('status').value = 'all';

            // Отключаем выбор района
            document.getElementById('area').disabled = true;

            // Возвращаем карту к общему виду Узбекистана
            mymap.setView([41.3, 69.3], 6);

            // Показываем все точки
            applyStatusFilter(points, 'all');
            
            // Барча вилоятлар статистикасини янгилаш
            updateRegionsStats();

            // Сохраняем сброшенные фильтры в localStorage
            saveFilterState();
        }

        // Саҳифа юкланганда фильтр қийматларини тиклаш
        document.addEventListener('DOMContentLoaded', function () {
            // Вилоятлар статистикасини тайёрлаш
            prepareRegionsStats();
            
            // Восстанавливаем фильтры из localStorage
            restoreFilterState();

            // Добавляем обработчик события для кнопки сброса фильтров
            document.getElementById('reset-filters').addEventListener('click', resetAllFilters);

            // Обработчик события для поля поиска БС
            document.getElementById('bs-search').addEventListener('input', function () {
                const searchValue = this.value.trim();
                const regionId = parseInt(document.getElementById('region').value) || null;
                const areaId = parseInt(document.getElementById('area').value) || null;
                const statusValue = document.getElementById('status').value;

                // Сохраняем состояние фильтров
                saveFilterState();

                // Удаляем все маркеры с карты
                pointLayers.forEach(marker => mymap.removeLayer(marker));
                pointLayers.length = 0;

                // Выбираем источник данных для фильтрации
                let dataToFilter;
                if (regionId) {
                    if (areaId) {
                        // Если выбран район, загружаем данные района
                        fetch(`/api/base-stations/area/${areaId}/`)
                            .then(response => response.json())
                            .then(stations => {
                                // Синхронизируем статусы с глобальными данными
                                const stationStatusMap = {};
                                points.forEach(point => {
                                    stationStatusMap[point.bsName || point.name] = point.status;
                                });

                                stations = stations.map(station => {
                                    const stationName = station.bsName || station.name;
                                    const statusFromGlobal = stationStatusMap[stationName];
                                    return {
                                        ...station,
                                        status: statusFromGlobal !== undefined ? statusFromGlobal : (station.status === 'offline')
                                    };
                                });

                                // Применяем фильтр по имени/номеру БС
                                if (searchValue) {
                                    stations = filterByBsName(stations, searchValue);
                                }

                                // Применяем фильтр по статусу
                                applyStatusFilter(stations, statusValue);
                            });
                    } else {
                        // Если выбран только регион, загружаем данные региона
                        fetch(`/api/base-stations/region/${regionId}/`)
                            .then(response => response.json())
                            .then(stations => {
                                // Синхронизируем статусы с глобальными данными
                                const stationStatusMap = {};
                                points.forEach(point => {
                                    stationStatusMap[point.bsName || point.name] = point.status;
                                });

                                stations = stations.map(station => {
                                    const stationName = station.bsName || station.name;
                                    const statusFromGlobal = stationStatusMap[stationName];
                                    return {
                                        ...station,
                                        status: statusFromGlobal !== undefined ? statusFromGlobal : (station.status === 'offline')
                                    };
                                });

                                // Применяем фильтр по имени/номеру БС
                                if (searchValue) {
                                    stations = filterByBsName(stations, searchValue);
                                }

                                // Применяем фильтр по статусу
                                applyStatusFilter(stations, statusValue);
                            });
                    }
                } else {
                    // Если не выбран ни регион, ни район, фильтруем все точки
                    let filteredPoints = [...points];

                    // Применяем фильтр по имени/номеру БС
                    if (searchValue) {
                        filteredPoints = filterByBsName(filteredPoints, searchValue);
                    }

                    // Применяем фильтр по статусу
                    applyStatusFilter(filteredPoints, statusValue);
                }
            });
        });

        // Обработчик изменения статуса
        document.getElementById('status').addEventListener('change', function () {
            const statusValue = this.value;
            const regionId = parseInt(document.getElementById('region').value) || null;
            const areaId = parseInt(document.getElementById('area').value) || null;
            const bsSearch = document.getElementById('bs-search').value.trim();

            saveFilterState();

            // Удаляем все маркеры с карты
            pointLayers.forEach(marker => mymap.removeLayer(marker));
            pointLayers.length = 0;

            // Применяем фильтры в зависимости от выбранных значений
            if (regionId) {
                // Если выбран регион
                if (areaId) {
                    // Если выбран район
                    fetch(`/api/base-stations/area/${areaId}/`)
                        .then(response => response.json())
                        .then(stations => {
                            // Синхронизируем статусы с глобальными данными
                            const stationStatusMap = {};
                            points.forEach(point => {
                                stationStatusMap[point.bsName || point.name] = point.status;
                            });

                            stations = stations.map(station => {
                                const stationName = station.bsName || station.name;
                                const statusFromGlobal = stationStatusMap[stationName];
                                return {
                                    ...station,
                                    status: statusFromGlobal !== undefined ? statusFromGlobal : (station.status === 'offline')
                                };
                            });

                            // Применяем фильтр по имени/номеру БС
                            if (bsSearch) {
                                stations = filterByBsName(stations, bsSearch);
                            }

                            // Применяем фильтр по статусу
                            applyStatusFilter(stations, statusValue);
                        });
                } else {
                    // Если выбран только регион
                    fetch(`/api/base-stations/region/${regionId}/`)
                        .then(response => response.json())
                        .then(stations => {
                            // Синхронизируем статусы с глобальными данными
                            const stationStatusMap = {};
                            points.forEach(point => {
                                stationStatusMap[point.bsName || point.name] = point.status;
                            });

                            stations = stations.map(station => {
                                const stationName = station.bsName || station.name;
                                const statusFromGlobal = stationStatusMap[stationName];
                                return {
                                    ...station,
                                    status: statusFromGlobal !== undefined ? statusFromGlobal : (station.status === 'offline')
                                };
                            });

                            // Применяем фильтр по имени/номеру БС
                            if (bsSearch) {
                                stations = filterByBsName(stations, bsSearch);
                            }

                            // Применяем фильтр по статусу
                            applyStatusFilter(stations, statusValue);
                        });
                }
            } else {
                // Если не выбран ни регион, ни район
                let filteredPoints = [...points];

                // Применяем фильтр по имени/номеру БС
                if (bsSearch) {
                    filteredPoints = filterByBsName(filteredPoints, bsSearch);
                }

                // Применяем фильтр по статусу ко всем точкам
                applyStatusFilter(filteredPoints, statusValue);
            }
        });

        // Обработчики кликов на кнопки переключения типа карты
        const mapButton = document.getElementById('map-button');
        const satelliteButton = document.getElementById('satellite-button');

        mapButton.addEventListener('click', function () {
            mymap.removeLayer(satelliteLayer);
            osmLayer.addTo(mymap);
            mapButton.classList.add('active');
            satelliteButton.classList.remove('active');
        });

        satelliteButton.addEventListener('click', function () {
            mymap.removeLayer(osmLayer);
            satelliteLayer.addTo(mymap);
            satelliteButton.classList.add('active');
            mapButton.classList.remove('active');
        });

        // Слушаем событие 'zoomend'
        mymap.on('zoomend', updateCircleSizes);

        // Ўзбекистон чегаралари
        const uzbekistanBounds = [
            [37.1, 55.9], // Жанубий-ғарбий нуқта
            [45.6, 73.1]  // Шимолий-шарқий нуқта
        ];
        // Картани Ўзбекистон билан чегаралаш
        mymap.setMaxBounds(uzbekistanBounds);
    </script>

</body>

{% endblock content %}