{% extends 'base.html' %}
{% load static %}

{% block content %}
<body>
<div class="container">
  <h1>Загрузка файла</h1>

  <!--
       method="POST" - важный момент.
       enctype="multipart/form-data" - обязательно для загрузки файлов.
       action="{% url 'upload' %}" - используем name='upload' из urls.py
  -->
  <form method="POST" action="{% url 'upload' %}" enctype="multipart/form-data">
    <!-- Обязательная строка для защиты от CSRF -->
    {% csrf_token %}

    <div class="form-group">
      <label for="id_file">Выберите файл:</label>
      <!-- Имя поля (name) должно совпадать с тем, что используем в views (myfile) -->
      <input type="file" class="form-control-file" id="id_file" name="file" required>
    </div>

    <button type="submit" class="btn btn-primary mt-3">Загрузить</button>
  </form>
</div>
</body>

 {% endblock content %}
