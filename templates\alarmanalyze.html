{% extends 'base.html' %}
{% load static %}
{% block content %}
<script type="text/javascript" src="{% static 'js/jquery.min.js' %}"></script>
<script type="text/javascript" src="{% static 'js/moment.min.js' %}"></script>
<script type="text/javascript" src="{% static 'js/daterangepicker.js' %}"></script>
<link rel="stylesheet" type="text/css" href="{% static 'css/bootstrap.css' %}"/>
<link rel="stylesheet" type="text/css" href="{% static 'css/daterangepicker.css' %}"/>

<div class="jumbotron">
    <p>
</div>
<body>
    <h1>На стадии разработки!!!</h1>
<!--<H3>Функция работает !!!</H3>-->
<form action="{% url 'alarmanalyzeview' %}" method="post">
    {% csrf_token %}

    <div class="container card ">
        <div class="row ">
                <table>
                    <TR>
                        <td width="60%">
                            <label class="form-control bg-light" aria-label="Выбор диапазона даты">
                                <input class="form-control bg-light" type="text" name="daterange" value="Выбор даты"/>
                            </label>
                        </td>
                        <Td width="20%">
                            <label class="form-control bg-light" aria-label="Выбор БС">
                                <input type="text" class="form-control" placeholder="Номер БС"
                                       aria-label="Номер БС" aria-describedby="button-addon2" name="bsnumber">
                            </label>
                        </td>
                        <TD width="20%" align="center">
                            <label class="form-control bg-light" aria-label="Нажать на кнопку">
                                <div class="d-grid gap-2">
                                <button class="btn btn-primary"  type="submit" id="knopka">Показать</button>
                                </div>
                            </label>
                        </TD>
                    </TR>
                </table>
        </div>
    </div>
    <br>

</form>
<p>


<script type="text/javascript">
$(function() {
    $('input[name="daterange"]').daterangepicker({
        showWeekNumbers: true,
        "locale": {
        "format": "DD/MM/YYYY",
        "separator": " - ",
        "applyLabel": "Сохранить",
        "cancelLabel": "Назад",
        "daysOfWeek": [
            "Вс",
            "Пн",
            "Вт",
            "Ср",
            "Чт",
            "Пт",
            "Сб"
        ],
        "monthNames": [
            "Январь",
            "Февраль",
            "Март",
            "Апрель",
            "Май",
            "Июнь",
            "Июль",
            "Август",
            "Сентябрь",
            "Октябрь",
            "Ноябрь",
            "Декабрь"
        ],
        "firstDay": 1
    }}
    );
});
</script>

</body>
{% endblock content %}
