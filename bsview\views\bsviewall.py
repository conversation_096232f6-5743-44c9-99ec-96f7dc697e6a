from bsview.views.imports_file import *

class BsPageView(LoginRequiredMixin, ListView):
    model = BsBeeline
    template_name = 'bsview.html'
    paginate_by = 30

    # bss = Bsbeeline.objects.filter(region='Andijan')
    def get_queryset(self):
        # if self.request.user.privilege_id != 1:
        #     return BsBeeline.objects.filter(region=self.request.user.region)
        # else:
        return BsBeeline.objects.filter(region=self.request.user.region)
class BsUpdate(LoginRequiredMixin, UpdateView):
    model = BsBeeline
    template_name = 'updatebs.html'
    form_class = UpdForm


class BsRegionView(LoginRequiredMixin, ListView):
    model = BsBeeline
    template_name = 'bsregionview.html'
    paginate_by = 20

    # bss = BsBeeline.objects.filter(region='Andijan')
    def get_queryset(self):
        if self.kwargs['region']:
            return BsBeeline.objects.filter(region=self.kwargs['region'])


class BsDetailView(LoginRequiredMixin, DetailView, UpdateView):
    model = BsBeeline
    form_class = UpdForm
    template_name = 'viewdetail.html'


class BsDeleteView(LoginRequiredMixin, DeleteView):
    model = BsBeeline
    template_name = 'bs_delete.html'
    success_url = reverse_lazy('bsview')


@login_required
def search_page1(request):
    bsname = request.POST['bsname']
    if request.user.privilege_id == 1 or request.user.privilege_id == 4:
        return render(request, 'search.html', {'baza': BsBeeline.objects.filter(bsname__icontains=bsname)})
    else:
        return render(request, 'search.html',
                      {'baza': BsBeeline.objects.filter(bsname__icontains=bsname, region=request.user.region)})


@login_required
def search_page2(request):
    bsnumber = request.POST['bsnumber']
    if request.user.privilege_id != 1 or request.user.privilege_id != 4:
        return render(request, 'search.html', {'baza': BsBeeline.objects.filter(bsnum=bsnumber)})
    else:
        return render(request, 'search.html',
                      {'baza': BsBeeline.objects.filter(bsnum=bsnumber, region=request.user.region)})


def accessdeny(request):
    return render(request, 'access_deny.html')


def closeWindow(request):
    return render(request, 'closewindow.html')


@login_required
def addBs(request):
    if request.method == 'POST':
        form = BsForm(request.POST)
        if form.is_valid():
            # print(form.cleaned_data)
            try:
                BsBeeline.objects.create(**form.cleaned_data)
                return redirect('bsview')
            except Exception as e:
                response = "<H1> ", str(e.args)[1:40], "<h1>"
                return HttpResponse(response)


    else:
        form = BsForm
    regionid = request.GET.get('region', None)

    if regionid:
        getregion = RegionUzb.objects.get(id=regionid)

    return render(request, 'addbs.html', {'form': form})


@login_required
def editBs(request):
    if request.method == 'POST':
        form = UpdForm(request.POST)
        if form.is_valid():
            form.save()
    else:
        form = BsForm
    return render(request, 'editbs.html', {'form': form})


# AJAX
@login_required
def load_areas(request):
    region_id = request.GET.get('region')
    areas = AreaUzb.objects.filter(region_id=region_id).all()
    return render(request, 'dropdown_area.html', {'areas': areas})
    # return JsonResponse(list(cities.values('id', 'name')), safe=False)

