{% extends 'base.html' %}
{% load static %}

{% block content %}
<script type="text/javascript" src="{% static 'js/jquery.min.js' %}"></script>
<script type="text/javascript" src="{% static 'js/moment.min.js' %}"></script>
<script type="text/javascript" src="{% static 'js/daterangepicker.js' %}"></script>
<link rel="stylesheet" type="text/css" href="{% static 'css/bootstrap.css' %}"/>
<link rel="stylesheet" type="text/css" href="{% static 'css/daterangepicker.css' %}"/>

<div class="jumbotron">
    <p>
</div>
<body>
<!--<H3>Страница находится на стадии разработки !!!</H3>-->
<!--H3>Базовая станция: {{ bsnumber }} | Количество аварии: {{ kolAlm }} | Продолжительность: {{ sumTime }} </H3>-->
<b><table class="table table-hover" ><TR>
    <td> Базовая станция: </td>
    <td></td>
    <td> Количество аварий: </td>
    <td>  </td>
    <td> Количество Boolean1: </td>
    <td>  </td>

</TR></table></b>


<form action="{% url 'alarmanalyzeview' %}" method="post">
    {% csrf_token %}

    <div class="container card ">
        <div class="row ">
            <table>
                <TR>
                    <td width="60%">
                        <label class="form-control bg-light" aria-label="Выбор диапазона даты">
                            <input class="form-control bg-light" type="text" name="daterange" value={{ dat }}/>
                        </label>
                    </td>
                    <Td width="20%">
                        <label class="form-control bg-light" aria-label="Выбор БС">
                            <input type="text" class="form-control" placeholder="Номер БС" value={{ bsnomer }}
                                   aria-label="Номер БС" aria-describedby="button-addon2" name="bsnumber">
                        </label>
                    </td>
                    <TD width="20%" align="center">
                        <label class="form-control bg-light" aria-label="Нажать на кнопку">
                            <div class="d-grid gap-2">
                                <button class="btn btn-primary" type="submit" id="knopka">Показать</button>
                            </div>
                        </label>
                    </TD>
                </TR>
            </table>
        </div>
    </div>
</form>
<table class="table table-hover table-bordered" border="2">
    <br>
    <tr><b>
        <td> Дата\Время: </td>
        {% for hour in hours %}
        <td align="center"><b>{{ hour }}</b></td>
        {% endfor %}
    </b></TR>
        {% for day in date_list %}
        <tr>
        {% if day.weekday == 6 or day.weekday == 5 %}
            <td class="text-warning"><b>{{ day|date:"d.m.Y"}}</b></td>
            {% else %}
            <td><b>{{ day|date:"d.m.Y" }}</b></td>
        {% endif %}

        {% for hour in hours %}
        <td></td>
        {% endfor %}
        </tr>
        {% endfor %}
</table>

<script type="text/javascript">

var today = new Date("{{ start_date }}");
var endDate = new Date("{{ end_date }}");

$(function() {
    $('input[name="daterange"]').daterangepicker({
    startDate: today,
    endDate: endDate,
    showWeekNumbers: true,
    "locale": {
        "format": "DD/MM/YYYY",
        "separator": " - ",
        "applyLabel": "Сохранить",
        "cancelLabel": "Назад",
        "daysOfWeek": [
            "Вс",
            "Пн",
            "Вт",
            "Ср",
            "Чт",
            "Пт",
            "Сб"
        ],
        "monthNames": [
            "Январь",
            "Февраль",
            "Март",
            "Апрель",
            "Май",
            "Июнь",
            "Июль",
            "Август",
            "Сентябрь",
            "Октябрь",
            "Ноябрь",
            "Декабрь"
        ],
        "firstDay": 1
    }}
    );
});

</script>
</body>

<div class="col-12">
{% include "pagination.html" %}
</div>


{% endblock %}

