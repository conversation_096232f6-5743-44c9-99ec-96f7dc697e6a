{% extends 'base.html' %}
{% load static %}

{% block content %}
<script type="text/javascript" src="{% static 'js/jquery.min.js' %}"></script>
<script type="text/javascript" src="{% static 'js/moment.min.js' %}"></script>
<script type="text/javascript" src="{% static 'js/daterangepicker.js' %}"></script>
<link rel="stylesheet" type="text/css" href="{% static 'css/bootstrap.css' %}"/>
<link rel="stylesheet" type="text/css" href="{% static 'css/daterangepicker.css' %}"/>

<div class="jumbotron">
    <p>
</div>
<body>
<table class="table table-hover table-striped">
    <tr>
        <td width="5"> №</td>
        <td width="25%">Регион</td>
        <td width="15%">Всего БС</td>
        <td width="20%">Нефункционирующие каб.</td>
        <td width="20%">Нефункционирующие БС</td>
        <td width="15%"> % </Td>
    </TR>

    {% for item in region %}
    <tr>
        <td>{{ item.id}}</td>
<!--        <td>{{ item.name }}</td>-->
        <td><a class="dropdown-item" href="{% url 'alarmregionview' item.id %}">{{ item.name }}</a></td>
        <td>{{ item.bs_count }}</td>
        <td>{{ item.alarm_count }}</td>
        <td>{{ item.unique_bsnumber }}</td>
        <td>{{ item.bs_percentage }}</td>
<!--        <td>{{ item.appeartime|date:"Y-m-d H:m:s"}}</td>-->
    {% endfor %}
        <tr>
        <td></td>
    <td><b>Всего:</b></td>
        <td><b>{{ total_bs_count }}</b></td>
        <td><b>{{ total_alarm_count }}</b></td>
        <td><b>{{ total_unique_bsnumber }}</b></td>
        <td><b>{{ total_bs_percentage }}</b></Td>
    </b></TR>

</table>


</body>


{% endblock %}

