import datetime

from openpyxl import Workbook
from openpyxl.styles import Border, Side, Font, PatternFill
from openpyxl.utils import get_column_letter
from io import BytesIO
from openpyxl import Workbook

#from openpyxl.writer.excel import save_virtual_workbook


def reportbsAzimut(bss):
    book = Workbook()
    sheet = book.active
    sheet.append(['№', 'BSName', 'BSNumber', 'BiSecNumber', 'Lat', 'Lon',
                  'GSM-900', 'GSM-1800', 'GSMBi-1800', 'UMTS-900', 'UMTS-2100', 'BiSec',
                  'LTE-800', 'LTE-1800', 'LTEBi-1800', 'LTE-2600', 'LTE-2300', 'LTE-2100',
                  'Modem', 'Address', 'Area', 'Regions'])
    # sheet.cell(row=1, column=1, value='Hey')
    for rows in sheet.iter_rows(min_row=1, max_row=1, min_col=1, max_col=22):
        for cell in rows:
            cell.fill = PatternFill(fill_type='solid', start_color='00C0C0C0', end_color='00808080')
            cell.font = Font(bold=True)
    n = 0
    for bs in bss:
        n += 1

        area = ''
        if bs.area_id != 0:
            area = str(bs.area)
        sheet.append([n, bs.bsname, bs.bsnum, bs.besecnum, bs.lat, bs.lon, bs.gsm900,
                      bs.gsm1800, bs.gsmbi1800,  bs.umts900, bs.umts2100, bs.umtsbesec,
                      bs.lte800, bs.lte1800, bs.ltebi1800, bs.lte2600, bs.lte2300, bs.lte2100,
                      bs.modem, bs.address, area, str(bs.region)])
    set_border(sheet, 'A1:V' + str(n + 1))

    sheet.column_dimensions['A'].width = 5
    sheet.column_dimensions['B'].width = 20
    sheet.column_dimensions['C'].width = 5
    sheet.column_dimensions['D'].width = 5
    for i in range(5, 19):
        sheet.column_dimensions[get_column_letter(i)].width = 11
    sheet.column_dimensions['S'].width = 12
    sheet.column_dimensions['T'].width = 70
    sheet.column_dimensions['U'].width = 25
    sheet.column_dimensions['V'].width = 30
    return book


def reportKabinet(areaTrue, region, bss, areaAll, regionAll):
    book = Workbook()
    sheet = book.active
    if areaTrue == True:
        obl = 'Районы'
        reportname = f" Отчет по кабинетам {region}"
    else:
        obl = 'Область'
        reportname = f" Отчет по кабинетам Узбекистан"
    sheet.append(['№', obl, 'Всего БС', 'GSM-900', 'GSM-1800', 'UMTS-900', 'UMTS-2100', 'BiSec',
                  'LTE-800', 'LTE-1800', 'LTE-2600', 'LTE-2300', 'LTE-2100'])
    n = 0
    ssum = 0
    sg900 = 0
    sg1800 = 0
    su900 = 0
    su2100 = 0
    sbise = 0
    sl800 = 0
    sl1800 = 0
    sl2600 = 0
    sl2300 = 0
    sl2100 = 0
    # if areaTrue == True:
    #     vibor = areaAll
    # else:
    #     vibor = regionAll
    for ar in areaAll:
        g900 = 0
        g1800 = 0
        u900 = 0
        u2100 = 0
        bise = 0
        l800 = 0
        l1800 = 0
        l2600 = 0
        l2300 = 0
        l2100 = 0
        n += 1
        sum = 0
        for bs in bss:
            if areaTrue == True:
                idvibor = bs.area_id
            else:
                idvibor = bs.region_id
            if ar.id == idvibor:
                sum += 1
                if bs.gsm900 and bs.gsm900 != "None": g900 += 1
                if bs.gsm1800 and bs.gsm1800 != "None": g1800 += 1
                if bs.umts900 and bs.umts900 != "None": u900 += 1
                if bs.umts2100 and bs.umts2100 != "None": u2100 += 1
                if bs.umtsbesec and bs.umtsbesec != "None": bise += 1
                if bs.lte800 and bs.lte800 != "None": l800 += 1
                if bs.lte1800 and bs.lte1800 != "None": l1800 += 1
                if bs.lte2600 and bs.lte2600 != "None": l2600 += 1
                if bs.lte2300 and bs.lte2300 != "None": l2300 += 1
                if bs.lte2100 and bs.lte2100 != "None": l2100 += 1
        sheet.append([n, ar.name, sum, g900, g1800, u900, u2100, bise, l800, l1800, l2600, l2300, l2100])
        ssum += sum
        sg900 += g900
        sg1800 += g1800
        su900 += u900
        su2100 += u2100
        sbise += bise
        sl800 += l800
        sl1800 += l1800
        sl2600 += l2600
        sl2300 += l2300
        sl2100 += l2100
    sheet.append([n, 'Сумма:', ssum, sg900, sg1800, su900, su2100, sbise, sl800, sl1800, sl2600, sl2300, sl2100])

    set_border(sheet, 'A1:M' + str(n + 2))
    for rows in sheet.iter_rows(min_row=1, max_row=1, min_col=1, max_col=13):
        for cell in rows:
            cell.fill = PatternFill(fill_type='solid', start_color='00C0C0C0', end_color='00808080')
            cell.font = Font(bold=True)
    sheet.column_dimensions['A'].width = 5
    sheet.column_dimensions['B'].width = 25

    return book


def set_border(ws, cell_range):
    thin = Side(border_style="thin", color="000000")
    for row in ws[cell_range]:
        for cell in row:
            cell.border = Border(top=thin, left=thin, right=thin, bottom=thin)

def reportAlarmOml(alarms, kolalarms, regionname, startdate, enddate, alarm_type):
    dt = datetime.timedelta(days=0,hours=0,minutes=0,seconds=0)
    if alarm_type=="oml":
        diap="'GSM900 + GSM1800'"
    else:
        diap=""
    for alarm in alarms:
        dt = dt + alarm.downtime

    book = Workbook()
    sheet = book.active
    sheet.append(['','Отчет по авариям:',regionname])
    sheet.append(['','Количество каб:',kolalarms, diap])
    sheet.append(['','Сумма простоя:',dt, diap])
    sheet.append(['','Начальная дата:',str(startdate)])
    sheet.append(['','Конечняя дата:',str(enddate)])
    sheet.append(['№', 'AlarmId', 'BSC/RNC', 'Название БС', 'Номер БС','Авария', 'Время появления', 'Время устранения', 'Простой'])
    # sheet.cell(row=1, column=1, value='Hey')
    for rows in sheet.iter_rows(min_row=6, max_row=6, min_col=1, max_col=9):
        for cell in rows:
            cell.fill = PatternFill(fill_type='solid', start_color='00C0C0C0', end_color='00808080')
            cell.font = Font(bold=True)

    n = 0
    for alarm in alarms:
        n += 1
        area = ''
        sheet.append([n, alarm.alarmid, alarm.bscrnc, alarm.bsname, alarm.bsnumber,
                      alarm.alarmname, alarm.appeartime, alarm.cleartime, alarm.downtime])
    set_border(sheet, 'A6:I' + str(n + 1))
    sheet.auto_filter.ref = "A6:I6"
    sheet.column_dimensions['A'].width = 5
    sheet.column_dimensions['B'].width = 20
    sheet.column_dimensions['C'].width = 15
    sheet.column_dimensions['D'].width = 20
    sheet.column_dimensions['E'].width = 10
    sheet.column_dimensions['F'].width = 15
    sheet.column_dimensions['G'].width = 23
    sheet.column_dimensions['H'].width = 23
    sheet.column_dimensions['I'].width = 10
    return book

def reportAlarmFull(alarms, kolalarms, regionname, startdate, enddate):
    dt = datetime.timedelta(days=0,hours=0,minutes=0,seconds=0)
    for alarm in alarms:
        dt = dt + alarm.downtime

    book = Workbook()
    sheet = book.active
    sheet.append(['','Отчет по авариям:',regionname])
    sheet.append(['','Количество каб:',kolalarms])
    sheet.append(['','Сумма простоя:',dt])
    sheet.append(['','Начальная дата:',str(startdate)])
    sheet.append(['','Конечняя дата:',str(enddate)])
    sheet.append(['№', 'AlarmId', 'IdAlarm', 'BSC/RNC', 'Название БС', 'Номер БС','Cell','Авария', 'Время появления', 'Время устранения', 'Простой'])
    # sheet.cell(row=1, column=1, value='Hey')
    for rows in sheet.iter_rows(min_row=6, max_row=6, min_col=1, max_col=9):
        for cell in rows:
            cell.fill = PatternFill(fill_type='solid', start_color='00C0C0C0', end_color='00808080')
            cell.font = Font(bold=True)

    n = 0
    for alarm in alarms:
        n += 1
        area = ''
        sheet.append([n, alarm.alarmid, alarm.idalarm, alarm.bscrnc, alarm.bsname, alarm.bsnumber, alarm.cellname,
                      alarm.alarmname, alarm.appeartime, alarm.cleartime, alarm.downtime])
    set_border(sheet, 'A6:K' + str(n + 1))
    sheet.auto_filter.ref = "A6:K6"
    sheet.column_dimensions['A'].width = 5
    sheet.column_dimensions['B'].width = 15
    sheet.column_dimensions['C'].width = 10
    sheet.column_dimensions['D'].width = 20
    sheet.column_dimensions['E'].width = 25
    sheet.column_dimensions['F'].width = 10
    sheet.column_dimensions['G'].width = 12
    sheet.column_dimensions['H'].width = 25
    sheet.column_dimensions['I'].width = 23
    sheet.column_dimensions['J'].width = 23
    sheet.column_dimensions['K'].width = 15
    return book


