{% extends 'base.html' %}
{% load static %}


{% block content %}

<div class="jumbotron">
    <p>
</div>
<body>
<form action="{% url 'getgnet' %}" method="post" id="getgnetform">
    {% csrf_token %}

    <div class="container card ">
        <div class="row ">
            <div class="col col-3">
                <p>
                    <b>Выберите область:</b>
                <p>

                <div class="card-header">
                    <select class="form-select" multiple aria-label="multiple select example" name="regiono" id="reg">
                        {% for reg in region %}
                        {% if reg.id == user.region_id %}
                        <option selected value="{{ reg.id }}">{{reg.name}}</option>
                        {% else %}
                        {% if user.privilege_id == 1 or user.privilege_id == 4 %}
                        <option value="{{ reg.id }}">{{reg.name}}</option>
                        {% else %}
                        <option value="{{ reg.id }}" disabled>{{reg.name}}</option>
                        {% endif %}

                        {% endif %}
                        {% endfor %}
                    </select>

                </div>
            </div>

        </div>
        <br>
    </div>
    <br>
    <button class="btn btn-primary" type="submit">Создать Cell файл</button>
    <a href="{% url 'bsview' %}" class="btn btn-primary">Отмена</a>
</form>
<!--<a href="{% url 'getkmz' %}" class="btn btn-primary">Создать KMZ</a>-->
<!--<input class="btn btn-primary" type="button" value="Отмена" onclick="javascript:history.go(-1);">-->

</body>
{% endblock %}

