from django import forms
from .models import BsBeeline, RegionUzb, AreaUzb


class BsForm(forms.Form):

    # bsname = forms.CharField(max_length=30, label="Наименование БС", empty_value=False, widget=forms.TextInput(attrs={'class':'form-control', 'aria-label' : 'Введите имя БС' }))
    bsname = forms.CharField(max_length=30, label="Наименование БС", required=True,
                             widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Введите имя БС'}))
    bsnum = forms.CharField(max_length=6, label="Номер БС", empty_value=False,required=True,
                            widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Введите номер БС'}))
    besecnum = forms.Char<PERSON><PERSON>(max_length=6, required=False, label="Номер Бисектор",  widget=forms.TextInput(
        attrs={'class': 'form-control', 'placeholder': 'Номер Бисектора (если есть)'}))
    lat = forms.CharField(max_length=9, label="Широта", empty_value=False, required=True, widget=forms.TextInput(
        attrs={'class': 'form-control', 'placeholder': 'Введите широту(в десятичном формате)'}))
    lon = forms.CharField(max_length=9, label="Долгота", empty_value=False, required=True, widget=forms.TextInput(
        attrs={'class': 'form-control', 'placeholder': 'Введите долготу(в десятичном формате)'}))
    modem = forms.CharField(max_length=30, label="Номер модема(АСКУЭ)", required=False, widget=forms.TextInput(
        attrs={'class': 'form-control', 'placeholder': 'Номер модема АСКУЭ (если есть)'}))
    address = forms.CharField(max_length=100, label="Адрес", required=True,
                              widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Адрес БС'}))

    # area = forms.CharField(max_length=30, label="Район", empty_value=False, widget=forms.TextInput(
    #     attrs={'class': 'form-control', 'placeholder': 'Введите название района'}))
    region = forms.ModelChoiceField(queryset=RegionUzb.objects.all(), label='Область', required=True,
                                    widget=forms.Select(attrs={'class': 'form-control'}))
    area = forms.ModelChoiceField(queryset=AreaUzb.objects.all(), label='Район',required=True,
                                  widget=forms.Select(attrs={'class': 'form-control'}))

    gsm900 = forms.CharField(max_length=15, required=False, label="Азимуты GSM-900", widget=forms.TextInput(
        attrs={'class': 'form-control', 'placeholder': 'Пример (0-120-240)'}))
    gsm1800 = forms.CharField(max_length=15, required=False, label="Азимуты GSM-1800", widget=forms.TextInput(
        attrs={'class': 'form-control', 'placeholder': 'Азимуты секторов (разделитель -)'}))
    gsmbi1800 = forms.CharField(max_length=15, required=False, label="Азимуты GSMBi-1800", widget=forms.TextInput(
        attrs={'class': 'form-control', 'placeholder': 'Азимуты секторов (разделитель -)'}))
    umts900 = forms.CharField(max_length=15, required=False, label="Азимуты UMTS-900", widget=forms.TextInput(
        attrs={'class': 'form-control', 'placeholder': 'Азимуты секторов (разделитель -)'}))
    umts2100 = forms.CharField(max_length=15, required=False, label="Азимуты UMTS-2100", widget=forms.TextInput(
        attrs={'class': 'form-control', 'placeholder': 'Азимуты секторов (разделитель -)'}))
    umtsbesec = forms.CharField(max_length=15, required=False, label="Азимуты UMTS-Бисек", widget=forms.TextInput(
        attrs={'class': 'form-control', 'placeholder': 'Азимуты секторов (разделитель -)'}))
    lte800 = forms.CharField(max_length=15, required=False, label="Азимуты LTE-800", widget=forms.TextInput(
        attrs={'class': 'form-control', 'placeholder': 'Азимуты секторов (разделитель -)'}))
    lte1800 = forms.CharField(max_length=15, required=False, label="Азимуты LTE-1800", widget=forms.TextInput(
        attrs={'class': 'form-control', 'placeholder': 'Азимуты секторов (разделитель -)'}))
    ltebi1800 = forms.CharField(max_length=15, required=False, label="Азимуты LTEBi-1800", widget=forms.TextInput(
        attrs={'class': 'form-control', 'placeholder': 'Азимуты секторов (разделитель -)'}))
    lte2600 = forms.CharField(max_length=30, required=False, label="Азимуты LTE-2600", widget=forms.TextInput(
        attrs={'class': 'form-control', 'placeholder': 'Азимуты секторов (разделитель -)'}))
    lte2300 = forms.CharField(max_length=15, required=False, label="Азимуты LTE-2300", widget=forms.TextInput(
        attrs={'class': 'form-control', 'placeholder': 'Азимуты секторов (разделитель -)'}))
    lte2100 = forms.CharField(max_length=15, required=False, label="Азимуты LTE-2100", widget=forms.TextInput(
        attrs={'class': 'form-control', 'placeholder': 'Азимуты секторов (разделитель -)'}))


class UpdForm(forms.ModelForm):
    class Meta:
        model = BsBeeline
        # fields = '__all__'
        fields = ['bsname', 'bsnum', 'besecnum', 'lat', 'lon', 'modem', 'address',
                    'gsm900', 'gsm1800', 'gsmbi1800', 'umts900', 'umts2100', 'umtsbesec', 'lte800', 'lte1800', 'ltebi1800', 'lte2600',
                    'lte2300', 'lte2100','area', 'region',
                    ]
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['area'].queryset = AreaUzb.objects.none()

        if 'region' in self.data:
            try:
                region_id = int(self.data.get('region'))
                self.fields['area'].queryset = AreaUzb.objects.filter(region_id=region_id).order_by('name')
            except (ValueError, TypeError):
                pass  # invalid input from the client; ignore and fallback to empty City queryset
        elif self.instance.pk:
             self.fields['area'].queryset = AreaUzb.objects.filter(region=self.instance.region).order_by('name')
             # print(self.instance.pk)
             # print(AreaUzb.objects.filter(region=self.instance.region).order_by('name'))

    widgets = {
        'bsname': forms.TextInput(attrs={'class': 'form-control'}),
        'bsnum': forms.TextInput(attrs={'class': 'form-control'}),
        'besecnum': forms.TextInput(attrs={'class': 'form-control'}),
        'lat': forms.TextInput(attrs={'class': 'form-control'}),
        'lon': forms.TextInput(attrs={'class': 'form-control'}),
        'modem': forms.TextInput(attrs={'class': 'form-control'}),
        'address': forms.TextInput(attrs={'class': 'form-control'}),
        'area': forms.TextInput(attrs={'class': 'form-control'}),
        'region': forms.TextInput(attrs={'class': 'form-control'}),
        'gsm900': forms.TextInput(attrs={'class': 'form-control'}),
        'gsm1800': forms.TextInput(attrs={'class': 'form-control'}),
        'gsmbi1800': forms.TextInput(attrs={'class': 'form-control'}),
        'umts900': forms.TextInput(attrs={'class': 'form-control'}),
        'umts2100': forms.TextInput(attrs={'class': 'form-control'}),
        'umtsbesec': forms.TextInput(attrs={'class': 'form-control'}),
        'lte800': forms.TextInput(attrs={'class': 'form-control'}),
        'lte1800': forms.TextInput(attrs={'class': 'form-control'}),
        'ltebi1800': forms.TextInput(attrs={'class': 'form-control'}),
        'lte2600': forms.TextInput(attrs={'class': 'form-control'}),
        'lte2100': forms.TextInput(attrs={'class': 'form-control'}),
        'lte2300': forms.TextInput(attrs={'class': 'form-control'}),
    }

