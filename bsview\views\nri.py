from bsview.views.imports_file import *
from django.db import connections


@login_required
def nri_view(request):
    region = RegionUzb.objects.all()
    return render(request, 'nriview.html', {'region': region})


@login_required
def get_from_nri(request):
    file_stream = BytesIO()
    if request.method == "POST":
        band = request.POST.get('band')
        region = reg_select(int(request.POST.get('regiono')))
        print(band, region)
        ourbss = BsBeeline.objects.filter(region_id=region)
        with connections['mysql_nri'].cursor() as cursor:
            # cursor.execute("SELECT * FROM base_station_applications WHERE `region_id` = " + region + " and `application_status_id` = '5';")
            # sql_query = "SELECT t1.* FROM base_station_applications t1 WHERE `region_id` = " + region + " and `application_status_id` = '5'" \
            #             " and t1.year = (SELECT MAX(t2.year) FROM base_station_applications t2 WHERE t2.base_station_id = t1.base_station_id)"
            sql_query="SELECT t1.* FROM base_station_applications t1 WHERE t1.region_id = " + region + " AND t1.application_status_id = 5" \
                      " AND t1.year = (SELECT MAX(t2.year) FROM base_station_applications t2 WHERE t2.number = t1.number" \
                      " AND t2.region_id = " + region + " AND t2.application_status_id = 5)"
            cursor.execute(sql_query)
            table1_data = cursor.fetchall()
        all_bs_id = [row[0] for row in table1_data]  # 1-я колонка (индекс 0) [byltrc]
        all_bs_number = [row[2] for row in table1_data]  # 5-я колонка (индекс 4)
        all_bs_name = [row[7] for row in table1_data]  # 5-я колонка (индекс 4)
        all_lat = [row[9] for row in table1_data]  # 5-я колонка (индекс 4)
        all_lon = [row[10] for row in table1_data]  # 5-я колонка (индекс 4)

        with connections['mysql_nri'].cursor() as cursor:
            id_bs_str = ','.join(map(str, all_bs_id))
            # sql_query = f"SELECT * FROM base_station_antennas WHERE application_id IN ({id_bs_str})"
            sql_query = "SELECT * FROM base_station_antennas"
            cursor.execute(sql_query)
            bs_Antenna_data = cursor.fetchall()
        # Извлечение данных из 1,2-й и 7-й колонок
        app_id = [row[1] for row in bs_Antenna_data]
        antenna_id = [row[3] for row in bs_Antenna_data]
        sectors = [row[4] for row in bs_Antenna_data]
        # azimuth = [row[7] for row in bs_Antenna_data]
        height = [row[8] for row in bs_Antenna_data]
        diapasons = [row[9] for row in bs_Antenna_data]
        horizontal = [row[10] for row in bs_Antenna_data]
        vertical = [row[11] for row in bs_Antenna_data]

        with connections['mysql_nri'].cursor() as cursor:
            cursor.execute("SELECT * FROM antennas")
            Antennas = cursor.fetchall()
        anten_id = [row[0] for row in Antennas]
        anten_name = [row[2] for row in Antennas]
        anten_diapason = [row[3] for row in Antennas]
        anten_horizontal = [row[4] for row in Antennas]
        anten_vertical = [row[5] for row in Antennas]

        # for allbs in Antennas:
        #     print(allbs[2])

        # book = reportNri(all_bs_id, all_bs_name, all_bs_number, all_lat, all_lon, app_id, antenna_id, height, sectors, diapasons, horizontal, vertical, anten_id, anten_name, band)
        book = reportNri(all_bs_id, all_bs_name, all_bs_number, all_lat, all_lon, app_id, antenna_id, height, sectors, anten_diapason, anten_horizontal, anten_vertical, anten_id, anten_name, band)
        # for i in range(0,len(all_bs_id)):
        # if all_or_no == 'alluzb':
        #     bss = BsBeeline.objects.all()
        #     areaAll = RegionUzb.objects.all()
        #     areas = False
        # else:
        #     bss = BsBeeline.objects.filter(region_id=region)
        #     areaAll = AreaUzb.objects.filter(region_id=region)
        #     areas = True
        # if report == 'reportRadio1':
        #     book = reportbsAzimut(bss)
        # elif report == 'reportRadio2':
        #     book = reportKabinet(areas, region, bss, areaAll, RegionUzb.objects.all())
        book.save(file_stream)
        response = HttpResponse(content=file_stream.getvalue(), content_type='application/ms-excel')
        # # response = HttpResponse(content=save_virtual_workbook(book), content_type='application/ms-excel')
        response[
             'Content-Disposition'] = f"attachment; filename= NRI-ReportData_{band}-{dt.datetime.now().strftime('%d-%m-%Y-%H-%M-%S')}.xlsx"
        return response


def reg_select(region_number):
    if region_number == 1: region = 8
    if region_number == 2: region = 11
    if region_number == 3: region = 6
    if region_number == 4: region = 1
    if region_number == 5: region = 4
    if region_number == 6: region = 9
    if region_number == 7: region = 12
    if region_number == 8: region = 7
    if region_number == 9: region = 14
    if region_number == 10: region = 5
    if region_number == 11: region = 3
    if region_number == 12: region = 10
    if region_number == 13: region = 2
    if region_number == 14: region = 6
    if region_number == 15: region = 13
    return str(region)
