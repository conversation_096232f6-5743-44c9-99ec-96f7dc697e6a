from bsview.views.imports_file import *

@login_required
def createReport(request):
    region = RegionUzb.objects.all()
    return render(request, 'reportcreate.html', {'region': region})


@login_required
def getReport(request):
    file_stream = BytesIO()

    if request.method == "POST":

        all_or_no = request.POST.get('inlineRadioOptions')
        report = request.POST.get('reportRadioOptions')
        region = request.POST.get('regiono')
        print(all_or_no)
        if all_or_no == 'alluzb':
            bss = BsBeeline.objects.all()
            areaAll = RegionUzb.objects.all()
            areas = False
        else:
            bss = BsBeeline.objects.filter(region_id=region)
            areaAll = AreaUzb.objects.filter(region_id=region)
            areas = True
        if report == 'reportRadio1':
            book = reportbsAzimut(bss)
        elif report == 'reportRadio2':
            book = reportKabinet(areas, region, bss, areaAll, RegionUzb.objects.all())
        book.save(file_stream)
        response = HttpResponse(content=file_stream.getvalue(), content_type='application/ms-excel')
        # response = HttpResponse(content=save_virtual_workbook(book), content_type='application/ms-excel')
        response[
            'Content-Disposition'] = f'attachment; filename= ReportData-{dt.datetime.now().strftime("%d-%m-%Y-%H-%M-%S")}.xlsx'
        return response

