# Generated by Django 4.1.5 on 2023-01-17 17:08

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('bsview', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='bsbeeline',
            name='gsm1800',
            field=models.CharField(max_length=15, null=True, verbose_name='GSM-1800'),
        ),
        migrations.AlterField(
            model_name='bsbeeline',
            name='gsm900',
            field=models.Char<PERSON>ield(max_length=15, null=True, verbose_name='GSM-900'),
        ),
        migrations.AlterField(
            model_name='bsbeeline',
            name='lte1800',
            field=models.Char<PERSON>ield(max_length=15, null=True, verbose_name='LTE-1800'),
        ),
        migrations.AlterField(
            model_name='bsbeeline',
            name='lte2100',
            field=models.Char<PERSON><PERSON>(max_length=15, null=True, verbose_name='LTE-2100'),
        ),
        migrations.AlterField(
            model_name='bsbeeline',
            name='lte2300',
            field=models.CharField(max_length=15, null=True, verbose_name='LTE-2300'),
        ),
        migrations.AlterField(
            model_name='bsbeeline',
            name='lte2600',
            field=models.CharField(max_length=30, null=True, verbose_name='LTE-2600'),
        ),
        migrations.AlterField(
            model_name='bsbeeline',
            name='lte800',
            field=models.CharField(max_length=15, null=True, verbose_name='LTE-800'),
        ),
        migrations.AlterField(
            model_name='bsbeeline',
            name='region',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='bsview.regionuzb'),
        ),
        migrations.AlterField(
            model_name='bsbeeline',
            name='umts2100',
            field=models.CharField(max_length=15, null=True, verbose_name='UMTS-2100'),
        ),
        migrations.AlterField(
            model_name='bsbeeline',
            name='umts900',
            field=models.CharField(max_length=15, null=True, verbose_name='UMTS-900'),
        ),
        migrations.AlterField(
            model_name='bsbeeline',
            name='umtsbesec',
            field=models.CharField(max_length=15, null=True, verbose_name='UMTS-Бисектор'),
        ),
    ]
