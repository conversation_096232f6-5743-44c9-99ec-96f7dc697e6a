{% extends 'base.html' %}
{% load static %}
{% load crispy_forms_tags %}


{% block content %}

<div class="jumbotron">
    <p>
</div>
<body>
<form action="" method="post" id="editForm" data-areas-url="{% url 'ajax_load_areas' %}" novalidate>
    {% csrf_token %}
    <table class="table table-stripped">
        <TR class="bg-light">
            <TD>
                <div class="row">
                    <div class="col">
                        <label class="form-control bg-light" >
                        {{form.bsname | as_crispy_field }}
                        </label>
                    </div>
                    <div class="col">
                        <label class="form-control bg-light" >
                            {{form.bsnum | as_crispy_field }}
                        </label>
                    </div>
                    <div class="col">
                        <label class="form-control bg-light" >
                            {{form.besecnum | as_crispy_field }}
                        </label>
                    </div>
                </div>
            </TD>
        </TR>
        <TR class="bg-light">
            <TD>
                <div class="row">
                    <div class="col">
                        <label class="form-control bg-light"
                               aria-label="{{ form.lat.label }}">
                            {{form.lat | as_crispy_field }}
                        </label>
                    </div>
                    <div class="col">
                        <label class="form-control bg-light"
                               aria-label="{{ form.lon.label }}">
                            {{form.lon | as_crispy_field }}
                        </label>
                    </div>
                    <div class="col">
                        <label class="form-control  bg-light"
                               aria-label="{{ form.modem.label }}">
                           {{form.modem | as_crispy_field }}
                        </label>
                    </div>
                </div>
            </TD>
        </TR>
        <TR class="bg-light">
            <TD>
                <div class="row">
                    <div class="col">
                        <label class="form-control bg-light"
                               aria-label="{{ form.region.label }}">
                            {{form.region | as_crispy_field }}
                        </label>
                    </div>
                    <div class="col">
                        <label class="form-control bg-light"
                               aria-label="{{ form.area.label }}">
                           {{form.area | as_crispy_field }}
                        </label>
                    </div>
                </div>
            </TD>
        </TR>
        <TR class="bg-light">
            <TD>
                <div class="row">
                    <div class="col">
                        <label class="form-control bg-light"
                               aria-label="{{ form.address.label }}">
                           {{form.address | as_crispy_field }}
                        </label>
                    </div>
                </div>
            </TD>
        </TR>
        <TR class="bg-light">
            <TD>
                <div class="row">
                    <div class="col">
                        <label class="form-control bg-light"
                               aria-label="{{ form.gsm900.label }}">
                            {{form.gsm900 | as_crispy_field }}
                        </label>
                    </div>
                    <div class="col">
                        <label class="form-control bg-light"
                               aria-label="{{ form.gsm1800.label }}">
                           {{form.gsm1800 | as_crispy_field }}
                        </label>
                    </div>
                    <div class="col">
                        <label class="form-control bg-light"
                               aria-label="{{ form.gsmbi1800.label }}">
                           {{form.gsmbi1800 | as_crispy_field }}
                        </label>
                    </div>
                    <div class="col">
                        <label class="form-control bg-light"
                               aria-label="{{ form.umts900.label }}">
                          {{form.umts900 | as_crispy_field }}
                        </label>
                    </div>
                    <div class="col">
                        <label class="form-control bg-light"
                               aria-label="{{ form.umts2100.label }}">
                           {{form.umts2100 | as_crispy_field }}
                        </label>
                    </div>
                    <div class="col">
                        <label class="form-control bg-light"
                               aria-label="{{ form.umtsbesec.label }}">
                            {{form.umtsbesec | as_crispy_field }}
                        </label>
                    </div>
                </div>
            </TD>
        </TR>
        <TR class="bg-light">
            <TD>
                <div class="row">
                    <div class="col">
                        <label class="form-control bg-light"
                               aria-label="{{ form.lte800.label }}">
                            {{form.lte800 | as_crispy_field }}
                        </label>
                    </div>
                    <div class="col">
                        <label class="form-control bg-light"
                               aria-label="{{ form.lte1800.label }}">
                             {{form.lte1800 | as_crispy_field }}
                        </label>
                    </div>
                    <div class="col">
                        <label class="form-control bg-light"
                               aria-label="{{ form.ltebi1800.label }}">
                             {{form.ltebi1800 | as_crispy_field }}
                        </label>
                    </div>

                    <div class="col">
                        <label class="form-control bg-light"
                               aria-label="{{ form.lte2600.label }}">
                            {{form.lte2600 | as_crispy_field }}
                        </label>
                    </div>
                    <div class="col">
                        <label class="form-control bg-light"
                               aria-label="{{ form.lte2300.label }}">
                             {{form.lte2300 | as_crispy_field }}
                        </label>
                    </div>
                    <div class="col">
                        <label class="form-control bg-light"
                               aria-label="{{ form.lte2100.label }}">
                            {{form.lte2100 | as_crispy_field }}
                        </label>
                    </div>
                </div>
            </TD>
        </TR>
    </table>

    <button class="btn btn-primary" type="submit">Сохранить</button>
    <input class="btn btn-primary" type="button" value="Назад" onclick="javascript:history.go(-1);">
</form>

<script src="{% static 'js/jquery-3.3.1.min.js' %}"></script>
  <script>
    $("#id_region").change(function () {
      var url = $("#editForm").attr("data-areas-url");
      var regionId = $(this).val();

      $.ajax({
        url: url,
        data: {
          'region': regionId
        },
        success: function (data) {
          $("#id_area").html(data);
        }
      });

    });
  </script>


</body>
{% endblock %}

