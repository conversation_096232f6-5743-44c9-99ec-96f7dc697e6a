{% extends 'base.html' %}
{% load crispy_forms_tags %}
{% block title %} Map View BS Beeline {% endblock title %}

{% block content %}

<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
    integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin="" />
<link rel="stylesheet" href="/static/css/styles.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/leaflet-search/4.0.0/leaflet-search.min.css" />

<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
    integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/leaflet-search/4.0.0/leaflet-search.min.js"></script>

<style>
    /* Base styles */
    body {
        margin: 0;
        padding: 0;
        overflow: hidden;
    }

    .container-fluid {
        padding: 0;
        margin: 0;
        position: relative;
    }

    /* Loader styles */
    .loader-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.85);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        transition: opacity 0.5s, visibility 0.5s;
    }

    .loader {
        width: 120px;
        height: 120px;
        border: 5px solid transparent;
        border-top-color: #F9A825;
        border-radius: 50%;
        animation: spin 1.5s linear infinite;
        position: relative;
    }

    .loader:before,
    .loader:after {
        content: "";
        position: absolute;
        border: 5px solid transparent;
        border-radius: 50%;
    }

    .loader:before {
        top: 5px;
        left: 5px;
        right: 5px;
        bottom: 5px;
        border-top-color: #E53935;
        animation: spin 2s linear infinite;
    }

    .loader:after {
        top: 15px;
        left: 15px;
        right: 15px;
        bottom: 15px;
        border-top-color: #64B5F6;
        animation: spin 1s linear infinite;
    }

    .loader-text {
        margin-top: 30px;
        font-family: 'Arial', sans-serif;
        font-size: 28px;
        color: white;
        letter-spacing: 3px;
        position: relative;
    }

    .loader-text span {
        display: inline-block;
        opacity: 0;
        animation: fadeIn 1s ease-in-out infinite;
    }

    .loader-text span:nth-child(n+1) {
        animation-delay: calc(0.05s * var(--n));
    }

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }

        100% {
            transform: rotate(360deg);
        }
    }

    @keyframes fadeIn {

        0%,
        100% {
            opacity: 0;
            transform: translateY(5px);
        }

        50% {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Map container */
    #mapid {
        height: 100vh;
        width: calc(100% - 300px);
        margin: 0;
        padding: 0;
        position: fixed;
        top: 0;
        left: 300px;
        right: 0;
        bottom: 0;
        z-index: 95;
    }

    /* Navbar */
    .navbar {
        margin-bottom: 0 !important;
        z-index: 2000;
        position: relative;
    }

    /* Leaflet controls */
    .leaflet-control-container .leaflet-top {
        top: 80px;
    }

    .leaflet-control-container .leaflet-right {
        right: 10px;
    }

    .leaflet-control-zoom {
        margin-right: 10px;
    }

    .leaflet-control-attribution {
        display: none !important;
    }

    /* Sidebar */
    .sidebar {
        width: 300px;
        max-width: 300px;
        height: calc(100vh - 70px);
        background-color: white;
        box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
        display: flex;
        flex-direction: column;
        overflow: hidden;
        position: fixed;
        top: 70px;
        left: 0;
        z-index: 1000;
        border-radius: 0;
    }

    .sidebar-header {
        padding: 12px;
        background-color: #2c3e50;
        color: white;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .sidebar-header h2 {
        margin: 0;
        font-size: 16px;
        font-weight: 500;
    }

    .sidebar-content {
        padding: 0 8px 8px;
        overflow-y: auto;
        flex: 1;
        position: relative;
    }

    /* Filter styles */
    .filter-group {
        margin-bottom: 6px;
    }

    .filter-group label {
        display: block;
        margin-bottom: 1px;
        font-weight: 500;
        font-size: 10px;
        color: #555;
    }

    .filter-group select,
    .filter-group input,
    #bs-search,
    #reset-filters {
        width: 100%;
        padding: 4px 6px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 11px;
        height: 28px;
    }

    .search-reset-container {
        display: flex;
        gap: 5px;
        margin-bottom: 6px;
        align-items: flex-end;
    }

    .search-reset-container .search-input-container {
        flex: 1;
    }

    .search-reset-container .reset-button-container {
        width: auto;
    }

    #reset-filters {
        white-space: nowrap;
        background-color: #dc3545;
        border-color: #dc3545;
        color: white;
        margin-bottom: 0;
    }

    #reset-filters:hover {
        background-color: #c82333;
        border-color: #bd2130;
    }

    /* Stats styles */
    .stats-container {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;
        background-color: #f5f5f5;
        border-radius: 5px;
        padding: 10px;
    }

    .stat-item {
        text-align: center;
        flex: 1;
    }

    .stat-value {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 5px;
    }

    .stat-label {
        font-size: 12px;
        color: #666;
    }

    .stat-item.online .stat-value {
        color: #4caf50;
    }

    .stat-item.offline .stat-value {
        color: #f44336;
    }

    /* Downtime legend */
    .downtime-legend {
        position: absolute;
        bottom: 20px;
        left: 310px;
        z-index: 1000;
        background-color: rgba(255, 255, 255, 0.7);
        padding: 8px;
        border-radius: 6px;
        box-shadow: 0 1px 5px rgba(0, 0, 0, 0.2);
        max-width: 240px;
        font-size: 11px;
    }

    .downtime-legend h5 {
        margin: 0 0 6px 0;
        font-size: 12px;
        font-weight: bold;
        text-align: center;
    }

    .downtime-item {
        display: flex;
        align-items: center;
        margin-bottom: 3px;
        cursor: pointer;
        padding: 2px;
        border-radius: 3px;
        transition: background-color 0.2s;
    }

    .downtime-item:hover {
        background-color: rgba(0, 0, 0, 0.05);
    }

    .downtime-item.active {
        background-color: rgba(0, 0, 0, 0.1);
    }

    .color-box {
        width: 14px;
        height: 14px;
        border-radius: 50%;
        margin-right: 6px;
        border: 1px solid rgba(0, 0, 0, 0.2);
    }

    .downtime-label {
        flex: 1;
    }

    .downtime-count {
        font-weight: bold;
        margin-left: 5px;
    }

    .show-all-item {
        border-top: 1px solid #ddd;
        padding-top: 5px;
        margin-top: 5px;
    }

    /* Map controls */
    .map-type-control {
        background-color: white;
        border-radius: 4px;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
        padding: 3px;
        position: absolute;
        top: 80px;
        right: 10px;
        z-index: 1000;
    }

    .map-controls-row {
        display: flex;
        flex-direction: row;
        gap: 3px;
    }

    .map-source-buttons,
    .map-type-buttons {
        display: flex;
    }

    .map-type-control button {
        background-color: white;
        border: none;
        padding: 6px 6px;
        cursor: pointer;
        font-size: 14px;
        outline: none;
        transition: all 0.2s ease;
        color: #555;
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .map-source-logo {
        width: 24px;
        height: 24px;
        object-fit: contain;
    }

    .map-source-buttons button:first-child,
    .map-type-buttons button:first-child {
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;
    }

    .map-source-buttons button:last-child,
    .map-type-buttons button:last-child {
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
    }

    .map-type-control button.active {
        background-color: #4285F4;
        color: white;
        font-weight: 500;
    }

    .map-type-control button:hover:not(.active) {
        background-color: #f4f4f4;
    }

    /* Search styles */
    .search-container {
        display: flex;
        margin-bottom: 3px;
        width: 100%;
    }

    .search-input {
        flex-grow: 1;
        padding: 6px 8px;
        border: 1px solid #ddd;
        border-radius: 4px 0 0 4px;
        font-size: 13px;
        height: 36px;
        box-sizing: border-box;
    }

    .search-button {
        background-color: white;
        border: 1px solid #ddd;
        border-left: none;
        border-radius: 0 4px 4px 0;
        padding: 6px 10px;
        cursor: pointer;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
    }

    .search-button:hover {
        background-color: #f4f4f4;
    }

    /* Region stats */
    .regions-stats {
        margin-top: 0;
        border-top: none;
        padding-top: 0;
    }

    .regions-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        margin-top: 0;
        font-size: 12px;
    }

    .regions-table thead {
        position: sticky;
        top: 0;
        z-index: 10;
        background-color: white;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .regions-table th {
        background-color: #f3f3f3;
        padding: 6px 5px;
        text-align: center;
        font-weight: 600;
        border-bottom: 2px solid #ddd;
        font-size: 11px;
    }

    .regions-table td {
        padding: 5px 4px;
        text-align: center;
        border-bottom: 1px solid #eee;
        font-size: 11px;
    }

    .regions-table tr:hover {
        background-color: #f5f5f5;
        cursor: pointer;
    }

    .regions-table .region-name-cell {
        text-align: left;
        font-weight: 500;
    }

    .regions-table tr.active {
        background-color: #e3f2fd;
    }

    .total-stat {
        color: #333;
    }

    .online-stat {
        color: #4caf50;
    }

    .offline-stat {
        color: #f44336;
    }

    /* BS name tooltip */
    .bs-name-tooltip {
        background: transparent;
        border: none;
        box-shadow: none;
        color: #000000;
        font-weight: bold;
        font-size: 10px;
        padding: 0;
        text-shadow: 1px 1px 1px rgba(255, 255, 255, 0.7);
        white-space: nowrap;
        border-radius: 0;
        transition: font-size 0.2s;
    }

    /* Make BS search label same style as Area label */
    #bs-search-label,
    #area-label {
        font-size: 13px;
        font-weight: 500;
        margin-bottom: 4px;
        display: inline-block;
    }

    .zoom-level-low .bs-name-tooltip {
        font-size: 8px;
    }

    .zoom-level-medium .bs-name-tooltip {
        font-size: 10px;
    }

    .zoom-level-high .bs-name-tooltip {
        font-size: 12px;
    }

    .leaflet-tooltip.bs-name-tooltip {
        background: transparent;
        border: none;
        box-shadow: none;
    }

    .leaflet-tooltip.bs-name-tooltip:before {
        display: none;
    }

    /* Copyright */
    .custom-copyright {
        position: absolute;
        bottom: 5px;
        right: 10px;
        background-color: rgba(255, 255, 255, 0.7);
        padding: 3px 8px;
        border-radius: 3px;
        font-size: 11px;
        color: #333;
        z-index: 1000;
        font-weight: 500;
    }

    /* Popup styles */
    .bs-hover-popup {
        background-color: white;
        border-radius: 4px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
        padding: 5px 8px;
        font-size: 12px;
        border: none;
    }

    .bs-hover-popup .leaflet-popup-content-wrapper {
        background-color: white;
        border-radius: 4px;
        padding: 0;
    }

    .bs-hover-popup .leaflet-popup-content {
        margin: 0;
        line-height: 1.5;
    }

    .bs-hover-popup .leaflet-popup-tip {
        background-color: white;
    }

    /* Language selector */
    .language-selector {
        display: flex;
    }

    .lang-btn {
        background-color: transparent;
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.3);
        margin-left: 5px;
        padding: 2px 6px;
        font-size: 11px;
        cursor: pointer;
        transition: all 0.2s;
    }

    .lang-btn:hover {
        background-color: rgba(255, 255, 255, 0.2);
    }

    .lang-btn.active {
        background-color: #f39c12;
        border-color: #f39c12;
        color: #2c3e50;
        font-weight: bold;
    }

    /* Utility spacing */
    .sidebar-content>div:nth-child(2) {
        margin-top: 3px;
    }

    .regions-table+div {
        margin-top: 3px;
    }
</style>
</head>

<body>
    <!-- Loader -->
    <div class="loader-container" id="loader">
        <div class="loader"></div>
        <div class="loader-text">
            <span>Q</span>
            <span>u</span>
            <span>a</span>
            <span>l</span>
            <span>i</span>
            <span>t</span>
            <span>y</span>
            <span>&nbsp;</span>
            <span>t</span>
            <span>e</span>
            <span>a</span>
            <span>m</span>
        </div>
    </div>

    <div class="container-fluid">
        <div class="sidebar">
            <div class="sidebar-header">
                <h2 id="stats-header">Статистика</h2>
                <div class="language-selector">
                    <button id="lang-ru" class="lang-btn active">RU</button>
                    <button id="lang-en" class="lang-btn">EN</button>
                </div>
            </div>
            <div class="sidebar-content">
                <!-- Вилоятлар статистикаси -->
                <div class="regions-stats">
                    <table class="regions-table">
                        <thead>
                            <tr>
                                <th id="region-header">Регион</th>
                                <th>All</th>
                                <th>On</th>
                                <th>Off</th>
                                <th>%</th>
                            </tr>
                        </thead>
                        <tbody id="regions-stats-container">
                            <!-- Вилоятлар статистикаси динамик равишда қўшилади -->
                        </tbody>
                    </table>
                </div>

                <!-- Ажратиш чизиғи -->
                <div style="border-top: 1px solid #ddd; margin: 3px 0;"></div>

                <!-- Қолган фильтрлар -->
                <div class="filter-group">
                    <label for="area" id="area-label">Район:</label>
                    <select id="area" disabled>
                        <option value="" id="all-areas">Все районы</option>
                        <!-- Районы будут добавлены динамически -->
                    </select>
                </div>

                <!-- Поиск и сброс фильтров в одну строку -->
                <div class="search-reset-container">
                    <!-- Поиск по имени или номеру БС -->
                    <div class="search-input-container">
                        <label for="bs-search" id="bs-search-label">Поиск:</label>
                        <input type="text" id="bs-search" class="form-control" placeholder="Имя или номер БС">
                    </div>

                    <!-- Кнопка сброса фильтров -->
                    <div class="reset-button-container">
                        <button id="reset-filters" class="btn btn-sm">Сброс</button>
                    </div>
                </div>

                <!-- Яширин селектлар (логика учун) -->
                <div style="display: none;">
                    <select id="region">
                        <option value="" id="all-regions">Все регионы</option>
                        <!-- Регионы будут добавлены динамически -->
                    </select>
                    <select id="status">
                        <option value="all" id="status-all">Все</option>
                        <option value="online" id="status-online">Онлайн</option>
                        <option value="offline" id="status-offline">Оффлайн</option>
                    </select>
                    <!-- Яширин статистика элементлари -->
                    <div id="total-bs-count">0</div>
                    <div id="active-bs-count">0</div>
                    <div id="inactive-bs-count">0</div>
                </div>

                <!-- Карта режими тугмалари сайдбардан олиб ташланди -->
            </div>
        </div>
    </div>

    <div id="mapid"></div>

    <!-- Авария давомийлиги легендаси -->
    <div class="downtime-legend" id="downtime-legend">
        <h5 id="downtime-legend-header">Авария давомийлиги</h5>
        <div class="downtime-list">
            <div class="downtime-item" data-duration="1">
                <div class="color-box" style="background-color: #3388ff;"></div>
                <div class="downtime-label" id="downtime-label-1">1 соатгача</div>
                <div class="downtime-count" id="downtime-count-1">0</div>
            </div>
            <div class="downtime-item" data-duration="2">
                <div class="color-box" style="background-color: #ffcc00;"></div>
                <div class="downtime-label" id="downtime-label-2">2 соатгача</div>
                <div class="downtime-count" id="downtime-count-2">0</div>
            </div>
            <div class="downtime-item" data-duration="3">
                <div class="color-box" style="background-color: #a52a2a;"></div>
                <div class="downtime-label" id="downtime-label-3">3 соатгача</div>
                <div class="downtime-count" id="downtime-count-3">0</div>
            </div>
            <div class="downtime-item" data-duration="4">
                <div class="color-box" style="background-color: #ff0000;"></div>
                <div class="downtime-label" id="downtime-label-4">4 соатгача</div>
                <div class="downtime-count" id="downtime-count-4">0</div>
            </div>
            <div class="downtime-item" data-duration="5">
                <div class="color-box" style="background-color: #000000;"></div>
                <div class="downtime-label" id="downtime-label-5">4 соатдан ортиқ</div>
                <div class="downtime-count" id="downtime-count-5">0</div>
            </div>
            <div class="downtime-item show-all-item" data-duration="all">
                <div class="color-box"
                    style="background: linear-gradient(45deg, #3388ff, #ffcc00, #a52a2a, #ff0000, #000000);"></div>
                <div class="downtime-label" id="downtime-label-all">Барчаси</div>
                <div class="downtime-count" id="downtime-count-all">0</div>
            </div>
        </div>
    </div>

    <!-- Копирайт элементи -->
    <div class="custom-copyright">© 2025 Quality team</div>

    <!-- Карта манбаси ва режими бошқаруви -->
    <div class="map-type-control">
        <!-- Qidiruv elementi -->
        <div class="search-container">
            <input type="text" id="location-search" class="search-input" placeholder="Search location">
            <button id="search-button" class="search-button" title="Search"><i class="fas fa-search"></i></button>
        </div>
        <div class="map-controls-row">
            <!-- Карта манбаси -->
            <div class="map-source-buttons">
                <button id="osm-source-button" class="active" title="OpenStreetMap"><img
                        src="https://upload.wikimedia.org/wikipedia/commons/thumb/b/b0/Openstreetmap_logo.svg/256px-Openstreetmap_logo.svg.png"
                        alt="OpenStreetMap" class="map-source-logo"></button>
                <button id="yandex-source-button" title="Яндекс Карты"><img
                        src="https://upload.wikimedia.org/wikipedia/commons/thumb/5/58/Yandex_icon.svg/200px-Yandex_icon.svg.png"
                        alt="Яндекс Карты" class="map-source-logo"></button>
            </div>
            <!-- Карта режими -->
            <div class="map-type-buttons">
                <button id="map-button" class="active" title="Карта"><i class="fas fa-map"></i></button>
                <button id="satellite-button" title="Спутник"><i class="fas fa-satellite"></i></button>
            </div>
        </div>
    </div>

    {{ points_data_json|json_script:"points-data" }}

    <script>
        // Til ma'lumotlari
        const translations = {
            'ru': {
                'stats_header': 'Статистика',
                'region_header': 'Регион',
                'area_label': 'Район:',
                'bs_search_label': 'Поиск БС:',
                'bs_search_placeholder': 'Введите название или номер БС',
                'reset_filters': 'Сбросить фильтры',
                'all_regions': 'Все регионы',
                'all_areas': 'Все районы',
                'status_all': 'Все',
                'status_online': 'Онлайн',
                'status_offline': 'Оффлайн',
                'map_title': 'Карта',
                'satellite_title': 'Спутник',
                'bs_active': 'БС активен',
                'bs_inactive': 'БС не работает',
                'osm_title': 'OpenStreetMap',
                'yandex_title': 'Яндекс Карты',
                'search_location': 'Поиск местоположения',
                'search_button': 'Поиск',
                'location_not_found': 'Местоположение не найдено',
                'search_error': 'Ошибка при поиске',
                'region_label': 'Регион',
                'area_label': 'Район',
                'technology_label': 'Технология',
                'downtime_label': 'Авария',
                'downtime_legend_header': 'Продолжительность аварии',
                'downtime_1h': 'До 1 часа',
                'downtime_2h': 'До 2 часов',
                'downtime_3h': 'До 3 часов',
                'downtime_4h': 'До 4 часов',
                'downtime_more_4h': 'Более 4 часов',
                'downtime_all': 'Все'
            },
            'en': {
                'stats_header': 'Statistics',
                'region_header': 'Region',
                'area_label': 'Area:',
                'bs_search_label': 'Search BS:',
                'bs_search_placeholder': 'Enter BS name or number',
                'reset_filters': 'Reset filters',
                'all_regions': 'All regions',
                'all_areas': 'All areas',
                'status_all': 'All',
                'status_online': 'Online',
                'status_offline': 'Offline',
                'map_title': 'Map',
                'satellite_title': 'Satellite',
                'bs_active': 'BS is active',
                'bs_inactive': 'BS is not working',
                'osm_title': 'OpenStreetMap',
                'yandex_title': 'Yandex Maps',
                'search_location': 'Search location',
                'search_button': 'Search',
                'location_not_found': 'Location not found',
                'search_error': 'Error during search',
                'region_label': 'Region',
                'area_label': 'Area',
                'technology_label': 'Technology',
                'downtime_label': 'Downtime',
                'downtime_legend_header': 'Downtime Duration',
                'downtime_1h': 'Up to 1 hour',
                'downtime_2h': 'Up to 2 hours',
                'downtime_3h': 'Up to 3 hours',
                'downtime_4h': 'Up to 4 hours',
                'downtime_more_4h': 'More than 4 hours',
                'downtime_all': 'All'
            }
        };

        // Регион ва туман номларини таржима қилиш учун маълумотлар базаси
        const regionTranslations = {
            'город Ташкент': 'Tashkent city',
            'Ташкентская область': 'Tashkent region',
            'Андижанская область': 'Andijan region',
            'Бухарская область': 'Bukhara region',
            'город Коканд': 'Kokand city',
            'Джиззахская область': 'Jizzakh region',
            'Кашкадарьинская область': 'Kashkadarya region',
            'Наваийская область': 'Navoi region',
            'Наманганская область': 'Namangan region',
            'Рес.Каракалпакстан': 'Republic of Karakalpakstan',
            'Самаркандская область': 'Samarkand region',
            'Сурхандарьинская область': 'Surkhandarya region',
            'Сырдарьинская область': 'Syrdarya region',
            'Ферганская область': 'Fergana region',
            'Хорезмская область': 'Khorezm region',
            'Все регионы': 'All regions'
        };

        // Туман ва шаҳар номларини таржима қилиш учун қоидалар
        const areaTranslations = {};

        // Маълумотларни тўлдириш учун қоидалар
        // Қорақалпоғистон туманлари
        ['город Нукус', 'Амударьинский район', 'Берунийский район', 'Канлыкульский район',
            'Караузякский район', 'Кегейлийский район', 'Кунградский район', 'Муйнакский район',
            'Нукусский район', 'Тахиаташский район', 'Тахтакупырский район', 'Турткульский район',
            'Ходжейлийский район', 'Чимбайский район', 'Шуманайский район', 'Элликкалинский район',
            'Бозатауский район'].forEach((name, index) => {
                const englishNames = [
                    'Nukus city', 'Amudaryo district', 'Beruniy district', 'Qanlikol district',
                    'Qoraozak district', 'Kegeyli district', 'Qongirot district', 'Muynoq district',
                    'Nukus district', 'Taxiatosh district', 'Taxtakopir district', 'Tortkol district',
                    'Xojayli district', 'Chimboy district', 'Shumanay district', 'Ellikqala district',
                    'Bozatov district'
                ];
                areaTranslations[name] = englishNames[index];

                // Қўшимчасиз номлар учун ҳам добавляем
                if (name.includes('район')) {
                    const shortName = name.replace(' район', '');
                    areaTranslations[shortName] = englishNames[index];
                }
            });

        // Барча шаҳарларни таржима қилиш учун қоидалар
        const cities = [
            'Ташкент', 'Бекабад', 'Чирчик', 'Самарканд', 'Каттакурган', 'Бухара', 'Каган',
            'Андижан', 'Фергана', 'Коканд', 'Маргилан', 'Кувасай', 'Наманган', 'Джизак',
            'Карши', 'Навои', 'Зарафшан', 'Гулистан', 'Ширин', 'Янгиер', 'Термез', 'Ургенч',
            'Хива', 'Алмалык', 'Ангрен', 'Асака', 'Денау', 'Акташ', 'Шахрисабз', 'Китаб',
            'Чуст', 'Риштан', 'Янгиюль', 'Нукус'
        ];

        // Шаҳарларни таржима қоидаларини яратиш
        cities.forEach(city => {
            let translatedName = city;
            // Башқа шаҳар номларини transliterate қилишни қўллаш
            const transliterationMap = {
                'Ташкент': 'Tashkent', 'Бекабад': 'Bekabad', 'Чирчик': 'Chirchik',
                'Самарканд': 'Samarkand', 'Каттакурган': 'Kattakurgan', 'Бухара': 'Bukhara',
                'Каган': 'Kagan', 'Андижан': 'Andijan', 'Фергана': 'Fergana',
                'Коканд': 'Kokand', 'Маргилан': 'Marg\'ilon', 'Кувасай': 'Quvasoy',
                'Наманган': 'Namangan', 'Джизак': 'Jizzakh', 'Карши': 'Qarshi',
                'Навои': 'Navoi', 'Зарафшан': 'Zarafshon', 'Гулистан': 'Guliston',
                'Ширин': 'Shirin', 'Янгиер': 'Yangiyer', 'Термез': 'Termez',
                'Ургенч': 'Urgench', 'Хива': 'Xiva', 'Алмалык': 'Olmaliq',
                'Ангрен': 'Angren', 'Асака': 'Asaka', 'Денау': 'Denov',
                'Акташ': 'Oqtosh', 'Шахрисабз': 'Shahrisabz', 'Китаб': 'Kitob',
                'Чуст': 'Chust', 'Риштан': 'Rishton', 'Янгиюль': 'Yangiyul',
                'Нукус': 'Nukus'
            };

            translatedName = transliterationMap[city] || city;

            // "город" префикси билан
            areaTranslations[`город ${city}`] = `${translatedName} city`;

            // "г." префикси билан
            areaTranslations[`г. ${city}`] = `${translatedName} city`;
        });

        // "Все районы" учун таржима
        areaTranslations['Все районы'] = 'All areas';

        // Жорий тил, автоматик равишда сақланган қийматни ёки рус тилини танлаш
        let currentLanguage = localStorage.getItem('mapLanguage') || 'ru';

        // Глобал давомийлик фильтри
        let currentDurationFilter = 'all';

        // Давомийлик бўйича БС статистикаси
        let durationStats = {
            '1': 0, // 1 соатгача
            '2': 0, // 2 соатгача
            '3': 0, // 3 соатгача
            '4': 0, // 4 соатгача
            '5': 0  // 4 соатдан кўп
        };

        // Координаталар учун регион марказлари
        const regionCenters = {
            1: [41.311081, 69.240562], // Тошкент шаҳри
            2: [40.783555, 72.350891], // Андижон
            3: [40.384240, 71.785690], // Фарғона
            4: [41.001071, 71.672278], // Наманган
            5: [39.768083, 64.421710], // Бухоро
            6: [40.121462, 67.842194], // Жиззах
            7: [38.839802, 65.781462], // Қашқадарё
            8: [40.103922, 65.374260], // Навоий
            9: [41.248990, 69.333240], // Тошкент вилояти
            10: [40.837641, 68.661338], // Сирдарё
            11: [37.940552, 67.510929], // Сурхондарё
            12: [41.552080, 60.631622], // Хоразм
            13: [43.804363, 59.018464], // Қорақалпоғистон
            14: [39.654388, 66.975824]  // Самарқанд
        };

        // Умумий статистика қийматлари
        let globalTotalBsCount = 0;
        let globalActiveBsCount = 0;
        let globalInactiveBsCount = 0;

        // Ҳар бир вилоят учун статистика тайёрлаш
        let regionsStatsData = {};

        // Loader учун скрипт
        document.addEventListener('DOMContentLoaded', function () {
            // Барча ҳарфлар дастлабда кўриниши учун
            setTimeout(function () {
                const loaderTextSpans = document.querySelectorAll('.loader-text span');
                loaderTextSpans.forEach(span => {
                    span.style.opacity = '1';
                    span.style.animation = 'none';
                });
            }, 800);

            // Барча манбалар юкланишини кутиш
            window.addEventListener('load', function () {
                setTimeout(function () {
                    const loader = document.getElementById('loader');
                    loader.style.opacity = '0';
                    loader.style.visibility = 'hidden';
                }, 800); // 0.8 секунддан сўнг лоадерни яшириш
            });
        });

        // UTILITY FUNCTIONS - МАЪЛУМОТЛАР БИЛАН ИШЛАШ ФУНКЦИЯЛАРИ

        // БС статусларини глобал маълумотлар билан синхронлаштириш функцияси
        function syncStationsWithGlobalStatus(stations, points) {
            const stationStatusMap = {};
            points.forEach(point => {
                stationStatusMap[point.bsName || point.name] = point.status;
            });

            return stations.map(station => {
                const stationName = station.bsName || station.name;
                const statusFromGlobal = stationStatusMap[stationName];
                return {
                    ...station,
                    status: statusFromGlobal !== undefined ? statusFromGlobal : (station.status === 'offline')
                };
            });
        }

        // Станциялар координаталари бўйича марказни ҳисоблаш
        function calculateCenter(stations) {
            if (!stations || stations.length === 0) return null;

            let totalLat = 0;
            let totalLon = 0;
            let validStations = 0;

            stations.forEach(station => {
                if (station.lat && station.lon && !isNaN(parseFloat(station.lat)) && !isNaN(parseFloat(station.lon))) {
                    totalLat += parseFloat(station.lat);
                    totalLon += parseFloat(station.lon);
                    validStations++;
                }
            });

            if (validStations === 0) return null;

            return {
                lat: totalLat / validStations,
                lon: totalLon / validStations
            };
        }

        // DOWNTIME DURATION FUNCTIONS - АВАРИЯ ДАВОМИЙЛИГИ БИЛАН ИШЛАШ ФУНКЦИЯЛАРИ

        // Аварияларни давомийлиги бўйича гуруҳлаш
        function getDowntimeDuration(point) {
            if (!point.calcTime || !point.status) return 0;

            // calcTime форматини текшириш ва соатларни олиш
            let hours = 0;
            let parsed = false; // Ҳеч бўлмаганда бирор форматни аниқлаганимизни билдирувчи флаг
            const calcTime = String(point.calcTime).toLowerCase();

            // Соатларни аниқлаш
            if (calcTime.includes('hour') || calcTime.includes('час') || calcTime.includes('соат')) {
                const match = calcTime.match(/(\d+)[\s]*(hour|hours|час|часа|часов|соат|ч|h)/);
                if (match) {
                    hours = parseInt(match[1]);
                    parsed = true;
                }
            }
            // Кунларни аниқлаш
            else if (calcTime.includes('day') || calcTime.includes('days') || calcTime.includes('день') ||
                calcTime.includes('дня') || calcTime.includes('дней') || calcTime.includes('кун')) {
                const match = calcTime.match(/(\d+)[\s]*(day|days|день|дня|дней|кун|d)/);
                if (match) {
                    hours = parseInt(match[1]) * 24; // кунларни соатларга ўтказиш
                    parsed = true;
                }
            }
            // Минутларни аниқлаш
            else if (calcTime.includes('minute') || calcTime.includes('minutes') || calcTime.includes('минут') ||
                calcTime.includes('мин')) {
                const match = calcTime.match(/(\d+)[\s]*(minute|minutes|минут|минута|минуты|мин|m)/);
                if (match) {
                    hours = parseInt(match[1]) / 60; // минутларни соатларга ўтказиш
                    parsed = true;
                }
            }

            // Агар ҳеч қайси формат аниқланмаган бўлса
            if (!parsed) {
                // Рақамларни излаш - агар нечадир рақамлар кетма-кет келса
                const numberMatch = calcTime.match(/(\d+)/);
                if (numberMatch) {
                    const num = parseInt(numberMatch[1]);

                    // Форматга қараб вақтни талқин қилиш
                    if (num > 1000) {
                        hours = num / (1000 * 60 * 60); // миллисекундлардан соатларга
                    } else if (num > 100 && num < 1000) {
                        hours = num / 60; // минутлардан соатларга
                    } else if (num <= 24) {
                        hours = num; // соатлар
                    } else {
                        hours = num; // ноаниқ, соат деб қабул қиламиз
                    }
                } else {
                    hours = 1; // стандарт 1 соат
                }
            }

            // Соатлар жуда кичик бўлса, минимал қиймат белгилаш
            if (hours <= 0.01) {
                hours = 0.1; // 6 минут
            }

            // Давомийлик гуруҳини қайтариш
            if (hours <= 1) return 1;         // 1 соатгача
            else if (hours <= 2) return 2;    // 1-2 соат
            else if (hours <= 3) return 3;    // 2-3 соат
            else if (hours <= 4) return 4;    // 3-4 соат
            else return 5;                    // 4 соатдан кўп
        }

        // Давомийлик гуруҳи бўйича ранг қайтариш
        function getDowntimeColor(durationGroup) {
            // тўғри рақамга айлантириш учун
            const group = parseInt(durationGroup) || 0;

            switch (group) {
                case 1: return '#3388ff'; // 1 соатгача кўк
                case 2: return '#ffcc00'; // 2 соатгача сариқ
                case 3: return '#a52a2a'; // 3 соатгача жигарранг
                case 4: return '#ff0000'; // 4 соатгача қизил
                case 5: return '#000000'; // 4 соатдан кўп қора
                default: return 'green';  // Соғлом (авария эмас)
            }
        }

        // Давомийлик статистикасини ҳисоблаш функцияси
        function calculateDurationStats(points) {
            // Статистикани нолга тиклаш
            durationStats = {
                '1': 0, '2': 0, '3': 0, '4': 0, '5': 0
            };

            // Фақат авария ҳолатидаги БС ларни фильтрлаш
            const offlinePoints = points.filter(point => point.status === true);

            // Хар бир авария ҳолатидаги БС ни давомийлиги бўйича гуруҳлаш
            offlinePoints.forEach(point => {
                const durationGroup = getDowntimeDuration(point);
                if (durationGroup > 0) {
                    durationStats[durationGroup]++;
                }
            });

            // Статистикани кўрсатиш
            updateDurationStats();

            return durationStats;
        }

        // Легенда статистикасини янгилаш функцияси
        function updateDurationStats() {
            // Ҳар бир давомийлик гуруҳи учун статистикани кўрсатиш
            for (let i = 1; i <= 5; i++) {
                document.getElementById('downtime-count-' + i).textContent = durationStats[i];
            }

            // Умумий аварияли БС сони
            const totalOffline = Object.values(durationStats).reduce((acc, val) => acc + val, 0);
            document.getElementById('downtime-count-all').textContent = totalOffline;

            // Активлик ҳолатини қўллаш
            const durationItems = document.querySelectorAll('.downtime-item');
            durationItems.forEach(item => {
                const duration = item.getAttribute('data-duration');
                item.classList.toggle('active', duration === currentDurationFilter);
            });
        }

        // MARKER FUNCTIONS - МАРКЕРЛАР БИЛАН ИШЛАШ ФУНКЦИЯЛАРИ

        // Функция для определения размера круга в зависимости от уровня зума
        function getCircleRadius(zoom) {
            if (zoom < 6) return 2;
            else if (zoom < 10) return 4;
            else return 10;
        }

        // БС маркерини яратиш функцияси
        function createBSMarker(point, statusValue) {
            // Координаталар тўғри эканини текшириш
            if (!point.lat || !point.lon || isNaN(parseFloat(point.lat)) || isNaN(parseFloat(point.lon))) {
                return null;
            }

            const pointName = point.bsName || point.name;

            // Авария ҳолати ва давомийлиги
            let pointColor = 'green';
            let durationGroup = 0;

            // Статус фильтрини текшириш - бу энг устувор фильтр
            if (point.status === true) { // БС аварияда
                if (statusValue === 'online') {
                    return null; // Фақат онлайн БС лар кўрсатилиши керак бўлса, аварияли БС ни кўрсатмаслик
                }
                // Авария ҳолатидаги БС учун давомийлик гуруҳини аниқлаш
                durationGroup = getDowntimeDuration(point);
                pointColor = getDowntimeColor(durationGroup);
            } else { // БС соғлом (онлайн)
                if (statusValue === 'offline') {
                    return null; // Фақат офлайн БС лар кўрсатилиши керак бўлса, соғлом БС ни кўрсатмаслик
                }
                // Онлайн БС учун яшил ранг
                pointColor = 'green';
            }

            // Давомийлик фильтрини текшириш - фақат давомийлик фильтри танланган бўлса
            if (currentDurationFilter !== 'all') {
                if (point.status === true) { // Фақат аварияли БС лар учун
                    if (parseInt(currentDurationFilter) !== durationGroup) {
                        return null; // Агар БС давомийлик гуруҳи танланган гуруҳга мос келмаса, кўрсатмаслик
                    }
                } else {
                    return null; // Агар давомийлик фильтри танланган бўлса, соғлом БС ларни кўрсатмаслик
                }
            }

            // Определяем, нужно ли показывать постоянную подпись (только для аварийных БС)
            const permanentTooltip = statusValue === 'offline' && point.status === true;

            const circleMarker = L.circleMarker([point.lat, point.lon], {
                radius: getCircleRadius(mymap.getZoom()),
                fillColor: pointColor,
                color: "#000",
                weight: 1,
                opacity: 1,
                fillOpacity: 0.8,
                pointName: pointName, // Сохраняем имя точки в опциях маркера
                durationGroup: durationGroup // Сохраняем группу длительности аварии
            });

            // Добавляем hover tooltip для аварийных БС с дополнительной информацией
            if (point.status === true) {
                // Создаем маркер с hover tooltip
                circleMarker.on('mouseover', function (e) {
                    // Подготавливаем содержимое tooltip
                    let popupContent = "<div style='min-width: 150px;'>";
                    popupContent += "<b>" + pointName + "</b><br>";

                    // Добавляем информацию о регионе и районе
                    if (point.region_name) {
                        let regionName = currentLanguage === 'ru' ?
                            point.region_name : translateRegionName(point.region_name);
                        popupContent += "<b>" + translations[currentLanguage]['region_label'] + ":</b> " + regionName + "<br>";
                    }

                    if (point.area_name) {
                        let areaName = currentLanguage === 'ru' ?
                            point.area_name : translateAreaName(point.area_name);
                        popupContent += "<b>" + translations[currentLanguage]['area_label'] + ":</b> " + areaName + "<br>";
                    }

                    // Добавляем тип технологии (2G/3G/4G)
                    if (point.typeG) {
                        popupContent += "<b>" + translations[currentLanguage]['technology_label'] + ":</b> " + point.typeG + "<br>";
                    }

                    // Добавляем продолжительность аварии
                    if (point.calcTime) {
                        popupContent += "<b>" + translations[currentLanguage]['downtime_label'] + ":</b> " + point.calcTime + "<br>";
                    }

                    popupContent += "</div>";

                    // Создаем временный popup
                    let popup = L.popup({
                        offset: [0, -10],
                        closeButton: false,
                        className: 'bs-hover-popup'
                    })
                        .setLatLng(e.latlng)
                        .setContent(popupContent)
                        .openOn(mymap);

                    // Сохраняем popup в маркере для возможности закрытия
                    this._hoverPopup = popup;
                });

                // Закрываем popup при mouseout
                circleMarker.on('mouseout', function (e) {
                    if (this._hoverPopup) {
                        mymap.closePopup(this._hoverPopup);
                        this._hoverPopup = null;
                    }
                });

                // Для аварийных БС показываем только постоянный tooltip с именем БС (если нужно)
                if (permanentTooltip) {
                    circleMarker.bindTooltip(pointName, {
                        permanent: true,
                        direction: 'bottom',
                        offset: [0, 3],
                        className: 'bs-name-tooltip'
                    });
                }
            } else {
                // Добавляем всплывающую подсказку с улучшенным содержимым для не-аварийных БС
                let tooltipContent = "<b>" + pointName + "</b><br>" +
                    translations[currentLanguage]['bs_active'];

                circleMarker.bindTooltip(tooltipContent, {
                    permanent: false,
                    direction: 'top',
                    offset: [0, -10]
                });
            }

            return circleMarker;
        }

        // FILTER FUNCTIONS - ФИЛЬТРЛАШ ФУНКЦИЯЛАРИ

        // Фильтр қийматларини сақлаш учун функция
        function saveFilterState() {
            const filterState = {
                region: document.getElementById('region').value,
                area: document.getElementById('area').value,
                bsSearch: document.getElementById('bs-search').value,
                status: document.getElementById('status').value,
                durationFilter: currentDurationFilter
            };
            localStorage.setItem('filterState', JSON.stringify(filterState));
        }

        // Функция для фильтрации БС по имени или номеру
        function filterByBsName(stations, searchText) {
            searchText = searchText.toLowerCase();
            return stations.filter(station => {
                const bsName = (station.bsName || station.name || '').toLowerCase();
                const bsNumber = (station.bsNumber || station.number || '').toString().toLowerCase();
                return bsName.includes(searchText) || bsNumber.includes(searchText);
            });
        }

        // Функция для применения фильтра по статусу и отображения точек
        function applyStatusFilter(pointsToFilter, statusValue, isRefresh = false) {
            // Если это обновление данных (рефреш), сохраняем текущие фильтры
            const regionId = document.getElementById('region').value;
            const areaId = document.getElementById('area').value;

            // Всегда удаляем все маркеры перед добавлением новых
            pointLayers.forEach(marker => mymap.removeLayer(marker));
            pointLayers.length = 0;

            let displayPoints = [...pointsToFilter];

            // Фильтр по статусу
            if (statusValue === 'online') {
                displayPoints = pointsToFilter.filter(point => point.status === false);
            } else if (statusValue === 'offline') {
                displayPoints = pointsToFilter.filter(point => point.status === true);
            }

            // Если выбран регион и это не район-фильтр, дополнительно проверяем регион
            if (regionId && !areaId && isRefresh) {
                displayPoints = displayPoints.filter(point => point.region_id == regionId);
            }

            // Обновляем локальную статистику для текущего выбора
            const totalDisplayed = displayPoints.length;
            const inactiveDisplayed = displayPoints.filter(point => point.status === true).length;
            const activeDisplayed = totalDisplayed - inactiveDisplayed;

            // Обновляем статистику в сайдбаре
            document.getElementById('total-bs-count').textContent = totalDisplayed;
            document.getElementById('active-bs-count').textContent = activeDisplayed;
            document.getElementById('inactive-bs-count').textContent = inactiveDisplayed;

            // Давомийлик статистикасини ҳисоблаш
            calculateDurationStats(displayPoints);

            // Отображаем отфильтрованные точки на карте
            if (displayPoints && displayPoints.length > 0) {
                displayPoints.forEach(function (point) {
                    // Проверяем правильность региона для точки при обновлении
                    if (isRefresh && regionId && point.region_id != regionId) {
                        return; // Пропускаем точки из других регионов
                    }

                    // Создаем маркер с помощью функции
                    const circleMarker = createBSMarker(point, statusValue);
                    if (circleMarker) {
                        circleMarker.addTo(mymap);
                        pointLayers.push(circleMarker);
                    }
                });
            }

            // При обновлении данных, если выбран регион или район, обновляем также
            // визуальное выделение активного региона в таблице регионов
            if (isRefresh) {
                updateRegionsStats();
            }
        }

        // TRANSLATION FUNCTIONS - ТАРЖИМА ФУНКЦИЯЛАРИ

        // Viloyat/tuman nomini tarjima qilish funksiyasi
        function translateRegionName(name) {
            if (currentLanguage === 'ru') return name;
            return regionTranslations[name] || name;
        }

        // Tuman nomini tarjima qilish
        function translateAreaName(name) {
            if (currentLanguage === 'ru') return name;
            return areaTranslations[name] || transliterate(name);
        }

        // Kirill harflaridan lotin harflariga o'tkazish uchun funksiya
        function transliterate(text) {
            const translitMap = {
                'а': 'a', 'б': 'b', 'в': 'v', 'г': 'g', 'д': 'd', 'е': 'e', 'ё': 'yo',
                'ж': 'zh', 'з': 'z', 'и': 'i', 'й': 'y', 'к': 'k', 'л': 'l', 'м': 'm',
                'н': 'n', 'о': 'o', 'п': 'p', 'р': 'r', 'с': 's', 'т': 't', 'у': 'u',
                'ф': 'f', 'х': 'kh', 'ц': 'ts', 'ч': 'ch', 'ш': 'sh', 'щ': 'sch', 'ъ': '',
                'ы': 'y', 'ь': '', 'э': 'e', 'ю': 'yu', 'я': 'ya',
                'А': 'A', 'Б': 'B', 'В': 'V', 'Г': 'G', 'Д': 'D', 'Е': 'E', 'Ё': 'Yo',
                'Ж': 'Zh', 'З': 'Z', 'И': 'I', 'Й': 'Y', 'К': 'K', 'Л': 'L', 'М': 'M',
                'Н': 'N', 'О': 'O', 'П': 'P', 'Р': 'R', 'С': 'S', 'Т': 'T', 'У': 'U',
                'Ф': 'F', 'Х': 'Kh', 'Ц': 'Ts', 'Ч': 'Ch', 'Ш': 'Sh', 'Щ': 'Sch', 'Ъ': '',
                'Ы': 'Y', 'Ь': '', 'Э': 'E', 'Ю': 'Yu', 'Я': 'Ya'
            };

            return text.split('').map(char => translitMap[char] || char).join('');
        }

        // Tilni o'zgartirish funksiyasi
        function changeLanguage(lang) {
            currentLanguage = lang;
            localStorage.setItem('mapLanguage', lang);

            // Til tugmalarini yangilash
            document.getElementById('lang-en').classList.toggle('active', lang === 'en');
            document.getElementById('lang-ru').classList.toggle('active', lang === 'ru');

            // Tarjimalarni qo'llash
            document.getElementById('stats-header').textContent = translations[lang]['stats_header'];
            document.getElementById('region-header').textContent = translations[lang]['region_header'];
            document.getElementById('area-label').textContent = translations[lang]['area_label'];
            document.getElementById('bs-search-label').textContent = translations[lang]['bs_search_label'];
            document.getElementById('bs-search').placeholder = translations[lang]['bs_search_placeholder'];
            document.getElementById('reset-filters').textContent = translations[lang]['reset_filters'];
            document.getElementById('all-regions').textContent = translations[lang]['all_regions'];
            document.getElementById('all-areas').textContent = translations[lang]['all_areas'];
            document.getElementById('status-all').textContent = translations[lang]['status_all'];
            document.getElementById('status-online').textContent = translations[lang]['status_online'];
            document.getElementById('status-offline').textContent = translations[lang]['status_offline'];

            // Qidiruv elementlarini yangilash
            document.getElementById('location-search').placeholder = translations[lang]['search_location'];
            document.getElementById('search-button').title = translations[lang]['search_button'];

            // Viloyatlar nomlarini tarjima qilish
            const regionSelect = document.getElementById('region');
            Array.from(regionSelect.options).forEach(option => {
                if (option.value !== '') {
                    const originalName = option.dataset.originalName || option.textContent;
                    option.textContent = lang === 'ru' ? originalName : translateRegionName(originalName);
                }
            });

            // Tumanlar nomlarini tarjima qilish
            const areaSelect = document.getElementById('area');
            if (!areaSelect.disabled) {
                Array.from(areaSelect.options).forEach(option => {
                    if (option.value !== '') {
                        const originalName = option.dataset.originalName || option.textContent;
                        option.textContent = lang === 'ru' ? originalName : translateAreaName(originalName);
                    }
                });
            }

            // BS tooltiplarini yangilash
            updateBSStatusText();

            // Hudud statistikasini yangilash
            updateRegionsStats();

            // Авария давомийлиги легендасини таржима қилиш
            document.getElementById('downtime-legend-header').textContent = translations[lang]['downtime_legend_header'];
            document.getElementById('downtime-label-1').textContent = translations[lang]['downtime_1h'];
            document.getElementById('downtime-label-2').textContent = translations[lang]['downtime_2h'];
            document.getElementById('downtime-label-3').textContent = translations[lang]['downtime_3h'];
            document.getElementById('downtime-label-4').textContent = translations[lang]['downtime_4h'];
            document.getElementById('downtime-label-5').textContent = translations[lang]['downtime_more_4h'];
            document.getElementById('downtime-label-all').textContent = translations[lang]['downtime_all'];
        }

        // BS statuslari matnlarini yangilash
        function updateBSStatusText() {
            mymap.eachLayer(function (layer) {
                if (layer instanceof L.CircleMarker && layer.getTooltip()) {
                    const pointName = layer.options.pointName;
                    const isOffline = layer.options.fillColor !== 'green';

                    // Doimiy tooltiplar uchun o'zgartirishlar shart emas (faqat BS nomi ko'rsatiladi)
                    if (!layer.getTooltip().options.permanent) {
                        const statusText = isOffline ?
                            translations[currentLanguage]['bs_inactive'] :
                            translations[currentLanguage]['bs_active'];

                        // Eski tooltip-ni o'chirib, yangi matn bilan yangisini yaratish
                        layer.unbindTooltip();
                        layer.bindTooltip("<b>" + pointName + "</b><br>" + statusText, {
                            permanent: false,
                            direction: 'top',
                            offset: [0, -10]
                        });
                    }
                }
            });
        }

        // MAP INITIALIZATION - КАРТАНИ ИНИЦИАЛИЗАЦИЯ ҚИЛИШ

        // 1. Инициализация карты
        var mymap = L.map('mapid').setView([41.3, 69.3], 6); // Марказ Ўзбекистон - умумий кўриниш

        // Qidiruv markeri
        var searchMarker = null;

        // OpenStreetMap слои
        var osmMapLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(mymap);

        var osmSatelliteLayer = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
            attribution: '&copy; Esri &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community',
            maxZoom: 20
        });

        // Яндекс карта слои
        var yandexMapLayer = L.tileLayer('https://core-renderer-tiles.maps.yandex.net/tiles?l=map&x={x}&y={y}&z={z}', {
            attribution: '&copy; <a href="https://yandex.ru/maps">Яндекс Карты</a>',
            maxZoom: 19
        });

        var yandexSatelliteLayer = L.tileLayer('https://core-sat.maps.yandex.net/tiles?l=sat&x={x}&y={y}&z={z}', {
            attribution: '&copy; <a href="https://yandex.ru/maps">Яндекс Карты</a>',
            maxZoom: 19
        });

        // Текущие активные слои
        var currentMapLayer = osmMapLayer;
        var currentSatelliteLayer = osmSatelliteLayer;
        var isMapMode = true; // По умолчанию режим карты

        // Ўзбекистон чегаралари
        const uzbekistanBounds = [
            [37.1, 55.9], // Жанубий-ғарбий нуқта
            [45.6, 73.1]  // Шимолий-шарқий нуқта
        ];
        // Картани Ўзбекистон билан чегаралаш
        mymap.setMaxBounds(uzbekistanBounds);

        // MAP AND UI EVENT HANDLING - КАРТА ВА UI БИЛАН БОҒЛИҚ ҲОДИСАЛАР

        // Функция для обновления слоя карты в зависимости от выбранного режима и источника
        function updateMapLayer() {
            // Удаляем текущий слой
            if (mymap.hasLayer(currentMapLayer)) {
                mymap.removeLayer(currentMapLayer);
            }
            if (mymap.hasLayer(currentSatelliteLayer)) {
                mymap.removeLayer(currentSatelliteLayer);
            }

            // Добавляем нужный слой в зависимости от режима
            if (isMapMode) {
                currentMapLayer.addTo(mymap);
            } else {
                currentSatelliteLayer.addTo(mymap);
            }
        }

        // Функция для обновления размера кругов и текста на карте
        function updateCircleSizes() {
            const currentZoom = mymap.getZoom();
            const newRadius = getCircleRadius(currentZoom);

            // Определяем класс для масштаба текста
            let zoomClass = '';
            if (currentZoom < 8) {
                zoomClass = 'zoom-level-low';
            } else if (currentZoom < 12) {
                zoomClass = 'zoom-level-medium';
            } else {
                zoomClass = 'zoom-level-high';
            }

            // Сначала удаляем все классы масштаба
            document.body.classList.remove('zoom-level-low', 'zoom-level-medium', 'zoom-level-high');
            // Добавляем нужный класс
            document.body.classList.add(zoomClass);

            // Обновляем радиус всех маркеров
            mymap.eachLayer(function (layer) {
                if (layer instanceof L.CircleMarker) {
                    layer.setRadius(newRadius);

                    // Если это аварийный БС с постоянной подсказкой, обновляем offset подсказки
                    if (layer.getTooltip() && layer.getTooltip().options.permanent) {
                        // Обновляем offset в зависимости от зума
                        let offsetValue = 3;
                        if (currentZoom < 8) {
                            offsetValue = 2;
                        } else if (currentZoom >= 12) {
                            offsetValue = 4;
                        }

                        // Получаем текущий контент подсказки
                        const tooltipContent = layer.getTooltip().getContent();

                        // Удаляем старую подсказку и создаем новую с обновленным offset
                        layer.unbindTooltip();
                        layer.bindTooltip(tooltipContent, {
                            permanent: true,
                            direction: 'bottom',
                            offset: [0, offsetValue],
                            className: 'bs-name-tooltip'
                        });
                    }
                }
            });
        }

        // Qidiruv funksiyasi
        function searchLocation(query) {
            // Agar bo'sh so'rov bo'lsa, hech narsa qilmaymiz
            if (!query.trim()) return;

            // Joriy tilga qarab API so'rovini sozlash
            const acceptLanguage = currentLanguage === 'en' ? 'en' : 'ru';

            // Nominatim API orqali qidiruv - accept-language parametri qo'shildi
            fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(query)}&accept-language=${acceptLanguage}`)
                .then(response => response.json())
                .then(data => {
                    if (data && data.length > 0) {
                        const result = data[0];
                        const lat = parseFloat(result.lat);
                        const lon = parseFloat(result.lon);

                        // Kartani topilgan joyga ko'chirish
                        mymap.setView([lat, lon], 13);

                        // Agar oldingi marker bo'lsa, uni o'chiramiz
                        if (searchMarker) {
                            mymap.removeLayer(searchMarker);
                        }

                        // Yangi marker qo'shamiz
                        searchMarker = L.marker([lat, lon]).addTo(mymap);

                        // Marker popup matnini o'rnatish
                        searchMarker.bindPopup(result.display_name).openPopup();
                    } else {
                        // Xabarni joriy tilga moslashtirish
                        alert(translations[currentLanguage]['location_not_found']);
                    }
                })
                .catch(error => {
                    console.error('Qidiruv xatosi:', error);
                    // Xabarni joriy tilga moslashtirish
                    alert(translations[currentLanguage]['search_error']);
                });
        }

        // DATA HANDLING AND DISPLAY - МАЪЛУМОТЛАРНИ ҚАЙТА ИШЛАШ ВА КЎРСАТИШ

        // Получение данных о точках
        const pointsDataElement = document.getElementById('points-data');
        var points = JSON.parse(pointsDataElement.textContent);

        // Убедимся, что у всех точек есть нужные поля и правильные типы
        points = points.map(point => {
            // Преобразуем строковые координаты в числа, если они ещё не преобразованы
            if (typeof point.lat === 'string') {
                point.lat = parseFloat(point.lat.replace(',', '.'));
            }
            if (typeof point.lon === 'string') {
                point.lon = parseFloat(point.lon.replace(',', '.'));
            }
            return point;
        });

        // Создаем и добавляем маркеры на карту
        const pointLayers = []; // Массив для хранения слоев маркеров

        // Подсчет статистики БС
        var totalBsCount = points.length;
        var inactiveBsCount = points.filter(point => point.status === true).length;
        var activeBsCount = totalBsCount - inactiveBsCount;

        // Глобал статистика қийматларини сақлаш
        globalTotalBsCount = totalBsCount;
        globalActiveBsCount = activeBsCount;
        globalInactiveBsCount = inactiveBsCount;

        // Отображение статистики
        document.getElementById('total-bs-count').textContent = totalBsCount;
        document.getElementById('active-bs-count').textContent = activeBsCount;
        document.getElementById('inactive-bs-count').textContent = inactiveBsCount;

        // Вилоятлар статистикасини янгилаш функцияси
        function updateRegionsStats() {
            const container = document.getElementById('regions-stats-container');
            container.innerHTML = '';

            // Фаол вилоят id-сини олиш
            const activeRegionId = document.getElementById('region').value;

            // Республика Ўзбекистон сатрини жадвалнинг бошига қўшиш
            const uzbekistanRow = document.createElement('tr');
            uzbekistanRow.className = activeRegionId === '' ? 'active' : '';
            uzbekistanRow.dataset.regionId = '';

            // Ўзбекистон номи
            const uzNameCell = document.createElement('td');
            uzNameCell.className = 'region-name-cell';
            uzNameCell.textContent = translations[currentLanguage]['all_regions'];
            uzNameCell.dataset.statType = 'all';
            uzbekistanRow.appendChild(uzNameCell);

            // Умумий БС сони
            const uzTotalCell = document.createElement('td');
            uzTotalCell.className = 'total-stat';
            uzTotalCell.textContent = globalTotalBsCount;
            uzTotalCell.dataset.statType = 'all';
            uzbekistanRow.appendChild(uzTotalCell);

            // Онлайн БС сони
            const uzOnlineCell = document.createElement('td');
            uzOnlineCell.className = 'online-stat';
            uzOnlineCell.textContent = globalActiveBsCount;
            uzOnlineCell.dataset.statType = 'online';
            uzbekistanRow.appendChild(uzOnlineCell);

            // Оффлайн БС сони
            const uzOfflineCell = document.createElement('td');
            uzOfflineCell.className = 'offline-stat';
            uzOfflineCell.textContent = globalInactiveBsCount;
            uzOfflineCell.dataset.statType = 'offline';
            uzbekistanRow.appendChild(uzOfflineCell);

            // Оффлайн БС фоизи
            const uzOfflinePercentCell = document.createElement('td');
            uzOfflinePercentCell.className = 'offline-stat';
            const uzOfflinePercent = globalTotalBsCount > 0 ?
                ((globalInactiveBsCount / globalTotalBsCount) * 100).toFixed(1) : '0.0';
            uzOfflinePercentCell.textContent = uzOfflinePercent + '%';
            uzOfflinePercentCell.dataset.statType = 'offline';
            uzbekistanRow.appendChild(uzOfflinePercentCell);

            container.appendChild(uzbekistanRow);

            // Ўзбекистон сатрига клик қўшиш
            uzbekistanRow.addEventListener('click', function (e) {
                const statType = e.target.dataset.statType || 'all';

                // Умумий фильтр танлаш ва тозалаш
                document.getElementById('region').value = '';
                document.getElementById('area').value = '';
                document.getElementById('area').disabled = true;
                document.getElementById('status').value = statType;

                // Картани умумий кўринишга қайтариш
                mymap.setView([41.3, 69.3], 6);

                // Умумий БС лар фильтрини қўллаш
                let filteredPoints = [...points];
                applyStatusFilter(filteredPoints, statType);

                // Фильтр ҳолатини сақлаш
                saveFilterState();

                // Вилоятлар статистикасини янгилаш
                updateRegionsStats();
            });

            // Ҳар бир вилоят учун элементлар яратиш
            Object.keys(regionsStatsData).forEach(regionId => {
                const region = regionsStatsData[regionId];
                const isActive = activeRegionId === regionId;

                const row = document.createElement('tr');
                row.className = isActive ? 'active' : '';
                row.dataset.regionId = regionId;

                // Вилоят номи - use originalName for translation if available
                const nameCell = document.createElement('td');
                nameCell.className = 'region-name-cell';
                const regionName = region.originalName || region.name;
                nameCell.textContent = currentLanguage === 'ru' ?
                    regionName : translateRegionName(regionName);
                nameCell.dataset.statType = 'all';
                row.appendChild(nameCell);

                // Умумий БС сони
                const totalCell = document.createElement('td');
                totalCell.className = 'total-stat';
                totalCell.textContent = region.total;
                totalCell.dataset.statType = 'all';
                row.appendChild(totalCell);

                // Онлайн БС сони
                const onlineCell = document.createElement('td');
                onlineCell.className = 'online-stat';
                onlineCell.textContent = region.online;
                onlineCell.dataset.statType = 'online';
                row.appendChild(onlineCell);

                // Оффлайн БС сони
                const offlineCell = document.createElement('td');
                offlineCell.className = 'offline-stat';
                offlineCell.textContent = region.offline;
                offlineCell.dataset.statType = 'offline';
                row.appendChild(offlineCell);

                // Оффлайн БС фоизи
                const offlinePercentCell = document.createElement('td');
                offlinePercentCell.className = 'offline-stat';
                const offlinePercent = region.total > 0 ?
                    ((region.offline / region.total) * 100).toFixed(1) : '0.0';
                offlinePercentCell.textContent = offlinePercent + '%';
                offlinePercentCell.dataset.statType = 'offline';
                row.appendChild(offlinePercentCell);

                container.appendChild(row);

                // Вилоят саторига клик event listener қўшиш
                row.addEventListener('click', handleRegionClick);
            });
        }

        // Область обработки кликов по регионам - вынесена в отдельную функцию для уменьшения дублирования кода
        function handleRegionClick(e) {
            const regionId = this.dataset.regionId;
            const statType = e.target.dataset.statType || 'all';

            // Статус фильтрини танлаш
            document.getElementById('status').value = statType;

            if (regionId) {
                // Вилоят танлаш
                document.getElementById('region').value = regionId;

                // Туман селектини тозалаш
                const areaSelect = document.getElementById('area');
                areaSelect.innerHTML = '<option value="">' + translations[currentLanguage]['all_areas'] + '</option>';
                areaSelect.disabled = true;  // Янги маълумотлар келгунча ўчириб қўямиз

                // Вилоят учун БС ларни фильтрлаш
                const regionStations = points.filter(point => point.region_id == regionId);

                // Вилоят марказини ҳисоблаш
                if (regionStations.length > 0) {
                    const center = calculateCenter(regionStations);
                    if (center) {
                        mymap.setView([center.lat, center.lon], 10);
                    } else if (regionCenters[regionId]) {
                        mymap.setView(regionCenters[regionId], 10);
                    }
                } else if (regionCenters[regionId]) {
                    mymap.setView(regionCenters[regionId], 10);
                }

                // Вилоят учун районларни юклаш
                fetch(`/api/areas/region/${regionId}/`)
                    .then(response => response.json())
                    .then(areas => {
                        areas.forEach(area => {
                            const option = document.createElement('option');
                            option.value = area.id;
                            const areaName = area.name;
                            option.dataset.originalName = areaName;
                            option.textContent = currentLanguage === 'ru' ?
                                areaName : translateAreaName(areaName);
                            areaSelect.appendChild(option);
                        });
                        areaSelect.disabled = false;
                    });

                // Статус фильтрини қўллаш (online, offline, all)
                let filteredPoints = [...regionStations];

                // Агар қидирув қиймати бўлса, уни ҳам қўллаш
                const bsSearch = document.getElementById('bs-search').value;
                if (bsSearch) {
                    filteredPoints = filterByBsName(filteredPoints, bsSearch);
                }

                // Маркерларни фильтрлаш ва кўрсатиш
                applyStatusFilter(filteredPoints, statType);

            } else {
                // Агар умумий Ўзбекистон танланган бўлса
                document.getElementById('region').value = '';
                document.getElementById('area').value = '';
                document.getElementById('area').disabled = true;

                // Умумий кўринишга қайтиш
                mymap.setView([41.3, 69.3], 6);

                // Барча нуқталарни кўрсатиш, статус фильтрлаш билан
                applyStatusFilter(points, statType);
            }

            // Фильтр ҳолатини сақлаш
            saveFilterState();

            // Вилоятлар статистикасини янгилаш
            updateRegionsStats();
        }

        // Вилоятлар статистикасини тайёрлаш функцияси
        function prepareRegionsStats(callback) {
            // Регионлар номларини олиш
            fetch('/api/regions/')
                .then(response => response.json())
                .then(regions => {
                    // Рес. Узбекистан регионини фильтрлаб ташлаш
                    regions = regions.filter(region => region.name !== 'Рес. Узбекистан');

                    // Статистикани ҳисоблаш учун асосий маълумотлардан фойдаланиш
                    regions.forEach(region => {
                        // Регион учун БС ларни фильтрлаш
                        const regionStations = points.filter(point => point.region_id == region.id);

                        const total = regionStations.length;
                        const offline = regionStations.filter(station => station.status === true).length;
                        const online = total - offline;

                        // Регион статистикасини сақлаш
                        regionsStatsData[region.id] = {
                            name: region.name,
                            originalName: region.name, // Store original Russian name
                            total: total,
                            online: online,
                            offline: offline
                        };
                    });

                    // Статистика элементларини янгилаш
                    updateRegionsStats();
                    // If there's a callback, execute it
                    if (typeof callback === 'function') {
                        callback();
                    }
                });
        }

        // Маълумотларни янгилаш функцияси
        function refreshData() {
            // Сохраняем текущее состояние фильтров перед обновлением
            const currentRegion = document.getElementById('region').value;
            const currentArea = document.getElementById('area').value;
            const currentBsSearch = document.getElementById('bs-search').value;
            const currentStatus = document.getElementById('status').value;
            const currentDuration = currentDurationFilter; // Давомийлик фильтрини сақлаб қолиш

            // Сохраняем состояние в localStorage
            saveFilterState();

            fetch(window.location.href)
                .then(response => response.text())
                .then(html => {
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');
                    const newPointsData = JSON.parse(doc.getElementById('points-data').textContent);

                    // Сохраняем старые данные для сравнения и сохранения calcTime
                    const oldPoints = [...points];
                    const oldPointsMap = {};
                    oldPoints.forEach(point => {
                        const pointName = point.bsName || point.name;
                        oldPointsMap[pointName] = point;
                    });

                    // Обновляем данные с сохранением аварийных периодов
                    points = newPointsData.map(newPoint => {
                        const pointName = newPoint.bsName || newPoint.name;
                        const oldPoint = oldPointsMap[pointName];

                        // Если точка была аварийной и имеет calcTime, сохраняем старый calcTime
                        if (oldPoint && oldPoint.status === true && oldPoint.calcTime && newPoint.status === true) {
                            newPoint.calcTime = oldPoint.calcTime;
                        }

                        return newPoint;
                    });

                    // Обновляем общую статистику
                    totalBsCount = points.length;
                    inactiveBsCount = points.filter(point => point.status === true).length;
                    activeBsCount = totalBsCount - inactiveBsCount;

                    // Глобал статистика қийматларини янгилаш
                    globalTotalBsCount = totalBsCount;
                    globalActiveBsCount = activeBsCount;
                    globalInactiveBsCount = inactiveBsCount;

                    // Вилоятлар статистикасини янгилаш
                    prepareRegionsStats(function () {
                        if (currentLanguage === 'en') {
                            // Селектлардаги маълумотларни таржима қилиш
                            translateSelectOptions();
                            // BS статус tooltip ларини янгилаш
                            updateBSStatusText();
                        }
                    });

                    // Восстанавливаем состояние фильтров
                    document.getElementById('region').value = currentRegion;
                    document.getElementById('area').value = currentArea;
                    document.getElementById('bs-search').value = currentBsSearch;
                    document.getElementById('status').value = currentStatus;
                    currentDurationFilter = currentDuration;

                    // Применяем фильтры в зависимости от текущего состояния
                    // ВАЖНО: передаем флаг isRefresh=true для правильной обработки фильтров при обновлении
                    if (currentRegion || currentArea) {
                        let filteredData;

                        if (currentRegion && currentArea) {
                            // Получаем только данные для текущего района
                            filteredData = points.filter(point => point.area_id == currentArea);
                        }
                        else if (currentRegion) {
                            // Получаем только данные для выбранного региона
                            filteredData = points.filter(point => point.region_id == currentRegion);

                            // Удаляем все старые маркеры перед добавлением новых -
                            // чтобы избежать показа маркеров из других регионов
                            pointLayers.forEach(marker => mymap.removeLayer(marker));
                            pointLayers.length = 0;
                        }

                        // Если есть поиск по БС, применяем и его
                        if (currentBsSearch && filteredData) {
                            filteredData = filterByBsName(filteredData, currentBsSearch);
                        }

                        // Применяем фильтр статуса, указывая что это обновление
                        if (filteredData) {
                            applyStatusFilter(filteredData, currentStatus, true);
                        }
                    } else {
                        // Если нет фильтров по региону/району, просто применяем фильтры
                        applyCurrentFilters();
                    }
                })
                .catch(error => {
                    console.error('Маълумотларни янгилашда хато:', error);
                });
        }

        // Вспомогательная функция для перевода всех опций в select-ах
        function translateSelectOptions() {
            // Перевод опций в select регионов
            const regionSelect = document.getElementById('region');
            Array.from(regionSelect.options).forEach(option => {
                if (option.value !== '') {
                    const originalName = option.dataset.originalName || option.textContent;
                    option.textContent = translateRegionName(originalName);
                }
            });

            // Перевод опций в select районов
            const areaSelect = document.getElementById('area');
            Array.from(areaSelect.options).forEach(option => {
                if (option.value !== '') {
                    const originalName = option.dataset.originalName || option.textContent;
                    option.textContent = translateAreaName(originalName);
                }
            });
        }

        // Вспомогательная функция для применения всех текущих фильтров
        function applyCurrentFilters() {
            const regionId = document.getElementById('region').value;
            const areaId = document.getElementById('area').value;
            const bsSearch = document.getElementById('bs-search').value;
            const statusValue = document.getElementById('status').value;

            let dataToFilter;

            if (regionId) {
                if (areaId) {
                    // Фильтр по району
                    dataToFilter = points.filter(point => point.area_id == areaId);

                    // Если район выбран, меняем масштаб и центр карты согласно району
                    const areaStations = dataToFilter.filter(point =>
                        point.lat && point.lon && !isNaN(parseFloat(point.lat)) && !isNaN(parseFloat(point.lon))
                    );

                    if (areaStations.length > 0) {
                        const center = calculateCenter(areaStations);
                        if (center) {
                            // Используем более крупный масштаб для района
                            mymap.setView([center.lat, center.lon], 12);
                        }
                    }
                } else {
                    // Фильтр по региону
                    dataToFilter = points.filter(point => point.region_id == regionId);

                    // Центрируем карту на регионе если это необходимо
                    const regionStations = dataToFilter.filter(point =>
                        point.lat && point.lon && !isNaN(parseFloat(point.lat)) && !isNaN(parseFloat(point.lon))
                    );

                    if (regionStations.length > 0) {
                        const center = calculateCenter(regionStations);
                        if (center) {
                            mymap.setView([center.lat, center.lon], 10);
                        } else if (regionCenters[regionId]) {
                            mymap.setView(regionCenters[regionId], 10);
                        }
                    }
                }
            } else {
                // Все точки
                dataToFilter = [...points];
            }

            // Фильтр по имени/номеру БС
            if (bsSearch) {
                dataToFilter = filterByBsName(dataToFilter, bsSearch);
            }

            // Применение фильтра по статусу и отображение на карте
            applyStatusFilter(dataToFilter, statusValue);

            // Обновляем список районов если выбран регион
            if (regionId && (!areaId || areaId === '')) {
                fetch(`/api/areas/region/${regionId}/`)
                    .then(response => response.json())
                    .then(areas => {
                        const areaSelect = document.getElementById('area');
                        const currentAreaValue = areaSelect.value;

                        areaSelect.innerHTML = '<option value="">' + translations[currentLanguage]['all_areas'] + '</option>';

                        areas.forEach(area => {
                            const option = document.createElement('option');
                            option.value = area.id;
                            const areaName = area.name;
                            option.dataset.originalName = areaName;
                            option.textContent = currentLanguage === 'ru' ?
                                areaName : translateAreaName(areaName);
                            areaSelect.appendChild(option);
                        });

                        areaSelect.value = currentAreaValue;
                        areaSelect.disabled = false;
                    });
            }
        }

        // Функция для сброса всех фильтров
        function resetAllFilters() {
            document.getElementById('region').value = '';
            document.getElementById('area').value = '';
            document.getElementById('area').disabled = true;
            document.getElementById('bs-search').value = '';
            document.getElementById('status').value = 'all';

            // Давомийлик фильтрини тозалаш
            currentDurationFilter = 'all';
            updateDurationStats();

            saveFilterState();

            // Картани умумий кўринишга қайтариш
            mymap.setView([41.3, 69.3], 6);

            // Барча нуқталарни кўрсатиш
            applyStatusFilter(points, 'all');
        }

        // EVENT LISTENERS - БАРЧА ХОДИСАЛАР

        document.addEventListener('DOMContentLoaded', function () {
            // Til tugmalariga hodisalar qo'shish
            document.getElementById('lang-ru').addEventListener('click', function () {
                changeLanguage('ru');
            });

            document.getElementById('lang-en').addEventListener('click', function () {
                changeLanguage('en');
            });

            // Dastlabki tilni o'rnatish
            changeLanguage(currentLanguage);

            // Вилоятлар статистикасини тайёрлаш
            prepareRegionsStats();

            // Восстанавливаем фильтры из localStorage
            const savedState = localStorage.getItem('filterState');
            if (savedState) {
                const filterState = JSON.parse(savedState);

                // Restore filters
                document.getElementById('region').value = filterState.region || '';
                document.getElementById('bs-search').value = filterState.bsSearch || '';
                document.getElementById('status').value = filterState.status || 'all';

                // Restore duration filter
                if (filterState.durationFilter) {
                    currentDurationFilter = filterState.durationFilter;
                    updateDurationStats();
                }

                // Apply filters
                applyCurrentFilters();
            } else {
                // По умолчанию показываем все точки
                applyStatusFilter(points, 'all');
            }

            // Добавляем обработчики событий для элементов фильтрации

            // Сброс фильтров
            document.getElementById('reset-filters').addEventListener('click', resetAllFilters);

            // Поиск БС
            document.getElementById('bs-search').addEventListener('input', function () {
                applyCurrentFilters();
                saveFilterState();
            });

            // Изменение региона
            document.getElementById('region').addEventListener('change', function () {
                const regionId = this.value;
                const statusValue = document.getElementById('status').value;

                // Сбрасываем фильтр района при изменении региона
                document.getElementById('area').value = '';
                document.getElementById('area').disabled = true;

                if (regionId) {
                    // Если выбран регион, фильтруем только по этому региону
                    const regionStations = points.filter(point => point.region_id == regionId);

                    // Центрируем карту на регион
                    if (regionStations.length > 0) {
                        const center = calculateCenter(regionStations);
                        if (center) {
                            mymap.setView([center.lat, center.lon], 10);
                        } else if (regionCenters[regionId]) {
                            mymap.setView(regionCenters[regionId], 10);
                        }
                    }

                    // Применяем фильтры
                    let filteredPoints = [...regionStations];

                    // Если есть поиск по БС, применяем и его
                    const bsSearch = document.getElementById('bs-search').value;
                    if (bsSearch) {
                        filteredPoints = filterByBsName(filteredPoints, bsSearch);
                    }

                    // Применяем фильтр статуса
                    applyStatusFilter(filteredPoints, statusValue);

                    // Запрашиваем районы для выбранного региона
                    fetch(`/api/areas/region/${regionId}/`)
                        .then(response => response.json())
                        .then(areas => {
                            const areaSelect = document.getElementById('area');

                            areaSelect.innerHTML = '<option value="">' + translations[currentLanguage]['all_areas'] + '</option>';

                            areas.forEach(area => {
                                const option = document.createElement('option');
                                option.value = area.id;
                                const areaName = area.name;
                                option.dataset.originalName = areaName;
                                option.textContent = currentLanguage === 'ru' ?
                                    areaName : translateAreaName(areaName);
                                areaSelect.appendChild(option);
                            });

                            areaSelect.disabled = false;
                        });
                } else {
                    // Если регион не выбран, возвращаемся к общему виду
                    mymap.setView([41.3, 69.3], 6);
                    applyCurrentFilters();
                }

                // Сохраняем состояние фильтров
                saveFilterState();

                // Обновляем визуальное выделение в таблице
                updateRegionsStats();
            });

            // Изменение района
            document.getElementById('area').addEventListener('change', function () {
                const areaId = this.value;
                const regionId = document.getElementById('region').value;
                const statusValue = document.getElementById('status').value;

                if (areaId) {
                    // Если выбран район, фильтруем только по этому району
                    const areaStations = points.filter(point => point.area_id == areaId);

                    // Центрируем карту на район если есть данные
                    if (areaStations.length > 0) {
                        const center = calculateCenter(areaStations);
                        if (center) {
                            // Используем более крупный масштаб для района
                            mymap.setView([center.lat, center.lon], 12);
                        }
                    }

                    // Применяем фильтры
                    let filteredPoints = [...areaStations];

                    // Если есть поиск по БС, применяем и его
                    const bsSearch = document.getElementById('bs-search').value;
                    if (bsSearch) {
                        filteredPoints = filterByBsName(filteredPoints, bsSearch);
                    }

                    // Применяем фильтр статуса
                    applyStatusFilter(filteredPoints, statusValue);
                } else if (regionId) {
                    // Если сброшен район, но выбран регион - фильтруем по региону
                    const regionStations = points.filter(point => point.region_id == regionId);

                    // Центрируем карту на регион
                    if (regionStations.length > 0) {
                        const center = calculateCenter(regionStations);
                        if (center) {
                            mymap.setView([center.lat, center.lon], 10);
                        } else if (regionCenters[regionId]) {
                            mymap.setView(regionCenters[regionId], 10);
                        }
                    }

                    // Применяем фильтры
                    let filteredPoints = [...regionStations];

                    // Если есть поиск по БС, применяем и его
                    const bsSearch = document.getElementById('bs-search').value;
                    if (bsSearch) {
                        filteredPoints = filterByBsName(filteredPoints, bsSearch);
                    }

                    // Применяем фильтр статуса
                    applyStatusFilter(filteredPoints, statusValue);
                } else {
                    // Если ничего не выбрано, применяем общие фильтры
                    applyCurrentFilters();
                }

                // Сохраняем состояние фильтров
                saveFilterState();

                // Обновляем визуальное выделение в таблице
                updateRegionsStats();
            });

            // Изменение статуса
            document.getElementById('status').addEventListener('change', function () {
                applyCurrentFilters();
                saveFilterState();
            });

            // Qidiruv tugmasiga hodisa qo'shish
            document.getElementById('search-button').addEventListener('click', function () {
                const query = document.getElementById('location-search').value;
                searchLocation(query);
            });

            // Enter tugmasini bosish orqali qidiruv
            document.getElementById('location-search').addEventListener('keypress', function (e) {
                if (e.key === 'Enter') {
                    const query = this.value;
                    searchLocation(query);
                }
            });

            // Обработчики для режима карты
            document.getElementById('map-button').addEventListener('click', function () {
                isMapMode = true;
                this.classList.add('active');
                document.getElementById('satellite-button').classList.remove('active');
                updateMapLayer();
            });

            document.getElementById('satellite-button').addEventListener('click', function () {
                isMapMode = false;
                this.classList.add('active');
                document.getElementById('map-button').classList.remove('active');
                updateMapLayer();
            });

            // Обработчики для источника карты
            document.getElementById('osm-source-button').addEventListener('click', function () {
                currentMapLayer = osmMapLayer;
                currentSatelliteLayer = osmSatelliteLayer;
                this.classList.add('active');
                document.getElementById('yandex-source-button').classList.remove('active');
                updateMapLayer();
            });

            document.getElementById('yandex-source-button').addEventListener('click', function () {
                currentMapLayer = yandexMapLayer;
                currentSatelliteLayer = yandexSatelliteLayer;
                this.classList.add('active');
                document.getElementById('osm-source-button').classList.remove('active');
                updateMapLayer();
            });

            // Давомийлик легендаси фильтрлари
            const durationItems = document.querySelectorAll('.downtime-item');
            durationItems.forEach(item => {
                item.addEventListener('click', function () {
                    const duration = this.getAttribute('data-duration');
                    currentDurationFilter = duration;

                    // Статус фильтрини "all" га қайтариш
                    const statusFilter = document.getElementById('status');
                    if (statusFilter.value !== 'all') {
                        statusFilter.value = 'all';
                    }

                    // Фильтрларни қўллаш
                    applyCurrentFilters();

                    // Легенда визуал ҳолатини янгилаш
                    updateDurationStats();

                    // Фильтр ҳолатини сақлаш
                    saveFilterState();
                });
            });
        });

        // Слушаем событие 'zoomend'
        mymap.on('zoomend', updateCircleSizes);

        // Ҳар 10 секундда маълумотларни янгилаш
        setInterval(refreshData, 10000);
    </script>
</body>

{% endblock content %}