{% extends 'base.html' %}
{% load static %}


{% block content %}

{% if error %}
<h2> В базе данных не найдены наименование районов нижеследующих БС  </h2>
<table class="table table-striped">
    <tr><b>
        <td width="10%">№</td>
        <td width="50%">Наименование БС</td>
        <td width="20%">Район</td>
    </b></tr>

    {% for errorbs, errorarea in allError %}

    <tr>
    <td >{{ forloop.counter }}</td>
        <td>{{ errorbs }} </td>
        <td>{{ errorarea }}</td>
    </tr>
    {% endfor %}
</table>
{% else %}

<h1> {{ message }} </h1>

{% endif %}

<input class="btn btn-primary" type="button" value="Назад" onclick="javascript:history.go(-1);">

{% endblock content %}