{% extends 'base.html' %}
{% load crispy_forms_tags %}
{% block title %} Map View BS Beeline {% endblock title %}

{% block content %}

<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
    integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin="" />
<link rel="stylesheet" href="/static/css/styles.css">

<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
    integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>

<style>
    /* Body stillarini o'rnatamiz */
    body {
        margin: 0;
        padding: 0;
        overflow: hidden;
    }

    /* Navbar stillarini o'zgartiramiz */
    .navbar {
        margin-bottom: 0 !important;
        z-index: 2000;
        position: relative;
    }

    /* Карта контейнери учун стиллар */
    #mapid {
        height: 100vh;
        width: calc(100% - 300px);
        margin: 0;
        padding: 0;
        position: fixed;
        top: 0;
        left: 300px;
        right: 0;
        bottom: 0;
        z-index: 95;
    }

    /* Zoom tugmalarini to'g'rilash */
    .leaflet-control-container .leaflet-top {
        top: 80px;
    }

    .leaflet-control-container .leaflet-right {
        right: 10px;
    }

    .leaflet-control-zoom {
        margin-right: 10px;
    }

    /* Bo'sh joyni yo'qotish uchun */
    .container-fluid {
        padding: 0;
        margin: 0;
        position: relative;
    }

    /* Стиль для сайдбара */
    .sidebar {
        width: 300px;
        height: calc(100vh - 70px);
        background-color: white;
        box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
        display: flex;
        flex-direction: column;
        overflow: hidden;
        position: fixed;
        top: 70px;
        left: 0;
        z-index: 1000;
        border-radius: 0;
    }

    .sidebar-header {
        padding: 15px;
        background-color: #2c3e50;
        color: white;
    }

    .sidebar-header h2 {
        margin: 0;
        font-size: 18px;
        font-weight: 500;
    }

    .sidebar-content {
        padding: 15px;
        overflow-y: auto;
        flex: 1;
    }

    .stats-container {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;
        background-color: #f5f5f5;
        border-radius: 5px;
        padding: 10px;
    }

    .stat-item {
        text-align: center;
        flex: 1;
    }

    .stat-value {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 5px;
    }

    .stat-label {
        font-size: 12px;
        color: #666;
    }

    .stat-item.online .stat-value {
        color: #4caf50;
    }

    .stat-item.offline .stat-value {
        color: #f44336;
    }

    .filter-group {
        margin-bottom: 15px;
    }

    .filter-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 500;
        font-size: 14px;
        color: #333;
    }

    .filter-group select {
        width: 100%;
        padding: 8px 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
    }

    .filter-group select:focus {
        outline: none;
        border-color: #2c3e50;
    }

    .filter-group select:disabled {
        background-color: #f5f5f5;
        cursor: not-allowed;
    }

    .map-type-control {
        background-color: white;
        border-radius: 4px;
        box-shadow: 0 1px 5px rgba(0, 0, 0, 0.4);
        padding: 5px;
        display: flex;
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 1000;
    }

    .map-type-control button {
        background-color: white;
        border: 1px solid #ccc;
        padding: 6px 10px;
        cursor: pointer;
        font-size: 12px;
        outline: none;
    }

    .map-type-control button:first-child {
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;
        border-right: none;
    }

    .map-type-control button:last-child {
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
    }

    .map-type-control button.active {
        background-color: #f0f0f0;
        font-weight: bold;
    }
</style>
</head>

<body>

    <div class="container-fluid">
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>Фильтры</h2>
            </div>
            <div class="sidebar-content">
                <!-- Статистика -->
                <div class="stats-container">
                    <div class="stat-item">
                        <div class="stat-value" id="total-bs-count">0</div>
                        <div class="stat-label">Всего БС</div>
                    </div>
                    <div class="stat-item online">
                        <div class="stat-value" id="active-bs-count">0</div>
                        <div class="stat-label">Онлайн</div>
                    </div>
                    <div class="stat-item offline">
                        <div class="stat-value" id="inactive-bs-count">0</div>
                        <div class="stat-label">Оффлайн</div>
                    </div>
                </div>

                <!-- Выбор региона -->
                <div class="filter-group">
                    <label for="region">Регион:</label>
                    <select id="region">
                        <option value="">Все регионы</option>
                        <!-- Регионы будут добавлены динамически -->
                    </select>
                </div>

                <!-- Выбор района -->
                <div class="filter-group">
                    <label for="area">Район:</label>
                    <select id="area" disabled>
                        <option value="">Все районы</option>
                        <!-- Районы будут добавлены динамически -->
                    </select>
                </div>

                <!-- Выбор статуса -->
                <div class="filter-group">
                    <label for="status">Статус:</label>
                    <select id="status">
                        <option value="all">Все</option>
                        <option value="online">Онлайн</option>
                        <option value="offline">Оффлайн</option>
                    </select>
                </div>

                <div class="mt-3">
                    <button id="map-button" class="btn btn-primary btn-sm">Карта</button>
                    <button id="satellite-button" class="btn btn-secondary btn-sm">Спутник</button>
                </div>
            </div>
        </div>
    </div>

    <div id="mapid"></div>

    {{ points_data_json|json_script:"points-data" }}

    <script>
        // 1. Инициализация карты
        var mymap = L.map('mapid').setView([41.3, 69.3], 6) // Марказ Ўзбекистон - умумий кўриниш

        // 2. Добавление базового слоя карты (по умолчанию)
        var osmLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(mymap);

        // 3. Добавление спутникового слоя
        var satelliteLayer = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
            attribution: '&copy; Esri &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community',
            maxZoom: 20
        });

        // 4. Получение данных о точках
        const pointsDataElement = document.getElementById('points-data');
        const points = JSON.parse(pointsDataElement.textContent);

        // Подсчет статистики БС
        const totalBsCount = points.length;
        const inactiveBsCount = points.filter(point => point.status === true).length;
        const activeBsCount = totalBsCount - inactiveBsCount;

        // Отображение статистики
        document.getElementById('total-bs-count').textContent = totalBsCount;
        document.getElementById('active-bs-count').textContent = activeBsCount;
        document.getElementById('inactive-bs-count').textContent = inactiveBsCount;

        // Функция для определения размера круга в зависимости от уровня зума
        function getCircleRadius(zoom) {
            if (zoom < 6) {
                return 2;
            } else if (zoom < 10) {
                return 4;
            } else {
                return 10;
            }
        }

        // Функция для обновления размера кругов на карте
        function updateCircleSizes() {
            const currentZoom = mymap.getZoom();
            const newRadius = getCircleRadius(currentZoom);

            mymap.eachLayer(function (layer) {
                if (layer instanceof L.CircleMarker) {
                    layer.setRadius(newRadius);
                }
            });
        }

        // Создаем и добавляем маркеры на карту
        const pointLayers = []; // Массив для хранения слоев маркеров
        const pointsByRegion = {}; // Объект для хранения точек по регионам
        const pointsByArea = {}; // Объект для хранения точек по районам

        // Координаты центров регионов
        const regionCenters = {
            1: [41.311081, 69.240562], // Тошкент шаҳри
            2: [40.783555, 72.350891], // Андижон
            3: [40.384240, 71.785690], // Фарғона
            4: [41.001071, 71.672278], // Наманган
            5: [39.768083, 64.421710], // Бухоро
            6: [40.121462, 67.842194], // Жиззах
            7: [38.839802, 65.781462], // Қашқадарё
            8: [40.103922, 65.374260], // Навоий
            9: [41.248990, 69.333240], // Тошкент вилояти
            10: [40.837641, 68.661338], // Сирдарё
            11: [37.940552, 67.510929], // Сурхондарё
            12: [41.552080, 60.631622], // Хоразм
            13: [43.804363, 59.018464], // Қорақалпоғистон
            14: [39.654388, 66.975824]  // Самарқанд
        };

        // Загрузка регионов
        fetch('/api/regions/')
            .then(response => response.json())
            .then(regions => {
                const regionSelect = document.getElementById('region');
                regions.forEach(region => {
                    const option = document.createElement('option');
                    option.value = region.id;
                    option.textContent = region.name;
                    regionSelect.appendChild(option);
                });
            });

        // Обработчик изменения региона
        document.getElementById('region').addEventListener('change', function () {
            const regionId = parseInt(this.value);
            const areaSelect = document.getElementById('area');

            // Очистка списка районов
            areaSelect.innerHTML = '<option value="">Все районы</option>';

            // Если выбрано "Все регионы" или значение пустое
            if (!regionId) {
                // Отключаем выбор района
                areaSelect.disabled = true;
                // Возвращаем карту к общему виду Узбекистана
                mymap.setView([41.3, 69.3], 6); // Марказ Ўзбекистон - умумий кўриниш
                // Показываем все точки
                pointLayers.forEach(marker => {
                    marker.addTo(mymap);
                });
                // Возвращаем общую статистику
                document.getElementById('total-bs-count').textContent = totalBsCount;
                document.getElementById('active-bs-count').textContent = activeBsCount;
                document.getElementById('inactive-bs-count').textContent = inactiveBsCount;
                return;
            }

            if (regionId) {
                // Загрузка районов для выбранного региона
                fetch(`/api/areas/region/${regionId}/`)
                    .then(response => response.json())
                    .then(areas => {
                        areas.forEach(area => {
                            const option = document.createElement('option');
                            option.value = area.id;
                            option.textContent = area.name;
                            areaSelect.appendChild(option);
                        });
                        areaSelect.disabled = false;
                    });

                // Загрузка базовых станций для выбранного региона и центрирование карты
                fetch(`/api/base-stations/region/${regionId}/`)
                    .then(response => response.json())
                    .then(stations => {
                        if (stations.length > 0) {
                            // Вычисляем центр региона на основе координат станций
                            let totalLat = 0;
                            let totalLon = 0;
                            stations.forEach(station => {
                                totalLat += parseFloat(station.lat);
                                totalLon += parseFloat(station.lon);
                            });
                            const centerLat = totalLat / stations.length;
                            const centerLon = totalLon / stations.length;

                            // Центрируем карту на вычисленном центре региона
                            mymap.setView([centerLat, centerLon], 10);
                        }
                    });

                // Обновление статистики для выбранного региона
                updateStatisticsByRegion(regionId);
            } else {
                // Если регион не выбран, отключаем выбор района
                areaSelect.disabled = true;

                // Возвращаем карту к общему виду
                mymap.setView([41.3, 69.3], 6);

                // Возвращаем общую статистику
                document.getElementById('total-bs-count').textContent = totalBsCount;
                document.getElementById('active-bs-count').textContent = activeBsCount;
                document.getElementById('inactive-bs-count').textContent = inactiveBsCount;
            }
        });

        // Обработчик изменения района
        document.getElementById('area').addEventListener('change', function () {
            const areaId = parseInt(this.value);

            if (areaId) {
                // Загрузка базовых станций для выбранного района
                fetch(`/api/base-stations/area/${areaId}/`)
                    .then(response => response.json())
                    .then(stations => {
                        if (stations.length > 0) {
                            // Вычисляем центр района на основе координат станций
                            let totalLat = 0;
                            let totalLon = 0;
                            stations.forEach(station => {
                                totalLat += parseFloat(station.lat);
                                totalLon += parseFloat(station.lon);
                            });
                            const centerLat = totalLat / stations.length;
                            const centerLon = totalLon / stations.length;

                            // Центрируем карту на вычисленном центре района
                            mymap.setView([centerLat, centerLon], 11);

                            // Обновляем статистику для района
                            updateStatisticsByArea(areaId, stations);
                        }
                    });
            } else {
                // Если район не выбран, возвращаемся к виду региона
                const regionId = parseInt(document.getElementById('region').value);
                if (regionId && regionCenters[regionId]) {
                    mymap.setView(regionCenters[regionId], 8);
                    updateStatisticsByRegion(regionId);
                }
            }
        });

        // Функция обновления статистики по региону
        function updateStatisticsByRegion(regionId) {
            fetch(`/api/base-stations/region/${regionId}/`)
                .then(response => response.json())
                .then(stations => {
                    const total = stations.length;
                    const inactive = stations.filter(station => station.status === true).length;
                    const active = total - inactive;

                    document.getElementById('total-bs-count').textContent = total;
                    document.getElementById('active-bs-count').textContent = active;
                    document.getElementById('inactive-bs-count').textContent = inactive;
                });
        }

        // Функция обновления статистики по району
        function updateStatisticsByArea(areaId, stations) {
            if (!stations) {
                fetch(`/api/base-stations/area/${areaId}/`)
                    .then(response => response.json())
                    .then(stations => {
                        updateAreaStats(stations);
                    });
            } else {
                updateAreaStats(stations);
            }

            function updateAreaStats(stations) {
                const total = stations.length;
                const inactive = stations.filter(station => station.status === true).length;
                const active = total - inactive;

                document.getElementById('total-bs-count').textContent = total;
                document.getElementById('active-bs-count').textContent = active;
                document.getElementById('inactive-bs-count').textContent = inactive;
            }
        }

        points.forEach(function (point) {
            let pointColor;
            let statusText;

            if (point.status === true) {
                pointColor = 'red';
                statusText = 'БС не работает';
            } else {
                pointColor = 'green';
                statusText = 'БС активен';
            }

            const circleMarker = L.circleMarker([point.lat, point.lon], {
                radius: getCircleRadius(mymap.getZoom()),
                fillColor: pointColor,
                color: "#000",
                weight: 1,
                opacity: 1,
                fillOpacity: 0.8
            })
                .bindTooltip("<b>" + point.name + "</b><br>" + statusText, {
                    permanent: false,
                    direction: 'top',
                    offset: [0, -10]
                })
                .addTo(mymap);

            pointLayers.push(circleMarker);
        });

        // Обработчик изменения статуса
        document.getElementById('status').addEventListener('change', function () {
            const statusValue = this.value;

            if (statusValue === 'all') {
                // Показываем все точки
                pointLayers.forEach(marker => {
                    marker.addTo(mymap);
                });

                // Обновляем статистику
                const total = points.length;
                const inactive = points.filter(point => point.status === true).length;
                const active = total - inactive;

                document.getElementById('total-bs-count').textContent = total;
                document.getElementById('active-bs-count').textContent = active;
                document.getElementById('inactive-bs-count').textContent = inactive;
            } else if (statusValue === 'online') {
                // Показываем только онлайн точки
                pointLayers.forEach((marker, index) => {
                    if (points[index].status === false) {
                        marker.addTo(mymap);
                    } else {
                        mymap.removeLayer(marker);
                    }
                });

                // Обновляем статистику
                const active = points.filter(point => point.status === false).length;

                document.getElementById('total-bs-count').textContent = active;
                document.getElementById('active-bs-count').textContent = active;
                document.getElementById('inactive-bs-count').textContent = 0;
            } else if (statusValue === 'offline') {
                // Показываем только оффлайн точки
                pointLayers.forEach((marker, index) => {
                    if (points[index].status === true) {
                        marker.addTo(mymap);
                    } else {
                        mymap.removeLayer(marker);
                    }
                });

                // Обновляем статистику
                const inactive = points.filter(point => point.status === true).length;

                document.getElementById('total-bs-count').textContent = inactive;
                document.getElementById('active-bs-count').textContent = 0;
                document.getElementById('inactive-bs-count').textContent = inactive;
            }
        });

        // Обработчики кликов на кнопки переключения типа карты
        const mapButton = document.getElementById('map-button');
        const satelliteButton = document.getElementById('satellite-button');

        mapButton.addEventListener('click', function () {
            mymap.removeLayer(satelliteLayer);
            osmLayer.addTo(mymap);
            mapButton.classList.add('btn-primary');
            mapButton.classList.remove('btn-secondary');
            satelliteButton.classList.add('btn-secondary');
            satelliteButton.classList.remove('btn-primary');
        });

        satelliteButton.addEventListener('click', function () {
            mymap.removeLayer(osmLayer);
            satelliteLayer.addTo(mymap);
            satelliteButton.classList.add('btn-primary');
            satelliteButton.classList.remove('btn-secondary');
            mapButton.classList.add('btn-secondary');
            mapButton.classList.remove('btn-primary');
        });

        // Слушаем событие 'zoomend'
        mymap.on('zoomend', updateCircleSizes);

        // Ўзбекистон чегаралари
        const uzbekistanBounds = [
            [37.1, 55.9], // Жанубий-ғарбий нуқта
            [45.6, 73.1]  // Шимолий-шарқий нуқта
        ];
        // Картани Ўзбекистон билан чегаралаш
        mymap.setMaxBounds(uzbekistanBounds);
    </script>

</body>

{% endblock content %}